﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Oberon.BusinessLogic.Model
{
    public class PharmacyInfo
    {
            public int pageNumber { get; set; }
            public int pageSize { get; set; }
            public string searchName { get; set; }
            public string searchLocation { get; set; }
            public string searchZipCode { get; set; }
            public DateTime? startDateofVisit { get; set; }
            public DateTime? endDateofVisit { get; set; }
            public string sortBy { get; set; }
            public int searchusercount { get; set; }
    }

    public class PharmacyListResponse
    {
        public string Name { get; set; }
        public string Location { get; set; }
        public long ZipCode { get; set; }
        public DateTime dateofVisit { get; set; }
        public int UserCount { get; set; }
    }

    public class PharmacyDeatils
    {
        public int id { get; set; }
        public int pageNumber { get; set; }
        public int pageSize { get; set; }
        public string searchName { get; set; }
        public string searchLocation { get; set; }
        public string searchZipCode { get; set; }
        public DateTime? startDateofVisit { get; set; }
        public DateTime? endDateofVisit { get; set; }
        public string sortBy { get; set; }
    }
}
