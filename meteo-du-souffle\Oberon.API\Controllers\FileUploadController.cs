﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oberon.API.Handlers;
using Oberon.API.Helper;
using Oberon.BusinessLogic.BusinessHelper;
using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Model;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Net;
using System.Text.RegularExpressions;
using System.Threading.Tasks;

namespace Oberon.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FileUploadController : ControllerBase
    {
        private readonly UserDBContext _context;
        private readonly IAzureBlobConnectionHandler _azureBlobConnection;
        static string[] _extentions = new string[] { ".pdf" };
        static string[] _imageExtentions = new string[] { ".png" };
        public FileUploadController(UserDBContext context, IAzureBlobConnectionHandler azureBlobConnection)
        {
            _context = context;
            _azureBlobConnection = azureBlobConnection;
        }

        #region ProfileImage Upload
        [HttpPost]
        [Route("profileupload")]
        public async Task<IActionResult> ProfileImageUpload([FromForm] FileUploadInfo model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "profile-pic";
                //Database existing check code here
                var user = await _context.ProfileInfo.Where(u => u.UserId == model.UserId).FirstOrDefaultAsync();
                if (user != null)
                {
                    //Delete file from Azure function call
                    var response = await blobStorage.DeleteFileAsync(user.FileName, containerName);
                    if (response)
                    {
                        //Delete data from DB
                        _context.ProfileInfo.Remove(user);
                        _context.SaveChanges();
                    }
                }

                var profilePicGuidName = string.Empty;
                var filePath = string.Empty;
                profilePicGuidName = Guid.NewGuid().ToString() + model.FileName;

                string str = profilePicGuidName;
                str = Regex.Replace(str, @"\s", "");

                var noteStream = model.File.OpenReadStream();
                filePath = await blobStorage.UploadFileAsBlob(noteStream, str, containerName, model.File.ContentType);

                ProfileInfo profileInfo = new ProfileInfo()
                {
                    UserId = model.UserId,
                    EmailId = model.EmailId,
                    FileName = str,
                    FilePath = filePath,
                    CreatedDate = DateTime.Now,
                    UpdatedDate = DateTime.Now
                };
                _context.Add(profileInfo);
                _context.SaveChanges();

                responseData.Status = true;
                responseData.Message = ResponseMessageHelper.PROFILEUPLOADSUCCESS.GetDescription();
                responseData.Data = null;
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region PDF File Upload
        //[Authorize]
        [HttpPost]
        [Route("pdffileupload")]
        public async Task<IActionResult> PdfFileUpload([FromForm] PdfFileUploadInfo model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "pdf-file";
                var filename = model.File.FileName;
                string fileType = filename.Substring(filename.Length - 4);
                //Database existing check code here
                var filenameinfo = await _context.PdfFileUpload.Where(u => u.Id == model.PdfFileNameId).FirstOrDefaultAsync();
                if (_extentions.Contains(fileType))
                {
                    //Delete file from Azure function call                    
                    var response = await blobStorage.DeleteFileAsync(filenameinfo.TempUniqueFileName, containerName);
                    if (response)
                    {
                        //update data to DB
                        filenameinfo.FileName = "";
                        filenameinfo.UniqueFileName = "";
                        filenameinfo.FilePath = "";
                        _context.Update(filenameinfo);
                        _context.SaveChanges();
                    }
                    var profilePicGuidName = string.Empty;
                    var filePath = string.Empty;
                    profilePicGuidName = Guid.NewGuid().ToString() + model.FileName;
                    var noteStream = model.File.OpenReadStream();

                    string str = profilePicGuidName;
                    str = Regex.Replace(str, @"\s", "");

                    filePath = await blobStorage.UploadFileAsBlob(noteStream, str, containerName, model.File.ContentType);

                    filenameinfo.TempFileName = model.FileName;
                    filenameinfo.TempUniqueFileName = str;
                    filenameinfo.TempFilePath = filePath;
                    _context.Update(filenameinfo);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.FILEUPLOAD.GetDescription();
                    responseData.Data = null;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.FILEFORMATE.GetDescription();
                    responseData.Data = null;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region PDF File Save 
        //[Authorize]
        [HttpPost]
        [Route("pdffilesave")]
        public async Task<IActionResult> PdfFileSave(PdfFileSaveInfo model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                if (model.IsPatho)
                {
                    var pathoinfo = await _context.PdfFileUpload.Where(u => u.Id == 1 && u.TempFileName != "").FirstOrDefaultAsync();
                    if (pathoinfo != null)
                    {
                        pathoinfo.UpdatedDate = DateTime.Now;
                        pathoinfo.FileName = pathoinfo.TempFileName;
                        pathoinfo.UniqueFileName = pathoinfo.TempUniqueFileName;
                        pathoinfo.FilePath = pathoinfo.TempFilePath;
                        _context.Update(pathoinfo);
                        _context.SaveChanges();
                    }

                }
                if (model.IsAllergies)
                {
                    var alleinfo = await _context.PdfFileUpload.Where(u => u.Id == 2 && u.TempFileName != "").FirstOrDefaultAsync();
                    if (alleinfo != null)
                    {
                        alleinfo.UpdatedDate = DateTime.Now;
                        alleinfo.FileName = alleinfo.TempFileName;
                        alleinfo.UniqueFileName = alleinfo.TempUniqueFileName;
                        alleinfo.FilePath = alleinfo.TempFilePath;
                        _context.Update(alleinfo);
                        _context.SaveChanges();
                    }
                }
                if (model.IsInfor)
                {
                    var infosinfo = await _context.PdfFileUpload.Where(u => u.Id == 3 && u.TempFileName != "").FirstOrDefaultAsync();
                    if (infosinfo != null)
                    {
                        infosinfo.UpdatedDate = DateTime.Now;
                        infosinfo.FileName = infosinfo.TempFileName;
                        infosinfo.UniqueFileName = infosinfo.TempUniqueFileName;
                        infosinfo.FilePath = infosinfo.TempFilePath;
                        _context.Update(infosinfo);
                        _context.SaveChanges();
                    }
                }
                if (model.IsTermAndCon)
                {
                    var termsinfo = await _context.PdfFileUpload.Where(u => u.Id == 4 && u.TempFileName != "").FirstOrDefaultAsync();
                    if (termsinfo != null)
                    {
                        termsinfo.UpdatedDate = DateTime.Now;
                        termsinfo.FileName = termsinfo.TempFileName;
                        termsinfo.UniqueFileName = termsinfo.TempUniqueFileName;
                        termsinfo.FilePath = termsinfo.TempFilePath;
                        _context.Update(termsinfo);
                        _context.SaveChanges();
                    }
                }
                if (model.IsConseilsInformation)
                {
                    var termsinfo = await _context.PdfFileUpload.Where(u => u.Id == 5 && u.TempFileName != "").FirstOrDefaultAsync();
                    if (termsinfo != null)
                    {
                        termsinfo.UpdatedDate = DateTime.Now;
                        termsinfo.FileName = termsinfo.TempFileName;
                        termsinfo.UniqueFileName = termsinfo.TempUniqueFileName;
                        termsinfo.FilePath = termsinfo.TempFilePath;
                        _context.Update(termsinfo);
                        _context.SaveChanges();
                    }
                }
                if (model.IsPolitiqueDeConfidentialite)
                {
                    var termsinfo = await _context.PdfFileUpload.Where(u => u.Id == 6 && u.TempFileName != "").FirstOrDefaultAsync();
                    if (termsinfo != null)
                    {
                        termsinfo.UpdatedDate = DateTime.Now;
                        termsinfo.FileName = termsinfo.TempFileName;
                        termsinfo.UniqueFileName = termsinfo.TempUniqueFileName;
                        termsinfo.FilePath = termsinfo.TempFilePath;
                        _context.Update(termsinfo);
                        _context.SaveChanges();
                    }
                }
                List<PdfFileInfo> PdfFileUploadList = new List<PdfFileInfo>();

                var response = _context.PdfFileUpload.ToList();
                foreach (var item in response)
                {
                    PdfFileInfo pdffileInfo = new PdfFileInfo();
                    pdffileInfo.Id = item.Id;
                    pdffileInfo.PdfFileName = item.PdfFileName;
                    pdffileInfo.FileName = item.FileName;
                    pdffileInfo.UniqueFileName = item.UniqueFileName;
                    pdffileInfo.FilePath = item.FilePath;
                    PdfFileUploadList.Add(pdffileInfo);
                }

                responseData.Status = true;
                responseData.Message = ResponseMessageHelper.FILESAVED.GetDescription();
                responseData.Data = PdfFileUploadList;
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region PDF File Details 
        ////[Authorize]
        [HttpGet]
        [Route("pdffiledeatils")]
        public async Task<IActionResult> GetPdfFileDetails()
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {

                //var response = await _context.PdfFileUpload.ToListAsync();
                var response = await _context.PdfFileUpload.OrderBy(i => i.Id).ToListAsync();
                List<PdfFileInfo> PdfFileUploadList = new List<PdfFileInfo>();

                foreach (var item in response)
                {
                    PdfFileInfo pdffileInfo = new PdfFileInfo();
                    pdffileInfo.Id = item.Id;
                    pdffileInfo.PdfFileName = item.PdfFileName;
                    pdffileInfo.FileName = item.FileName;
                    pdffileInfo.UniqueFileName = item.UniqueFileName;
                    pdffileInfo.FilePath = item.FilePath;
                    PdfFileUploadList.Add(pdffileInfo);
                }

                responseData.Status = true;
                responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                responseData.Data = PdfFileUploadList;
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Delete Pdf
        //[Authorize]
        [HttpDelete]
        [Route("pdffiledelete")]
        public async Task<IActionResult> PdfFiledelete(string UniqueFileName, int Id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "pdf-file";
                //Database existing check code here
                var filenameinfo = await _context.PdfFileUpload.Where(u => u.UniqueFileName == UniqueFileName && u.Id == Id).FirstOrDefaultAsync();
                if (filenameinfo.UniqueFileName != "")
                {
                    //Delete file from Azure function call                    
                    var response = await blobStorage.DeleteFileAsync(filenameinfo.UniqueFileName, containerName);
                    if (response)
                    {
                        //update data to DB
                        filenameinfo.FileName = "";
                        filenameinfo.UniqueFileName = "";
                        filenameinfo.FilePath = "";
                        filenameinfo.TempFileName = "";
                        filenameinfo.TempUniqueFileName = "";
                        filenameinfo.TempFilePath = "";
                        _context.Update(filenameinfo);
                        _context.SaveChanges();
                    }
                }
                responseData.Status = true;
                responseData.Message = ResponseMessageHelper.DELETE.GetDescription();
                responseData.Data = null;
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Splash Upload
        //[Authorize]
        [HttpPost]
        [Route("splashScreenUpload")]
        public async Task<IActionResult> SplashUpload([FromForm] SplashImageRequest model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "pdf-file";

                //var filename = model.File.FileName;
                //filename.Substring(filename.Length - 4);

                string fileType = Path.GetExtension(model.File.FileName);

                //Database existing check code here
                var filenameinfo = await _context.SplashImage.Where(s => s.Id == model.Id).FirstOrDefaultAsync();

                //if (_imageExtentions.Contains(fileType))
                if (fileType == ".jpg" || fileType == ".png" || fileType == ".jpeg" || fileType == ".svg")
                {
                    var profilePicGuidName = string.Empty;
                    var filePath = string.Empty;
                    profilePicGuidName = Guid.NewGuid().ToString() + model.FileName;
                    var noteStream = model.File.OpenReadStream();

                    string str = profilePicGuidName;
                    str = Regex.Replace(str, @"\s", "");

                    filePath = await blobStorage.UploadFileAsBlob(noteStream, str, containerName, model.File.ContentType);

                    filenameinfo.FileName = model.FileName;
                    filenameinfo.UniqueFileName = str;
                    filenameinfo.FilePath = filePath;
                    _context.Update(filenameinfo);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.FILEUPLOAD.GetDescription();
                    responseData.Data = null;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.FILEFORMATE.GetDescription();
                    responseData.Data = null;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region splashScreenImages 
        ////[Authorize]
        [HttpGet]
        [Route("splashScreenImages")]
        public async Task<IActionResult> SplashScreenImages()
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {

                //var response = await _context.PdfFileUpload.ToListAsync();
                var response = await _context.SplashImage.OrderBy(i => i.Id).ToListAsync();
                List<SplashImageInfo> splashImagesList = new List<SplashImageInfo>();

                foreach (var item in response)
                {
                    SplashImageInfo splashImageInfo = new SplashImageInfo();
                    splashImageInfo.Id = item.Id;
                    splashImageInfo.FilePath = item.FilePath;
                    splashImagesList.Add(splashImageInfo);
                }

                responseData.Status = true;
                responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                responseData.Data = splashImagesList;
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion
    }
}
