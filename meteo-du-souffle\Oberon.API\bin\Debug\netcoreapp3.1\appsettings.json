{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft": "Warning",
      "Microsoft.Hosting.Lifetime": "Information"
    }
  },
  "AllowedHosts": "*",
  "ConnectionStrings": {
    ////DEV:
    //
    //"DataBaseConnection": "Server=oberondevdb.postgres.database.azure.com;Database=Oberon_dev;Port=5432;User Id=oberondev;Password=***********;Ssl Mode=Require;"
    ////PROD:
    //
    "DataBaseConnection": "Server=oberonproddb.postgres.database.azure.com;Database=Oberon_prod;Port=5432;User Id=oberonprod;Password=***********;Ssl Mode=Require;"
  },
  "JWTSettings": {
    "SecretKey": "%OberonSciences.Website^"
  },
  ////DEV:
  //
  //"AzureConnection": {
  //  "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=oberondev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
  //},
  //"CorsSettings": {
  //  "AllowedOrigins": [
  //    "https://oberondev.azurewebsites.net",
  //    "http://oberondev.azurewebsites.net",
  //    "https://localhost:4200",
  //    "http://localhost:4200"
  //  ]
  //},
  ////PROD:
  //
  "AzureConnection": {
    "ConnectionString": "DefaultEndpointsProtocol=https;AccountName=oberonprod;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net"
  },
  //PROD:
  
  "CorsSettings": {
    "AllowedOrigins": [
      "https://oberonprod.azurewebsites.net",
      "http://oberonprod.azurewebsites.net",
      "https://localhost:4200",
      "http://localhost:4200"
    ]
  },
  "FcmNotification": {
    "SenderId": "************",
    "ServerKey": "AAAAyzgpJs0:APA91bGJZfYzES7pnsIT6buhJ6gqBxErLUSsDtN5LtYWdZpKL9zLGdXSBbVZpPHfKJXa1X4AH07s_PUnczthv0BGwx-EdhKHWVit71CnU6GIOKWh53cAsGoJgZl-5CyjF3UsenVB3mF4"
  }
}
