{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {"defines": ["TRACE", "RELEASE", "NETCOREAPP", "NETCOREAPP3_1", "NETCOREAPP1_0_OR_GREATER", "NETCOREAPP1_1_OR_GREATER", "NETCOREAPP2_0_OR_GREATER", "NETCOREAPP2_1_OR_GREATER", "NETCOREAPP2_2_OR_GREATER", "NETCOREAPP3_0_OR_GREATER", "NETCOREAPP3_1_OR_GREATER"], "languageVersion": "8.0", "platform": "", "allowUnsafe": false, "warningsAsErrors": false, "optimize": true, "keyFile": "", "emitEntryPoint": true, "xmlDoc": false, "debugType": "portable"}, "targets": {".NETCoreApp,Version=v3.1": {"Oberon.API/1.0.0": {"dependencies": {"AspNetCore.Firebase.Authentication": "2.0.1", "Azure.Storage.Blobs": "12.19.1", "CorePush": "3.0.10", "FireSharp": "2.0.4", "Firebase.Auth": "1.0.0", "FirebaseAdmin": "3.1.0", "Microsoft.AspNetCore.Authentication.JwtBearer": "3.1.32", "Microsoft.EntityFrameworkCore.Design": "5.0.16", "Microsoft.EntityFrameworkCore.Tools": "5.0.16", "Microsoft.NETCore.App": "2.2.8", "Npgsql.EntityFrameworkCore.PostgreSQL": "5.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL.Design": "1.1.0", "Oberon.BusinessLogic": "1.0.0", "Swashbuckle.AspNetCore": "6.3.1", "Swashbuckle.AspNetCore.Swagger": "6.3.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.3.1", "WindowsAzure.Storage": "9.3.3", "Microsoft.AspNetCore.Antiforgery": "*******", "Microsoft.AspNetCore.Authentication.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Authentication.Cookies": "*******", "Microsoft.AspNetCore.Authentication.Core.Reference": "*******", "Microsoft.AspNetCore.Authentication.Reference": "*******", "Microsoft.AspNetCore.Authentication.OAuth.Reference": "*******", "Microsoft.AspNetCore.Authorization": "*******", "Microsoft.AspNetCore.Authorization.Policy": "*******", "Microsoft.AspNetCore.Components.Authorization": "*******", "Microsoft.AspNetCore.Components": "*******", "Microsoft.AspNetCore.Components.Forms": "*******", "Microsoft.AspNetCore.Components.Server": "*******", "Microsoft.AspNetCore.Components.Web": "*******", "Microsoft.AspNetCore.Connections.Abstractions": "*******", "Microsoft.AspNetCore.CookiePolicy": "*******", "Microsoft.AspNetCore.Cors": "*******", "Microsoft.AspNetCore.Cryptography.Internal.Reference": "*******", "Microsoft.AspNetCore.Cryptography.KeyDerivation": "*******", "Microsoft.AspNetCore.DataProtection.Abstractions.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Reference": "*******", "Microsoft.AspNetCore.DataProtection.Extensions": "*******", "Microsoft.AspNetCore.Diagnostics.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Diagnostics.Reference": "*******", "Microsoft.AspNetCore.Diagnostics.HealthChecks": "*******", "Microsoft.AspNetCore.Reference": "*******", "Microsoft.AspNetCore.HostFiltering": "*******", "Microsoft.AspNetCore.Hosting.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Hosting.Reference": "*******", "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Html.Abstractions": "*******", "Microsoft.AspNetCore.Http.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Http.Connections.Common": "*******", "Microsoft.AspNetCore.Http.Connections": "*******", "Microsoft.AspNetCore.Http.Reference": "*******", "Microsoft.AspNetCore.Http.Extensions.Reference": "*******", "Microsoft.AspNetCore.Http.Features.Reference": "*******", "Microsoft.AspNetCore.HttpOverrides.Reference": "*******", "Microsoft.AspNetCore.HttpsPolicy": "*******", "Microsoft.AspNetCore.Identity": "*******", "Microsoft.AspNetCore.Localization": "*******", "Microsoft.AspNetCore.Localization.Routing": "*******", "Microsoft.AspNetCore.Metadata": "*******", "Microsoft.AspNetCore.Mvc.Abstractions": "*******", "Microsoft.AspNetCore.Mvc.ApiExplorer": "*******", "Microsoft.AspNetCore.Mvc.Core": "*******", "Microsoft.AspNetCore.Mvc.Cors": "*******", "Microsoft.AspNetCore.Mvc.DataAnnotations": "*******", "Microsoft.AspNetCore.Mvc": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Json": "*******", "Microsoft.AspNetCore.Mvc.Formatters.Xml": "*******", "Microsoft.AspNetCore.Mvc.Localization": "*******", "Microsoft.AspNetCore.Mvc.Razor": "*******", "Microsoft.AspNetCore.Mvc.RazorPages": "*******", "Microsoft.AspNetCore.Mvc.TagHelpers": "*******", "Microsoft.AspNetCore.Mvc.ViewFeatures": "*******", "Microsoft.AspNetCore.Razor": "*******", "Microsoft.AspNetCore.Razor.Runtime": "*******", "Microsoft.AspNetCore.ResponseCaching.Abstractions": "*******", "Microsoft.AspNetCore.ResponseCaching": "*******", "Microsoft.AspNetCore.ResponseCompression": "*******", "Microsoft.AspNetCore.Rewrite": "*******", "Microsoft.AspNetCore.Routing.Abstractions.Reference": "*******", "Microsoft.AspNetCore.Routing.Reference": "*******", "Microsoft.AspNetCore.Server.HttpSys": "*******", "Microsoft.AspNetCore.Server.IIS": "*******", "Microsoft.AspNetCore.Server.IISIntegration.Reference": "*******", "Microsoft.AspNetCore.Server.Kestrel.Core.Reference": "*******", "Microsoft.AspNetCore.Server.Kestrel.Reference": "*******", "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets": "*******", "Microsoft.AspNetCore.Session": "*******", "Microsoft.AspNetCore.SignalR.Common": "*******", "Microsoft.AspNetCore.SignalR.Core": "*******", "Microsoft.AspNetCore.SignalR": "*******", "Microsoft.AspNetCore.SignalR.Protocols.Json": "*******", "Microsoft.AspNetCore.StaticFiles": "*******", "Microsoft.AspNetCore.WebSockets": "*******", "Microsoft.AspNetCore.WebUtilities.Reference": "*******", "Microsoft.CSharp.Reference": "*******", "Microsoft.Extensions.Configuration.Binder.Reference": "*******", "Microsoft.Extensions.Configuration.CommandLine.Reference": "*******", "Microsoft.Extensions.Configuration.Reference": "*******", "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference": "*******", "Microsoft.Extensions.Configuration.FileExtensions.Reference": "*******", "Microsoft.Extensions.Configuration.Ini": "*******", "Microsoft.Extensions.Configuration.Json.Reference": "*******", "Microsoft.Extensions.Configuration.KeyPerFile": "*******", "Microsoft.Extensions.Configuration.UserSecrets.Reference": "*******", "Microsoft.Extensions.Configuration.Xml": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "*******", "Microsoft.Extensions.Diagnostics.HealthChecks": "*******", "Microsoft.Extensions.FileProviders.Abstractions.Reference": "*******", "Microsoft.Extensions.FileProviders.Composite": "*******", "Microsoft.Extensions.FileProviders.Embedded": "*******", "Microsoft.Extensions.FileProviders.Physical.Reference": "*******", "Microsoft.Extensions.FileSystemGlobbing.Reference": "*******", "Microsoft.Extensions.Hosting.Abstractions.Reference": "*******", "Microsoft.Extensions.Hosting": "*******", "Microsoft.Extensions.Http": "*******", "Microsoft.Extensions.Identity.Core": "*******", "Microsoft.Extensions.Identity.Stores": "*******", "Microsoft.Extensions.Localization.Abstractions": "*******", "Microsoft.Extensions.Localization": "*******", "Microsoft.Extensions.Logging.Configuration.Reference": "*******", "Microsoft.Extensions.Logging.Console.Reference": "*******", "Microsoft.Extensions.Logging.Debug.Reference": "*******", "Microsoft.Extensions.Logging.EventLog": "*******", "Microsoft.Extensions.Logging.EventSource": "*******", "Microsoft.Extensions.Logging.TraceSource": "*******", "Microsoft.Extensions.ObjectPool.Reference": "*******", "Microsoft.Extensions.Options.ConfigurationExtensions.Reference": "*******", "Microsoft.Extensions.Options.DataAnnotations": "*******", "Microsoft.Extensions.WebEncoders.Reference": "*******", "Microsoft.JSInterop": "*******", "Microsoft.Net.Http.Headers.Reference": "*******", "Microsoft.VisualBasic.Core": "********", "Microsoft.VisualBasic": "10.0.0.0", "Microsoft.Win32.Primitives": "*******", "Microsoft.Win32.Registry.Reference": "*******", "mscorlib": "*******", "netstandard": "2.1.0.0", "System.AppContext": "*******", "System.Buffers.Reference": "*******", "System.Collections.Concurrent": "4.0.15.0", "System.Collections": "*******", "System.Collections.NonGeneric": "*******", "System.Collections.Specialized": "*******", "System.ComponentModel.DataAnnotations": "*******", "System.ComponentModel": "*******", "System.ComponentModel.EventBasedAsync": "*******", "System.ComponentModel.Primitives": "*******", "System.ComponentModel.TypeConverter": "*******", "System.Configuration": "*******", "System.Console": "*******", "System.Core": "*******", "System.Data.Common": "*******", "System.Data.DataSetExtensions": "*******", "System.Data": "*******", "System.Diagnostics.Contracts": "*******", "System.Diagnostics.Debug": "*******", "System.Diagnostics.EventLog": "*******", "System.Diagnostics.FileVersionInfo": "*******", "System.Diagnostics.Process": "*******", "System.Diagnostics.StackTrace": "*******", "System.Diagnostics.TextWriterTraceListener": "*******", "System.Diagnostics.Tools": "*******", "System.Diagnostics.TraceSource": "*******", "System.Diagnostics.Tracing": "*******", "System": "*******", "System.Drawing": "*******", "System.Drawing.Primitives": "*******", "System.Dynamic.Runtime": "*******", "System.Globalization.Calendars": "*******", "System.Globalization": "*******", "System.Globalization.Extensions": "*******", "System.IO.Compression.Brotli": "*******", "System.IO.Compression": "*******", "System.IO.Compression.FileSystem": "*******", "System.IO.Compression.ZipFile": "*******", "System.IO": "*******", "System.IO.FileSystem": "*******", "System.IO.FileSystem.DriveInfo": "*******", "System.IO.FileSystem.Primitives": "*******", "System.IO.FileSystem.Watcher": "*******", "System.IO.IsolatedStorage": "*******", "System.IO.MemoryMappedFiles": "*******", "System.IO.Pipelines": "*******", "System.IO.Pipes": "*******", "System.IO.UnmanagedMemoryStream": "*******", "System.Linq": "*******", "System.Linq.Expressions": "*******", "System.Linq.Parallel": "*******", "System.Linq.Queryable": "*******", "System.Memory.Reference": "*******", "System.Net": "*******", "System.Net.Http": "*******", "System.Net.HttpListener": "*******", "System.Net.Mail": "*******", "System.Net.NameResolution": "*******", "System.Net.NetworkInformation": "*******", "System.Net.Ping": "*******", "System.Net.Primitives": "*******", "System.Net.Requests": "*******", "System.Net.Security": "*******", "System.Net.ServicePoint": "*******", "System.Net.Sockets": "*******", "System.Net.WebClient": "*******", "System.Net.WebHeaderCollection": "*******", "System.Net.WebProxy": "*******", "System.Net.WebSockets.Client": "*******", "System.Net.WebSockets": "*******", "System.Numerics": "*******", "System.Numerics.Vectors.Reference": "*******", "System.ObjectModel": "*******", "System.Reflection.DispatchProxy": "*******", "System.Reflection": "*******", "System.Reflection.Emit": "*******", "System.Reflection.Emit.ILGeneration": "*******", "System.Reflection.Emit.Lightweight": "*******", "System.Reflection.Extensions": "*******", "System.Reflection.Metadata.Reference": "1.4.5.0", "System.Reflection.Primitives": "*******", "System.Reflection.TypeExtensions": "*******", "System.Resources.Reader": "*******", "System.Resources.ResourceManager": "*******", "System.Resources.Writer": "*******", "System.Runtime.CompilerServices.VisualC": "*******", "System.Runtime": "*******", "System.Runtime.Extensions": "*******", "System.Runtime.Handles": "*******", "System.Runtime.InteropServices": "*******", "System.Runtime.InteropServices.RuntimeInformation": "*******", "System.Runtime.InteropServices.WindowsRuntime": "*******", "System.Runtime.Intrinsics": "*******", "System.Runtime.Loader": "*******", "System.Runtime.Numerics": "*******", "System.Runtime.Serialization": "*******", "System.Runtime.Serialization.Formatters": "*******", "System.Runtime.Serialization.Json": "*******", "System.Runtime.Serialization.Primitives": "*******", "System.Runtime.Serialization.Xml": "4.1.5.0", "System.Security.AccessControl.Reference": "*******", "System.Security.Claims": "*******", "System.Security.Cryptography.Algorithms": "*******", "System.Security.Cryptography.Cng.Reference": "*******", "System.Security.Cryptography.Csp": "*******", "System.Security.Cryptography.Encoding": "*******", "System.Security.Cryptography.Primitives": "*******", "System.Security.Cryptography.X509Certificates": "*******", "System.Security.Cryptography.Xml.Reference": "*******", "System.Security": "*******", "System.Security.Permissions": "*******", "System.Security.Principal": "*******", "System.Security.Principal.Windows.Reference": "*******", "System.Security.SecureString": "*******", "System.ServiceModel.Web": "*******", "System.ServiceProcess": "*******", "System.Text.Encoding.CodePages": "*******", "System.Text.Encoding": "*******", "System.Text.Encoding.Extensions": "*******", "System.Text.RegularExpressions": "*******", "System.Threading.Channels": "*******", "System.Threading": "*******", "System.Threading.Overlapped": "*******", "System.Threading.Tasks.Dataflow": "*******", "System.Threading.Tasks": "*******", "System.Threading.Tasks.Extensions.Reference": "*******", "System.Threading.Tasks.Parallel": "*******", "System.Threading.Thread": "*******", "System.Threading.ThreadPool": "*******", "System.Threading.Timer": "*******", "System.Transactions": "*******", "System.Transactions.Local": "*******", "System.ValueTuple": "*******", "System.Web": "*******", "System.Web.HttpUtility": "*******", "System.Windows": "*******", "System.Windows.Extensions": "*******", "System.Xml": "*******", "System.Xml.Linq": "*******", "System.Xml.ReaderWriter": "*******", "System.Xml.Serialization": "*******", "System.Xml.XDocument": "*******", "System.Xml.XmlDocument": "*******", "System.Xml.XmlSerializer": "*******", "System.Xml.XPath": "*******", "System.Xml.XPath.XDocument": "*******", "WindowsBase": "*******"}, "runtime": {"Oberon.API.dll": {}}, "compile": {"Oberon.API.dll": {}}}, "AspNetCore.Firebase.Authentication/2.0.1": {"dependencies": {"Microsoft.AspNetCore": "2.0.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "3.1.32", "Microsoft.AspNetCore.Authentication.OAuth": "2.0.3"}, "runtime": {"lib/netstandard2.0/AspNetCore.Firebase.Authentication.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}, "compile": {"lib/netstandard2.0/AspNetCore.Firebase.Authentication.dll": {}}}, "Azure.Core/1.36.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}, "runtime": {"lib/netcoreapp2.1/Azure.Core.dll": {"assemblyVersion": "1.36.0.0", "fileVersion": "1.3600.23.56006"}}, "compile": {"lib/netcoreapp2.1/Azure.Core.dll": {}}}, "Azure.Storage.Blobs/12.19.1": {"dependencies": {"Azure.Storage.Common": "12.18.1", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.1/Azure.Storage.Blobs.dll": {"assemblyVersion": "12.19.1.0", "fileVersion": "12.1900.123.56305"}}, "compile": {"lib/netstandard2.1/Azure.Storage.Blobs.dll": {}}}, "Azure.Storage.Common/12.18.1": {"dependencies": {"Azure.Core": "1.36.0", "System.IO.Hashing": "6.0.0"}, "runtime": {"lib/netstandard2.0/Azure.Storage.Common.dll": {"assemblyVersion": "12.18.1.0", "fileVersion": "12.1800.123.56305"}}, "compile": {"lib/netstandard2.0/Azure.Storage.Common.dll": {}}}, "CorePush/3.0.10": {"dependencies": {"Newtonsoft.Json": "13.0.3", "Portable.BouncyCastle": "1.8.9"}, "runtime": {"lib/netstandard2.0/CorePush.dll": {"assemblyVersion": "3.0.10.0", "fileVersion": "3.0.10.0"}}, "compile": {"lib/netstandard2.0/CorePush.dll": {}}}, "Firebase.Auth/1.0.0": {"dependencies": {"Microsoft.NETCore.Portable.Compatibility": "1.0.1", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard1.1/Firebase.Auth.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}, "compile": {"lib/netstandard1.1/Firebase.Auth.dll": {}}}, "FirebaseAdmin/3.1.0": {"dependencies": {"Google.Api.Gax.Rest": "4.8.0", "Google.Apis.Auth": "1.68.0", "System.Collections.Immutable": "8.0.0"}, "runtime": {"lib/netstandard2.0/FirebaseAdmin.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/FirebaseAdmin.dll": {}}}, "FireSharp/2.0.4": {"dependencies": {"Microsoft.Bcl.Async": "1.0.168", "Microsoft.Net.Http": "2.2.28", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/portable-net45+sl5+wp8+win8/FireSharp.dll": {"assemblyVersion": "2.0.3.0", "fileVersion": "2.0.3.0"}}, "compile": {"lib/portable-net45+sl5+wp8+win8/FireSharp.dll": {}}}, "Google.Api.Gax/4.8.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}, "compile": {"lib/netstandard2.0/Google.Api.Gax.dll": {}}}, "Google.Api.Gax.Rest/4.8.0": {"dependencies": {"Google.Api.Gax": "4.8.0", "Google.Apis.Auth": "1.68.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"assemblyVersion": "4.8.0.0", "fileVersion": "4.8.0.0"}}, "compile": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {}}}, "Google.Apis/1.68.0": {"dependencies": {"Google.Apis.Core": "1.68.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}, "compile": {"lib/netstandard2.0/Google.Apis.dll": {}}}, "Google.Apis.Auth/1.68.0": {"dependencies": {"Google.Apis": "1.68.0", "Google.Apis.Core": "1.68.0", "System.Management": "7.0.2"}, "runtime": {"lib/netstandard2.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}, "compile": {"lib/netstandard2.0/Google.Apis.Auth.dll": {}}}, "Google.Apis.Core/1.68.0": {"dependencies": {"Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Google.Apis.Core.dll": {"assemblyVersion": "1.68.0.0", "fileVersion": "1.68.0.0"}}, "compile": {"lib/netstandard2.0/Google.Apis.Core.dll": {}}}, "Humanizer.Core/2.8.26": {}, "Libuv/1.10.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4"}, "runtimeTargets": {"runtimes/linux-arm/native/libuv.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libuv.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libuv.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libuv.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libuv.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/libuv.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libuv.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libuv.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.AspNetCore/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Diagnostics": "2.0.2", "Microsoft.AspNetCore.Hosting": "2.0.2", "Microsoft.AspNetCore.Routing": "2.0.2", "Microsoft.AspNetCore.Server.IISIntegration": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.0.2", "Microsoft.Extensions.Configuration.CommandLine": "2.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.0.1", "Microsoft.Extensions.Configuration.Json": "2.0.1", "Microsoft.Extensions.Configuration.UserSecrets": "2.0.1", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.Configuration": "2.0.1", "Microsoft.Extensions.Logging.Console": "2.0.1", "Microsoft.Extensions.Logging.Debug": "2.0.1"}}, "Microsoft.AspNetCore.Authentication/2.0.3": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.0.2", "Microsoft.AspNetCore.DataProtection": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.WebEncoders": "2.0.1"}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2"}}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.1.32": {"dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "5.5.0"}, "runtime": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "3.1.32.0", "fileVersion": "3.100.3222.56610"}}, "compile": {"lib/netcoreapp3.1/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {}}}, "Microsoft.AspNetCore.Authentication.OAuth/2.0.3": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.0.3", "Newtonsoft.Json": "13.0.3"}}, "Microsoft.AspNetCore.Cryptography.Internal/2.0.2": {}, "Microsoft.AspNetCore.DataProtection/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.0.2", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.0.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Win32.Registry": "4.4.0", "System.Security.Cryptography.Xml": "4.4.0"}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.0.2": {}, "Microsoft.AspNetCore.Diagnostics/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.Abstractions": "2.0.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.FileProviders.Physical": "2.0.1", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Reflection.Metadata": "1.5.0"}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.0.2": {}, "Microsoft.AspNetCore.Hosting/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.Extensions.Configuration": "2.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.0.1", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.FileProviders.Physical": "2.0.1", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Reflection.Metadata": "1.5.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.0.1", "Microsoft.Extensions.Hosting.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.2", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Http/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.ObjectPool": "2.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.0.2"}}, "Microsoft.AspNetCore.Http.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.2", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.AspNetCore.Http.Extensions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "2.0.1", "Microsoft.Net.Http.Headers": "2.0.2", "System.Buffers": "4.5.1"}}, "Microsoft.AspNetCore.Http.Features/2.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.HttpOverrides/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Routing/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.AspNetCore.Routing.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.ObjectPool": "2.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2"}}, "Microsoft.AspNetCore.Server.IISIntegration/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.0.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.AspNetCore.HttpOverrides": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Security.Principal.Windows": "4.4.0"}}, "Microsoft.AspNetCore.Server.Kestrel/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv": "2.0.2"}}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.0.2", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.0.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.2.18051"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.2", "System.Buffers": "4.5.1", "System.Numerics.Vectors": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.2.18051"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv/2.0.2": {"dependencies": {"Libuv": "1.10.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.2.18051"}}, "compile": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll": {}}}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"dependencies": {"Microsoft.Net.Http.Headers": "2.0.2", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.Bcl/1.1.9": {"dependencies": {"Microsoft.Bcl.Build": "1.0.14"}}, "Microsoft.Bcl.Async/1.0.168": {"dependencies": {"Microsoft.Bcl": "1.1.9"}, "runtime": {"lib/net40/Microsoft.Threading.Tasks.Extensions.Desktop.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net40/Microsoft.Threading.Tasks.Extensions.dll": {"assemblyVersion": "********", "fileVersion": "*********"}, "lib/net40/Microsoft.Threading.Tasks.dll": {"assemblyVersion": "********", "fileVersion": "*********"}}, "compile": {"lib/net40/Microsoft.Threading.Tasks.Extensions.Desktop.dll": {}, "lib/net40/Microsoft.Threading.Tasks.Extensions.dll": {}, "lib/net40/Microsoft.Threading.Tasks.dll": {}}}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {}}}, "Microsoft.Bcl.Build/1.0.14": {}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/5.0.16": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.16", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.16", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "8.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "5.0.16.0", "fileVersion": "5.0.1622.16102"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.16": {"runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "5.0.16.0", "fileVersion": "5.0.1622.16102"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.16": {}, "Microsoft.EntityFrameworkCore.Design/5.0.16": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "5.0.16"}}, "Microsoft.EntityFrameworkCore.Relational/5.0.16": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.16", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "5.0.16.0", "fileVersion": "5.0.1622.16102"}}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.EntityFrameworkCore.Relational.Design/1.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "5.0.16", "NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/Microsoft.EntityFrameworkCore.Relational.Design.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.21115"}}, "compile": {"lib/netstandard1.3/Microsoft.EntityFrameworkCore.Relational.Design.dll": {}}}, "Microsoft.EntityFrameworkCore.Tools/5.0.16": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "5.0.16"}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.Configuration.Binder/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1"}}, "Microsoft.Extensions.Configuration.CommandLine/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1"}}, "Microsoft.Extensions.Configuration.FileExtensions/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1", "Microsoft.Extensions.FileProviders.Physical": "2.0.1"}}, "Microsoft.Extensions.Configuration.Json/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.0.1", "Newtonsoft.Json": "13.0.3"}}, "Microsoft.Extensions.Configuration.UserSecrets/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.0.1"}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "5.0.0.1", "fileVersion": "5.0.821.31504"}}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/2.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.0.1", "Microsoft.Extensions.FileSystemGlobbing": "2.0.1"}}, "Microsoft.Extensions.FileSystemGlobbing/2.0.1": {}, "Microsoft.Extensions.Hosting.Abstractions/2.0.2": {}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging.Configuration/2.0.1": {"dependencies": {"Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.1"}}, "Microsoft.Extensions.Logging.Console/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0"}}, "Microsoft.Extensions.Logging.Debug/2.0.1": {"dependencies": {"Microsoft.Extensions.Logging": "5.0.0"}}, "Microsoft.Extensions.ObjectPool/2.0.0": {}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.Extensions.Primitives/5.0.0": {"runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.Extensions.WebEncoders/2.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Text.Encodings.Web": "4.7.2"}}, "Microsoft.IdentityModel.JsonWebTokens/5.5.0": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.5.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {"assemblyVersion": "5.5.0.0", "fileVersion": "5.5.0.60624"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.JsonWebTokens.dll": {}}}, "Microsoft.IdentityModel.Logging/5.5.0": {"runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "5.5.0.0", "fileVersion": "5.5.0.60624"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Logging.dll": {}}}, "Microsoft.IdentityModel.Protocols/5.5.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.5.0", "Microsoft.IdentityModel.Tokens": "5.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "5.5.0.0", "fileVersion": "5.5.0.60624"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.dll": {}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.5.0": {"dependencies": {"Microsoft.IdentityModel.Protocols": "5.5.0", "Newtonsoft.Json": "13.0.3", "System.IdentityModel.Tokens.Jwt": "5.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "5.5.0.0", "fileVersion": "5.5.0.60624"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {}}}, "Microsoft.IdentityModel.Tokens/5.5.0": {"dependencies": {"Microsoft.IdentityModel.Logging": "5.5.0", "Newtonsoft.Json": "13.0.3", "System.Security.Cryptography.Cng": "4.5.0"}, "runtime": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "5.5.0.0", "fileVersion": "5.5.0.60624"}}, "compile": {"lib/netstandard2.0/Microsoft.IdentityModel.Tokens.dll": {}}}, "Microsoft.Net.Http/2.2.28": {"dependencies": {"Microsoft.Bcl": "1.1.9", "Microsoft.Bcl.Build": "1.0.14"}, "runtime": {"lib/net45/System.Net.Http.Extensions.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net45/System.Net.Http.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/net45/System.Net.Http.Extensions.dll": {}, "lib/net45/System.Net.Http.Primitives.dll": {}}}, "Microsoft.Net.Http.Headers/2.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.5.1"}}, "Microsoft.NETCore.App/2.2.8": {"dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.2.8", "Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "NETStandard.Library": "2.0.3"}}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.8"}}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.8"}}, "Microsoft.NETCore.Jit/1.0.2": {}, "Microsoft.NETCore.Platforms/2.2.4": {}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"dependencies": {"Microsoft.NETCore.Runtime.CoreCLR": "1.0.2"}, "runtime": {"lib/netstandard1.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}}, "compile": {"ref/netstandard1.0/System.ServiceModel.dll": {}}}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"dependencies": {"Microsoft.NETCore.Jit": "1.0.2", "Microsoft.NETCore.Windows.ApiSets": "1.0.1"}}, "Microsoft.NETCore.Targets/2.0.0": {}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "Microsoft.Win32.Registry/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Security.AccessControl": "4.4.0", "System.Security.Principal.Windows": "4.4.0"}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4"}}, "Newtonsoft.Json/13.0.3": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "13.0.3.27908"}}, "compile": {"lib/netstandard2.0/Newtonsoft.Json.dll": {}}}, "Npgsql/5.0.10": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netcoreapp3.1/Npgsql.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netcoreapp3.1/Npgsql.dll": {}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/5.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.16", "Microsoft.EntityFrameworkCore.Abstractions": "5.0.16", "Microsoft.EntityFrameworkCore.Relational": "5.0.16", "Npgsql": "5.0.10"}, "runtime": {"lib/netstandard2.1/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "********", "fileVersion": "********"}}, "compile": {"lib/netstandard2.1/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {}}}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design/1.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.16", "Microsoft.EntityFrameworkCore.Relational": "5.0.16", "Microsoft.EntityFrameworkCore.Relational.Design": "1.1.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "6.0.0", "Npgsql": "5.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL": "5.0.10"}, "runtime": {"lib/netstandard1.3/Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.3/Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll": {}}}, "Portable.BouncyCastle/1.8.9": {"runtime": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard2.0/BouncyCastle.Crypto.dll": {}}}, "Swashbuckle.AspNetCore/6.3.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.3.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.3.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.3.1"}}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.3.1"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}}, "System.Buffers/4.5.1": {}, "System.CodeDom/7.0.0": {"runtime": {"lib/netstandard2.0/System.CodeDom.dll": {"assemblyVersion": "7.0.0.0", "fileVersion": "7.0.22.51805"}}, "compile": {"lib/netstandard2.0/System.CodeDom.dll": {}}}, "System.Collections.Immutable/8.0.0": {"dependencies": {"System.Memory": "4.5.5", "System.Runtime.CompilerServices.Unsafe": "6.0.0"}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"assemblyVersion": "8.0.0.0", "fileVersion": "8.0.23.53103"}}, "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {}}}, "System.ComponentModel.Annotations/5.0.0": {"runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}, "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {}}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.220.61120"}}, "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.IdentityModel.Tokens.Jwt/5.5.0": {"dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "5.5.0", "Microsoft.IdentityModel.Tokens": "5.5.0", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "5.5.0.0", "fileVersion": "5.5.0.60624"}}, "compile": {"lib/netstandard2.0/System.IdentityModel.Tokens.Jwt.dll": {}}}, "System.IO.Hashing/6.0.0": {"dependencies": {"System.Buffers": "4.5.1", "System.Memory": "4.5.5"}, "runtime": {"lib/netstandard2.0/System.IO.Hashing.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/netstandard2.0/System.IO.Hashing.dll": {}}}, "System.Management/7.0.2": {"dependencies": {"System.CodeDom": "7.0.0"}, "runtime": {"lib/netstandard2.0/System.Management.dll": {"assemblyVersion": "*******", "fileVersion": "7.0.723.27404"}}, "compile": {"lib/netstandard2.0/System.Management.dll": {}}}, "System.Memory/4.5.5": {}, "System.Memory.Data/1.0.2": {"dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2"}, "runtime": {"lib/netstandard2.0/System.Memory.Data.dll": {"assemblyVersion": "1.0.2.0", "fileVersion": "1.0.221.20802"}}, "compile": {"lib/netstandard2.0/System.Memory.Data.dll": {}}}, "System.Numerics.Vectors/4.5.0": {}, "System.Reflection.Metadata/1.5.0": {}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"runtime": {"lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll": {"assemblyVersion": "*******", "fileVersion": "6.0.21.52210"}}, "compile": {"lib/netcoreapp3.1/System.Runtime.CompilerServices.Unsafe.dll": {}}}, "System.Security.AccessControl/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Security.Principal.Windows": "4.4.0"}}, "System.Security.Cryptography.Cng/4.5.0": {}, "System.Security.Cryptography.Xml/4.4.0": {}, "System.Security.Principal.Windows/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4"}}, "System.Text.Encodings.Web/4.7.2": {"runtime": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.21.11602"}}, "compile": {"lib/netstandard2.1/System.Text.Encodings.Web.dll": {}}}, "System.Text.Json/4.7.2": {"runtime": {"lib/netcoreapp3.0/System.Text.Json.dll": {"assemblyVersion": "*******", "fileVersion": "4.700.20.21406"}}, "compile": {"lib/netcoreapp3.0/System.Text.Json.dll": {}}}, "System.Threading.Tasks.Extensions/4.5.4": {}, "WindowsAzure.Storage/9.3.3": {"dependencies": {"NETStandard.Library": "2.0.3", "Newtonsoft.Json": "13.0.3"}, "runtime": {"lib/netstandard1.3/Microsoft.WindowsAzure.Storage.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}, "compile": {"lib/netstandard1.3/Microsoft.WindowsAzure.Storage.dll": {}}}, "Oberon.BusinessLogic/1.0.0": {"dependencies": {"AspNetCore.Firebase.Authentication": "2.0.1", "FireSharp": "2.0.4", "Firebase.Auth": "1.0.0", "FirebaseAdmin": "3.1.0", "Microsoft.NETCore.App": "2.2.8", "Npgsql.EntityFrameworkCore.PostgreSQL": "5.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL.Design": "1.1.0", "Swashbuckle.AspNetCore": "6.3.1", "Swashbuckle.AspNetCore.Swagger": "6.3.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.3.1"}, "runtime": {"Oberon.BusinessLogic.dll": {"assemblyVersion": "1.0.0", "fileVersion": "1.0.0.0"}}, "compile": {"Oberon.BusinessLogic.dll": {}}}, "Microsoft.AspNetCore.Antiforgery/*******": {"compile": {"Microsoft.AspNetCore.Antiforgery.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Cookies.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Core.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authentication.OAuth.Reference/*******": {"compile": {"Microsoft.AspNetCore.Authentication.OAuth.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"compile": {"Microsoft.AspNetCore.Authorization.Policy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Authorization/*******": {"compile": {"Microsoft.AspNetCore.Components.Authorization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components/*******": {"compile": {"Microsoft.AspNetCore.Components.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Forms/*******": {"compile": {"Microsoft.AspNetCore.Components.Forms.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Server/*******": {"compile": {"Microsoft.AspNetCore.Components.Server.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Components.Web/*******": {"compile": {"Microsoft.AspNetCore.Components.Web.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Connections.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.CookiePolicy/*******": {"compile": {"Microsoft.AspNetCore.CookiePolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cors/*******": {"compile": {"Microsoft.AspNetCore.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.Internal.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"compile": {"Microsoft.AspNetCore.Cryptography.KeyDerivation.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"compile": {"Microsoft.AspNetCore.DataProtection.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.Reference/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.AspNetCore.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Reference/*******": {"compile": {"Microsoft.AspNetCore.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HostFiltering/*******": {"compile": {"Microsoft.AspNetCore.HostFiltering.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Hosting.Server.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Html.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Connections/*******": {"compile": {"Microsoft.AspNetCore.Http.Connections.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Extensions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"compile": {"Microsoft.AspNetCore.Http.Features.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpOverrides.Reference/*******": {"compile": {"Microsoft.AspNetCore.HttpOverrides.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"compile": {"Microsoft.AspNetCore.HttpsPolicy.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Identity/*******": {"compile": {"Microsoft.AspNetCore.Identity.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization/*******": {"compile": {"Microsoft.AspNetCore.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Localization.Routing/*******": {"compile": {"Microsoft.AspNetCore.Localization.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Metadata/*******": {"compile": {"Microsoft.AspNetCore.Metadata.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ApiExplorer.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Core/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Cors.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"compile": {"Microsoft.AspNetCore.Mvc.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc/*******": {"compile": {"Microsoft.AspNetCore.Mvc.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Formatters.Xml.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Localization.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"compile": {"Microsoft.AspNetCore.Mvc.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"compile": {"Microsoft.AspNetCore.Mvc.RazorPages.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"compile": {"Microsoft.AspNetCore.Mvc.TagHelpers.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"compile": {"Microsoft.AspNetCore.Mvc.ViewFeatures.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor/*******": {"compile": {"Microsoft.AspNetCore.Razor.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"compile": {"Microsoft.AspNetCore.Razor.Runtime.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCaching/*******": {"compile": {"Microsoft.AspNetCore.ResponseCaching.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.ResponseCompression/*******": {"compile": {"Microsoft.AspNetCore.ResponseCompression.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Rewrite/*******": {"compile": {"Microsoft.AspNetCore.Rewrite.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Abstractions.Reference/*******": {"compile": {"Microsoft.AspNetCore.Routing.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Routing.Reference/*******": {"compile": {"Microsoft.AspNetCore.Routing.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"compile": {"Microsoft.AspNetCore.Server.HttpSys.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IIS/*******": {"compile": {"Microsoft.AspNetCore.Server.IIS.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.IISIntegration.Reference/*******": {"compile": {"Microsoft.AspNetCore.Server.IISIntegration.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Core.Reference/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Reference/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"compile": {"Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.Session/*******": {"compile": {"Microsoft.AspNetCore.Session.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Common/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Common.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Core/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Core.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR/*******": {"compile": {"Microsoft.AspNetCore.SignalR.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"compile": {"Microsoft.AspNetCore.SignalR.Protocols.Json.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.StaticFiles/*******": {"compile": {"Microsoft.AspNetCore.StaticFiles.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebSockets/*******": {"compile": {"Microsoft.AspNetCore.WebSockets.dll": {}}, "compileOnly": true}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"compile": {"Microsoft.AspNetCore.WebUtilities.dll": {}}, "compileOnly": true}, "Microsoft.CSharp.Reference/*******": {"compile": {"Microsoft.CSharp.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Binder.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.CommandLine.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.EnvironmentVariables.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.FileExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Ini/*******": {"compile": {"Microsoft.Extensions.Configuration.Ini.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.Json.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"compile": {"Microsoft.Extensions.Configuration.KeyPerFile.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"compile": {"Microsoft.Extensions.Configuration.UserSecrets.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Configuration.Xml/*******": {"compile": {"Microsoft.Extensions.Configuration.Xml.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"compile": {"Microsoft.Extensions.Diagnostics.HealthChecks.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Composite/*******": {"compile": {"Microsoft.Extensions.FileProviders.Composite.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"compile": {"Microsoft.Extensions.FileProviders.Embedded.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"compile": {"Microsoft.Extensions.FileProviders.Physical.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"compile": {"Microsoft.Extensions.FileSystemGlobbing.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"compile": {"Microsoft.Extensions.Hosting.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Hosting/*******": {"compile": {"Microsoft.Extensions.Hosting.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Http/*******": {"compile": {"Microsoft.Extensions.Http.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Core/*******": {"compile": {"Microsoft.Extensions.Identity.Core.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Identity.Stores/*******": {"compile": {"Microsoft.Extensions.Identity.Stores.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization.Abstractions/*******": {"compile": {"Microsoft.Extensions.Localization.Abstractions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Localization/*******": {"compile": {"Microsoft.Extensions.Localization.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Configuration.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Configuration.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Console.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Console.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.Debug.Reference/*******": {"compile": {"Microsoft.Extensions.Logging.Debug.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventLog/*******": {"compile": {"Microsoft.Extensions.Logging.EventLog.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.EventSource/*******": {"compile": {"Microsoft.Extensions.Logging.EventSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Logging.TraceSource/*******": {"compile": {"Microsoft.Extensions.Logging.TraceSource.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"compile": {"Microsoft.Extensions.ObjectPool.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"compile": {"Microsoft.Extensions.Options.ConfigurationExtensions.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"compile": {"Microsoft.Extensions.Options.DataAnnotations.dll": {}}, "compileOnly": true}, "Microsoft.Extensions.WebEncoders.Reference/*******": {"compile": {"Microsoft.Extensions.WebEncoders.dll": {}}, "compileOnly": true}, "Microsoft.JSInterop/*******": {"compile": {"Microsoft.JSInterop.dll": {}}, "compileOnly": true}, "Microsoft.Net.Http.Headers.Reference/*******": {"compile": {"Microsoft.Net.Http.Headers.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic.Core/********": {"compile": {"Microsoft.VisualBasic.Core.dll": {}}, "compileOnly": true}, "Microsoft.VisualBasic/10.0.0.0": {"compile": {"Microsoft.VisualBasic.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Primitives/*******": {"compile": {"Microsoft.Win32.Primitives.dll": {}}, "compileOnly": true}, "Microsoft.Win32.Registry.Reference/*******": {"compile": {"Microsoft.Win32.Registry.dll": {}}, "compileOnly": true}, "mscorlib/*******": {"compile": {"mscorlib.dll": {}}, "compileOnly": true}, "netstandard/2.1.0.0": {"compile": {"netstandard.dll": {}}, "compileOnly": true}, "System.AppContext/*******": {"compile": {"System.AppContext.dll": {}}, "compileOnly": true}, "System.Buffers.Reference/*******": {"compile": {"System.Buffers.dll": {}}, "compileOnly": true}, "System.Collections.Concurrent/4.0.15.0": {"compile": {"System.Collections.Concurrent.dll": {}}, "compileOnly": true}, "System.Collections/*******": {"compile": {"System.Collections.dll": {}}, "compileOnly": true}, "System.Collections.NonGeneric/*******": {"compile": {"System.Collections.NonGeneric.dll": {}}, "compileOnly": true}, "System.Collections.Specialized/*******": {"compile": {"System.Collections.Specialized.dll": {}}, "compileOnly": true}, "System.ComponentModel.DataAnnotations/*******": {"compile": {"System.ComponentModel.DataAnnotations.dll": {}}, "compileOnly": true}, "System.ComponentModel/*******": {"compile": {"System.ComponentModel.dll": {}}, "compileOnly": true}, "System.ComponentModel.EventBasedAsync/*******": {"compile": {"System.ComponentModel.EventBasedAsync.dll": {}}, "compileOnly": true}, "System.ComponentModel.Primitives/*******": {"compile": {"System.ComponentModel.Primitives.dll": {}}, "compileOnly": true}, "System.ComponentModel.TypeConverter/*******": {"compile": {"System.ComponentModel.TypeConverter.dll": {}}, "compileOnly": true}, "System.Configuration/*******": {"compile": {"System.Configuration.dll": {}}, "compileOnly": true}, "System.Console/*******": {"compile": {"System.Console.dll": {}}, "compileOnly": true}, "System.Core/*******": {"compile": {"System.Core.dll": {}}, "compileOnly": true}, "System.Data.Common/*******": {"compile": {"System.Data.Common.dll": {}}, "compileOnly": true}, "System.Data.DataSetExtensions/*******": {"compile": {"System.Data.DataSetExtensions.dll": {}}, "compileOnly": true}, "System.Data/*******": {"compile": {"System.Data.dll": {}}, "compileOnly": true}, "System.Diagnostics.Contracts/*******": {"compile": {"System.Diagnostics.Contracts.dll": {}}, "compileOnly": true}, "System.Diagnostics.Debug/*******": {"compile": {"System.Diagnostics.Debug.dll": {}}, "compileOnly": true}, "System.Diagnostics.EventLog/*******": {"compile": {"System.Diagnostics.EventLog.dll": {}}, "compileOnly": true}, "System.Diagnostics.FileVersionInfo/*******": {"compile": {"System.Diagnostics.FileVersionInfo.dll": {}}, "compileOnly": true}, "System.Diagnostics.Process/*******": {"compile": {"System.Diagnostics.Process.dll": {}}, "compileOnly": true}, "System.Diagnostics.StackTrace/*******": {"compile": {"System.Diagnostics.StackTrace.dll": {}}, "compileOnly": true}, "System.Diagnostics.TextWriterTraceListener/*******": {"compile": {"System.Diagnostics.TextWriterTraceListener.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tools/*******": {"compile": {"System.Diagnostics.Tools.dll": {}}, "compileOnly": true}, "System.Diagnostics.TraceSource/*******": {"compile": {"System.Diagnostics.TraceSource.dll": {}}, "compileOnly": true}, "System.Diagnostics.Tracing/*******": {"compile": {"System.Diagnostics.Tracing.dll": {}}, "compileOnly": true}, "System/*******": {"compile": {"System.dll": {}}, "compileOnly": true}, "System.Drawing/*******": {"compile": {"System.Drawing.dll": {}}, "compileOnly": true}, "System.Drawing.Primitives/*******": {"compile": {"System.Drawing.Primitives.dll": {}}, "compileOnly": true}, "System.Dynamic.Runtime/*******": {"compile": {"System.Dynamic.Runtime.dll": {}}, "compileOnly": true}, "System.Globalization.Calendars/*******": {"compile": {"System.Globalization.Calendars.dll": {}}, "compileOnly": true}, "System.Globalization/*******": {"compile": {"System.Globalization.dll": {}}, "compileOnly": true}, "System.Globalization.Extensions/*******": {"compile": {"System.Globalization.Extensions.dll": {}}, "compileOnly": true}, "System.IO.Compression.Brotli/*******": {"compile": {"System.IO.Compression.Brotli.dll": {}}, "compileOnly": true}, "System.IO.Compression/*******": {"compile": {"System.IO.Compression.dll": {}}, "compileOnly": true}, "System.IO.Compression.FileSystem/*******": {"compile": {"System.IO.Compression.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.Compression.ZipFile/*******": {"compile": {"System.IO.Compression.ZipFile.dll": {}}, "compileOnly": true}, "System.IO/*******": {"compile": {"System.IO.dll": {}}, "compileOnly": true}, "System.IO.FileSystem/*******": {"compile": {"System.IO.FileSystem.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.DriveInfo/*******": {"compile": {"System.IO.FileSystem.DriveInfo.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Primitives/*******": {"compile": {"System.IO.FileSystem.Primitives.dll": {}}, "compileOnly": true}, "System.IO.FileSystem.Watcher/*******": {"compile": {"System.IO.FileSystem.Watcher.dll": {}}, "compileOnly": true}, "System.IO.IsolatedStorage/*******": {"compile": {"System.IO.IsolatedStorage.dll": {}}, "compileOnly": true}, "System.IO.MemoryMappedFiles/*******": {"compile": {"System.IO.MemoryMappedFiles.dll": {}}, "compileOnly": true}, "System.IO.Pipelines/*******": {"compile": {"System.IO.Pipelines.dll": {}}, "compileOnly": true}, "System.IO.Pipes/*******": {"compile": {"System.IO.Pipes.dll": {}}, "compileOnly": true}, "System.IO.UnmanagedMemoryStream/*******": {"compile": {"System.IO.UnmanagedMemoryStream.dll": {}}, "compileOnly": true}, "System.Linq/*******": {"compile": {"System.Linq.dll": {}}, "compileOnly": true}, "System.Linq.Expressions/*******": {"compile": {"System.Linq.Expressions.dll": {}}, "compileOnly": true}, "System.Linq.Parallel/*******": {"compile": {"System.Linq.Parallel.dll": {}}, "compileOnly": true}, "System.Linq.Queryable/*******": {"compile": {"System.Linq.Queryable.dll": {}}, "compileOnly": true}, "System.Memory.Reference/*******": {"compile": {"System.Memory.dll": {}}, "compileOnly": true}, "System.Net/*******": {"compile": {"System.Net.dll": {}}, "compileOnly": true}, "System.Net.Http/*******": {"compile": {"System.Net.Http.dll": {}}, "compileOnly": true}, "System.Net.HttpListener/*******": {"compile": {"System.Net.HttpListener.dll": {}}, "compileOnly": true}, "System.Net.Mail/*******": {"compile": {"System.Net.Mail.dll": {}}, "compileOnly": true}, "System.Net.NameResolution/*******": {"compile": {"System.Net.NameResolution.dll": {}}, "compileOnly": true}, "System.Net.NetworkInformation/*******": {"compile": {"System.Net.NetworkInformation.dll": {}}, "compileOnly": true}, "System.Net.Ping/*******": {"compile": {"System.Net.Ping.dll": {}}, "compileOnly": true}, "System.Net.Primitives/*******": {"compile": {"System.Net.Primitives.dll": {}}, "compileOnly": true}, "System.Net.Requests/*******": {"compile": {"System.Net.Requests.dll": {}}, "compileOnly": true}, "System.Net.Security/*******": {"compile": {"System.Net.Security.dll": {}}, "compileOnly": true}, "System.Net.ServicePoint/*******": {"compile": {"System.Net.ServicePoint.dll": {}}, "compileOnly": true}, "System.Net.Sockets/*******": {"compile": {"System.Net.Sockets.dll": {}}, "compileOnly": true}, "System.Net.WebClient/*******": {"compile": {"System.Net.WebClient.dll": {}}, "compileOnly": true}, "System.Net.WebHeaderCollection/*******": {"compile": {"System.Net.WebHeaderCollection.dll": {}}, "compileOnly": true}, "System.Net.WebProxy/*******": {"compile": {"System.Net.WebProxy.dll": {}}, "compileOnly": true}, "System.Net.WebSockets.Client/*******": {"compile": {"System.Net.WebSockets.Client.dll": {}}, "compileOnly": true}, "System.Net.WebSockets/*******": {"compile": {"System.Net.WebSockets.dll": {}}, "compileOnly": true}, "System.Numerics/*******": {"compile": {"System.Numerics.dll": {}}, "compileOnly": true}, "System.Numerics.Vectors.Reference/*******": {"compile": {"System.Numerics.Vectors.dll": {}}, "compileOnly": true}, "System.ObjectModel/*******": {"compile": {"System.ObjectModel.dll": {}}, "compileOnly": true}, "System.Reflection.DispatchProxy/*******": {"compile": {"System.Reflection.DispatchProxy.dll": {}}, "compileOnly": true}, "System.Reflection/*******": {"compile": {"System.Reflection.dll": {}}, "compileOnly": true}, "System.Reflection.Emit/*******": {"compile": {"System.Reflection.Emit.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.ILGeneration/*******": {"compile": {"System.Reflection.Emit.ILGeneration.dll": {}}, "compileOnly": true}, "System.Reflection.Emit.Lightweight/*******": {"compile": {"System.Reflection.Emit.Lightweight.dll": {}}, "compileOnly": true}, "System.Reflection.Extensions/*******": {"compile": {"System.Reflection.Extensions.dll": {}}, "compileOnly": true}, "System.Reflection.Metadata.Reference/1.4.5.0": {"compile": {"System.Reflection.Metadata.dll": {}}, "compileOnly": true}, "System.Reflection.Primitives/*******": {"compile": {"System.Reflection.Primitives.dll": {}}, "compileOnly": true}, "System.Reflection.TypeExtensions/*******": {"compile": {"System.Reflection.TypeExtensions.dll": {}}, "compileOnly": true}, "System.Resources.Reader/*******": {"compile": {"System.Resources.Reader.dll": {}}, "compileOnly": true}, "System.Resources.ResourceManager/*******": {"compile": {"System.Resources.ResourceManager.dll": {}}, "compileOnly": true}, "System.Resources.Writer/*******": {"compile": {"System.Resources.Writer.dll": {}}, "compileOnly": true}, "System.Runtime.CompilerServices.VisualC/*******": {"compile": {"System.Runtime.CompilerServices.VisualC.dll": {}}, "compileOnly": true}, "System.Runtime/*******": {"compile": {"System.Runtime.dll": {}}, "compileOnly": true}, "System.Runtime.Extensions/*******": {"compile": {"System.Runtime.Extensions.dll": {}}, "compileOnly": true}, "System.Runtime.Handles/*******": {"compile": {"System.Runtime.Handles.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices/*******": {"compile": {"System.Runtime.InteropServices.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"compile": {"System.Runtime.InteropServices.RuntimeInformation.dll": {}}, "compileOnly": true}, "System.Runtime.InteropServices.WindowsRuntime/*******": {"compile": {"System.Runtime.InteropServices.WindowsRuntime.dll": {}}, "compileOnly": true}, "System.Runtime.Intrinsics/*******": {"compile": {"System.Runtime.Intrinsics.dll": {}}, "compileOnly": true}, "System.Runtime.Loader/*******": {"compile": {"System.Runtime.Loader.dll": {}}, "compileOnly": true}, "System.Runtime.Numerics/*******": {"compile": {"System.Runtime.Numerics.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization/*******": {"compile": {"System.Runtime.Serialization.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Formatters/*******": {"compile": {"System.Runtime.Serialization.Formatters.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Json/*******": {"compile": {"System.Runtime.Serialization.Json.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Primitives/*******": {"compile": {"System.Runtime.Serialization.Primitives.dll": {}}, "compileOnly": true}, "System.Runtime.Serialization.Xml/4.1.5.0": {"compile": {"System.Runtime.Serialization.Xml.dll": {}}, "compileOnly": true}, "System.Security.AccessControl.Reference/*******": {"compile": {"System.Security.AccessControl.dll": {}}, "compileOnly": true}, "System.Security.Claims/*******": {"compile": {"System.Security.Claims.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Algorithms/*******": {"compile": {"System.Security.Cryptography.Algorithms.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Cng.Reference/*******": {"compile": {"System.Security.Cryptography.Cng.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Csp/*******": {"compile": {"System.Security.Cryptography.Csp.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Encoding/*******": {"compile": {"System.Security.Cryptography.Encoding.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Primitives/*******": {"compile": {"System.Security.Cryptography.Primitives.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.X509Certificates/*******": {"compile": {"System.Security.Cryptography.X509Certificates.dll": {}}, "compileOnly": true}, "System.Security.Cryptography.Xml.Reference/*******": {"compile": {"System.Security.Cryptography.Xml.dll": {}}, "compileOnly": true}, "System.Security/*******": {"compile": {"System.Security.dll": {}}, "compileOnly": true}, "System.Security.Permissions/*******": {"compile": {"System.Security.Permissions.dll": {}}, "compileOnly": true}, "System.Security.Principal/*******": {"compile": {"System.Security.Principal.dll": {}}, "compileOnly": true}, "System.Security.Principal.Windows.Reference/*******": {"compile": {"System.Security.Principal.Windows.dll": {}}, "compileOnly": true}, "System.Security.SecureString/*******": {"compile": {"System.Security.SecureString.dll": {}}, "compileOnly": true}, "System.ServiceModel.Web/*******": {"compile": {"System.ServiceModel.Web.dll": {}}, "compileOnly": true}, "System.ServiceProcess/*******": {"compile": {"System.ServiceProcess.dll": {}}, "compileOnly": true}, "System.Text.Encoding.CodePages/*******": {"compile": {"System.Text.Encoding.CodePages.dll": {}}, "compileOnly": true}, "System.Text.Encoding/*******": {"compile": {"System.Text.Encoding.dll": {}}, "compileOnly": true}, "System.Text.Encoding.Extensions/*******": {"compile": {"System.Text.Encoding.Extensions.dll": {}}, "compileOnly": true}, "System.Text.RegularExpressions/*******": {"compile": {"System.Text.RegularExpressions.dll": {}}, "compileOnly": true}, "System.Threading.Channels/*******": {"compile": {"System.Threading.Channels.dll": {}}, "compileOnly": true}, "System.Threading/*******": {"compile": {"System.Threading.dll": {}}, "compileOnly": true}, "System.Threading.Overlapped/*******": {"compile": {"System.Threading.Overlapped.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Dataflow/*******": {"compile": {"System.Threading.Tasks.Dataflow.dll": {}}, "compileOnly": true}, "System.Threading.Tasks/*******": {"compile": {"System.Threading.Tasks.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Extensions.Reference/*******": {"compile": {"System.Threading.Tasks.Extensions.dll": {}}, "compileOnly": true}, "System.Threading.Tasks.Parallel/*******": {"compile": {"System.Threading.Tasks.Parallel.dll": {}}, "compileOnly": true}, "System.Threading.Thread/*******": {"compile": {"System.Threading.Thread.dll": {}}, "compileOnly": true}, "System.Threading.ThreadPool/*******": {"compile": {"System.Threading.ThreadPool.dll": {}}, "compileOnly": true}, "System.Threading.Timer/*******": {"compile": {"System.Threading.Timer.dll": {}}, "compileOnly": true}, "System.Transactions/*******": {"compile": {"System.Transactions.dll": {}}, "compileOnly": true}, "System.Transactions.Local/*******": {"compile": {"System.Transactions.Local.dll": {}}, "compileOnly": true}, "System.ValueTuple/*******": {"compile": {"System.ValueTuple.dll": {}}, "compileOnly": true}, "System.Web/*******": {"compile": {"System.Web.dll": {}}, "compileOnly": true}, "System.Web.HttpUtility/*******": {"compile": {"System.Web.HttpUtility.dll": {}}, "compileOnly": true}, "System.Windows/*******": {"compile": {"System.Windows.dll": {}}, "compileOnly": true}, "System.Windows.Extensions/*******": {"compile": {"System.Windows.Extensions.dll": {}}, "compileOnly": true}, "System.Xml/*******": {"compile": {"System.Xml.dll": {}}, "compileOnly": true}, "System.Xml.Linq/*******": {"compile": {"System.Xml.Linq.dll": {}}, "compileOnly": true}, "System.Xml.ReaderWriter/*******": {"compile": {"System.Xml.ReaderWriter.dll": {}}, "compileOnly": true}, "System.Xml.Serialization/*******": {"compile": {"System.Xml.Serialization.dll": {}}, "compileOnly": true}, "System.Xml.XDocument/*******": {"compile": {"System.Xml.XDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlDocument/*******": {"compile": {"System.Xml.XmlDocument.dll": {}}, "compileOnly": true}, "System.Xml.XmlSerializer/*******": {"compile": {"System.Xml.XmlSerializer.dll": {}}, "compileOnly": true}, "System.Xml.XPath/*******": {"compile": {"System.Xml.XPath.dll": {}}, "compileOnly": true}, "System.Xml.XPath.XDocument/*******": {"compile": {"System.Xml.XPath.XDocument.dll": {}}, "compileOnly": true}, "WindowsBase/*******": {"compile": {"WindowsBase.dll": {}}, "compileOnly": true}}}, "libraries": {"Oberon.API/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AspNetCore.Firebase.Authentication/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ANZ0Ty2nDI0RygVpFgabAAEj8CWwMgHArjEFWvgnVMVKKOaTAzbLeaGGpg+hI/39OWAOzNFytTG0vofWQjaQPA==", "path": "aspnetcore.firebase.authentication/2.0.1", "hashPath": "aspnetcore.firebase.authentication.2.0.1.nupkg.sha512"}, "Azure.Core/1.36.0": {"type": "package", "serviceable": true, "sha512": "sha512-vwqFZdHS4dzPlI7FFRkPx9ctA+aGGeRev3gnzG8lntWvKMmBhAmulABi1O9CEvS3/jzYt7yA+0pqVdxkpAd7dQ==", "path": "azure.core/1.36.0", "hashPath": "azure.core.1.36.0.nupkg.sha512"}, "Azure.Storage.Blobs/12.19.1": {"type": "package", "serviceable": true, "sha512": "sha512-x43hWFJ4sPQ23TD4piCwT+KlQpZT8pNDAzqj6yUCqh+WJ2qcQa17e1gh6ZOeT2QNFQTTDSuR56fm2bIV7i11/w==", "path": "azure.storage.blobs/12.19.1", "hashPath": "azure.storage.blobs.12.19.1.nupkg.sha512"}, "Azure.Storage.Common/12.18.1": {"type": "package", "serviceable": true, "sha512": "sha512-ohCslqP9yDKIn+DVjBEOBuieB1QwsUCz+BwHYNaJ3lcIsTSiI4Evnq81HcKe8CqM8qvdModbipVQKpnxpbdWqA==", "path": "azure.storage.common/12.18.1", "hashPath": "azure.storage.common.12.18.1.nupkg.sha512"}, "CorePush/3.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-oDTJY4TA2aJd6UqsiN250yVAMjAuoIpB8skak/43w+ea9WBED6v0yosJr9v4nak8WG8umonbxIiiPiqkM5GxjA==", "path": "corepush/3.0.10", "hashPath": "corepush.3.0.10.nupkg.sha512"}, "Firebase.Auth/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MRAGIRDXqB0jRvtJx0yRmTMjZInQcUSyHN7LuOwxL/nfyceDEA+Gfiimn7HtI2ntHw3hdUAoeeCA6BCc3qg0/A==", "path": "firebase.auth/1.0.0", "hashPath": "firebase.auth.1.0.0.nupkg.sha512"}, "FirebaseAdmin/3.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-QNafwBg5psTGKOIyo78sNYjJ9GI5z/TPqOgpyOOv+2nzeK8sVaIPT2eDpcgzpxcRcKbjISbel38cQH/pIHNeOA==", "path": "firebaseadmin/3.1.0", "hashPath": "firebaseadmin.3.1.0.nupkg.sha512"}, "FireSharp/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-laSHpVUBG6iCTUixmpeUmYxie2zbziOBM+LU0v2A0ETE9dUyBA+8aY63xql/o8LXj+kFybBLYUKBUrLCZWaq+A==", "path": "firesharp/2.0.4", "hashPath": "firesharp.2.0.4.nupkg.sha512"}, "Google.Api.Gax/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlV8Jq/G5CQAA3PwYAuKGjfzGOP7AvjhREnE6vgZlzxREGYchHudZWa2PWSqFJL+MBtz9YgitLpRogANN3CVvg==", "path": "google.api.gax/4.8.0", "hashPath": "google.api.gax.4.8.0.nupkg.sha512"}, "Google.Api.Gax.Rest/4.8.0": {"type": "package", "serviceable": true, "sha512": "sha512-zaA5LZ2VvGj/wwIzRB68swr7khi2kWNgqWvsB0fYtScIAl3kGkGtqiBcx63H1YLeKr5xau1866bFjTeReH6FSQ==", "path": "google.api.gax.rest/4.8.0", "hashPath": "google.api.gax.rest.4.8.0.nupkg.sha512"}, "Google.Apis/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-s2MymhdpH+ybZNBeZ2J5uFgFHApBp+QXf9FjZSdM1lk/vx5VqIknJwnaWiuAzXxPrLEkesX0Q+UsiWn39yZ9zw==", "path": "google.apis/1.68.0", "hashPath": "google.apis.1.68.0.nupkg.sha512"}, "Google.Apis.Auth/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-hFx8Qz5bZ4w0hpnn4tSmZaaFpjAMsgVElZ+ZgVLUZ2r9i+AKcoVgwiNfv1pruNS5cCvpXqhKECbruBCfRezPHA==", "path": "google.apis.auth/1.68.0", "hashPath": "google.apis.auth.1.68.0.nupkg.sha512"}, "Google.Apis.Core/1.68.0": {"type": "package", "serviceable": true, "sha512": "sha512-pAqwa6pfu53UXCR2b7A/PAPXeuVg6L1OFw38WckN27NU2+mf+KTjoEg2YGv/f0UyKxzz7DxF1urOTKg/6dTP9g==", "path": "google.apis.core/1.68.0", "hashPath": "google.apis.core.1.68.0.nupkg.sha512"}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "Libuv/1.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsCf4q+eyaI49rCPlgYxdxa1SQCysXFFdSJWdstrwxytg4+VPYLYrXD4AT2rjHVJ+UF7SSWX9CapWEYaU4ejVQ==", "path": "libuv/1.10.0", "hashPath": "libuv.1.10.0.nupkg.sha512"}, "Microsoft.AspNetCore/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-M1kweIFWsyqHnY4W8Jqwz/tuVKF7Ff1mokn9+jpMs+S8m1wlGKeqmy9ovNF1rJoSTnF97cb4Wn0JoTA84bCYSQ==", "path": "microsoft.aspnetcore/2.0.2", "hashPath": "microsoft.aspnetcore.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-11a6DvTSur4T62bf/l0nb1uS0h0vXfOiAMCwDYqFuR1Pkox8v9eiTgduyxDppmEQuAh3TboPhYY3TzufEAFK3Q==", "path": "microsoft.aspnetcore.authentication/2.0.3", "hashPath": "microsoft.aspnetcore.authentication.2.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-12+IIkf+5eM/fNch3k+nj8nzIeaQYBF87TxZZ3Uf42wPoMuGzc8nMx8fMQDyqKtzJJ+9WCnH7N9N8ekTz9F7xg==", "path": "microsoft.aspnetcore.authentication.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-qA2YEcpU02rBZvtOaZk4RPIBqneGAzkS0dBQXcHk31cvf5bbzj+FHENmTKgsXDADyKVR0U1+7kS+bc44JxGCVA==", "path": "microsoft.aspnetcore.authentication.core/2.0.2", "hashPath": "microsoft.aspnetcore.authentication.core.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/3.1.32": {"type": "package", "serviceable": true, "sha512": "sha512-tBnXyLLOm6VxJ51qXU2D7yzmcbXlLUh2YtTYFfNmGiznATcvO3d+e6Q0rRPFAKk92R6gP2JAu3FApM+6sfHjCQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/3.1.32", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.3.1.32.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OAuth/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cuQYTKA/u5/uY5Wxu8OyLRUAt3U7kGyBmHwHvWz83vseBsnvso+qp+KX9syr/5PfkEvzub1RCvctB2NCRz5vNQ==", "path": "microsoft.aspnetcore.authentication.oauth/2.0.3", "hashPath": "microsoft.aspnetcore.authentication.oauth.2.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-pCJyY7vC6YWY94ssKcgGzVFGsK/bk7RVEH/BxwHmc+T3t5VmXlBq7VvUmhLfk+P5Uc1l0hDIJX0ZJRLy9Sz1jg==", "path": "microsoft.aspnetcore.cryptography.internal/2.0.2", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-BXVpydukX6AjcnELAZHtTNexSdGLwJ21suskAtDgQshDz/mfySm0Z/voNzQyPFF6SMzDf7iXnXpEBMZchL18Rg==", "path": "microsoft.aspnetcore.dataprotection/2.0.2", "hashPath": "microsoft.aspnetcore.dataprotection.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Q4eEkEE527CR1qzfyVeTGDVL3mss2D0VKSMWJCwhzxVmSDFy3zyXaJfCDu39GnExAVM9gLKzkoU6KoJGu3vyAg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-fAsBgV/202K4ZMB3eFLWAXYRqUz4uf9CR9MwpNYJhMhO+yHxNPGDFBatsiKUVxG4oeMdhFXzYwUbUSaWUYU/7Q==", "path": "microsoft.aspnetcore.diagnostics/2.0.2", "hashPath": "microsoft.aspnetcore.diagnostics.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-4Zb2/cIFGfyHhPMr1tg1Tyuur4PK9Nr5uKnRLxHPJJh1OuAwEAZtUsPHcUa6HHNoA5tZhUFonHJwiFTy9+ZLLA==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-qKV9PnsiVC2J1ws1DPoQ1fX3bowLTK2WjXPXpItgKVbuuLSWM1ECoObX2fOkQt6FKt4vJ9i4j/hktFavxova1Q==", "path": "microsoft.aspnetcore.hosting/2.0.2", "hashPath": "microsoft.aspnetcore.hosting.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-358NTTCWJWpDKno3S85BU0hjxWQ8EzsyjZ5OSMi2XpQ9SrYwzTq6tlXSpVS3cV2RJ2Jx9lXc8uSXFwrOVyUieQ==", "path": "microsoft.aspnetcore.hosting.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-tvz7D661JTyJXRxWLqOSH0s1zF9bLviZd14aA8poR+srvldS0gg1j62e7SaM5LQrUn+Z4dPwJqBtLXZDj5PtYw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-oVmQJvA1dHr96VcJVyUYEPcQH+FHSJSEu52Fq6aB7rmpjtyxlcFzyvRNumD4J1QJjlhE/V8jF10lY2hH0J6h4w==", "path": "microsoft.aspnetcore.http/2.0.2", "hashPath": "microsoft.aspnetcore.http.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-yQM9JzPAExsxTqvJBBr3yC+6XyOETi2T/eOOBjrOOnYgQOO+7M7J8VvAW0wQID9zh7QqWO6kh3BGCT/aqvydtg==", "path": "microsoft.aspnetcore.http.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.http.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-z9uJ6w3BnhjWZZW+i5rVCqKIVLmngLP1AutfOJXJKtXKjAOBqWSTBgySGROqzWkPuDXot1dHVP7NAMnhtloIiQ==", "path": "microsoft.aspnetcore.http.extensions/2.0.2", "hashPath": "microsoft.aspnetcore.http.extensions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-1U5fPSOtIq+cPuqJTjN+EFN3dWn4ptSjybd8minSbyhy0oXr8ujYla86kb9kM3rddUBgrGCyTp/hf0/tMZab+g==", "path": "microsoft.aspnetcore.http.features/2.0.2", "hashPath": "microsoft.aspnetcore.http.features.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.HttpOverrides/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hZPYYSnG17A+fFws1R5eQBmzF/9zewVlsBk/XeXTQ8fmjY8fUaOyBQGrs3OWKRXtRt3D1VetJ+ngZFl3a5YS9g==", "path": "microsoft.aspnetcore.httpoverrides/2.0.2", "hashPath": "microsoft.aspnetcore.httpoverrides.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-v0f0iRS9H71g49cwNH8hezpZalluUc1Ok3sModvqC4heLdqfAAO52GxWYVtB6lOw5JR6YYy3KvINOx+YghsdHg==", "path": "microsoft.aspnetcore.routing/2.0.2", "hashPath": "microsoft.aspnetcore.routing.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-sqI4xsQYm/11KsY8P892yrpL3ALAp6e6u12mrnbdWhQt/IiWhK4X9OIQVVMM+ofrPkAKsjP96ctEkJcDKysNVw==", "path": "microsoft.aspnetcore.routing.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IISIntegration/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-UUbQIZp5dmEnDrgjIGjiTqqMBlus1+q+nL0JTmo40UveFVMO4rQSBMwv7M9QzR+T1qFCWNcysbutHIOdoYl8bA==", "path": "microsoft.aspnetcore.server.iisintegration/2.0.2", "hashPath": "microsoft.aspnetcore.server.iisintegration.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rPDyGoafAZwRvovro5wzmeaOScYjehjy7yABvgMfkkiPTUeSDdtm020XR3HFU+GxCAmhU8bQhLUH0CKk9NNGDQ==", "path": "microsoft.aspnetcore.server.kestrel/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-+d7WB++otIdpV10mbHsUEcPmL+676Zljsls4DUkaSB8toiYndEeK+yxXj9OsGtTCzQhv4FjLqEcgw01oA0JYbw==", "path": "microsoft.aspnetcore.server.kestrel.core/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.core.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-v8WKn9TCiGvgocbCFDxeOj3neAgEHwfpqu/J4W2GbwprRDawFLP5XbTDjbNjo5J2UVgFH5NHaRJocNWc3raQ9g==", "path": "microsoft.aspnetcore.server.kestrel.https/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.https.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-25BwaKnlKHZqPnOT1De2Oe7kpwWWxb7eMrnJx2FPyN5N4rfn/3GaSC72nZzwT4us9e8vKUJP+uzo1yFEBblbXA==", "path": "microsoft.aspnetcore.server.kestrel.transport.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3H5R93EodGu8WsPYJwjXyDwks+nvpso6F01qPiowWU1dHpPGsY8px3XX3QTX3vPlwCXjpwvwlDXY8AT7kgBJzg==", "path": "microsoft.aspnetcore.server.kestrel.transport.libuv/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.libuv.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dvn80+p1AIQKOfJ+VrOhVMUktWRvJs7Zb+UapZGBNSyrCzTsYiXbb9C7Mzw+nGj5UevnLNFcWWc7BUlLMD2qpw==", "path": "microsoft.aspnetcore.webutilities/2.0.2", "hashPath": "microsoft.aspnetcore.webutilities.2.0.2.nupkg.sha512"}, "Microsoft.Bcl/1.1.9": {"type": "package", "serviceable": true, "sha512": "sha512-USQ55innJy8K+tAXvVa1O8dUTp2s7pmJ5cJj6Tl02HtGc2xBDj0P2QH5620HbGXIWKWYhydoeQF8Rm/JRDBGhw==", "path": "microsoft.bcl/1.1.9", "hashPath": "microsoft.bcl.1.1.9.nupkg.sha512"}, "Microsoft.Bcl.Async/1.0.168": {"type": "package", "serviceable": true, "sha512": "sha512-tUNC02eBwDKpGre0BcNIvblLv1q0Q3DnS/vtkRHj2FE1sXwt386HAudztyl5C0U88hllrqHDvtlz8bK0Y8cHDA==", "path": "microsoft.bcl.async/1.0.168", "hashPath": "microsoft.bcl.async.1.0.168.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-UcSjPsst+DfAdJGVDsu346FX0ci0ah+lw3WRtn18NUwEqRt70HaOQ7lI72vy3+1LxtqI3T5GWwV39rQSrCzAeg==", "path": "microsoft.bcl.asyncinterfaces/6.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.6.0.0.nupkg.sha512"}, "Microsoft.Bcl.Build/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-cDLKSvNvRa519hplsbSoYqO69TjdDIhfjtKUM0g20/nVROoWsGav9KCI9HtnGjLmdV1+TcUUDhbotcllibjPEA==", "path": "microsoft.bcl.build/1.0.14", "hashPath": "microsoft.bcl.build.1.0.14.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-niMGaRvTPPGAhuhKxFdmdfeW5Vo6rvhNXy2vnRotC7ToR/1ST6iDqZCIvW4L/Pt2+ISiFbvcqRQIE9MrNSMUZQ==", "path": "microsoft.entityframeworkcore/5.0.16", "hashPath": "microsoft.entityframeworkcore.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-nGESdyRnKGQ0PBUBrtVwGz3U13/C7RKcOctps2WozCGo5fHnQmcVtZ23mYc3Ri1LSMY+l4vLblBPJUzRaPaAWA==", "path": "microsoft.entityframeworkcore.abstractions/5.0.16", "hashPath": "microsoft.entityframeworkcore.abstractions.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-BiSrqLpJ6dv6xOH1xuHG/85edPfQo5DbuUZJqXNCOm3DD9BjnxaeVYnl2EUdadEUbCTTNVhXT0iKnaOpD5bv6A==", "path": "microsoft.entityframeworkcore.analyzers/5.0.16", "hashPath": "microsoft.entityframeworkcore.analyzers.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-LoOs2a4OhPPPv4AdjRMDEn4FJXhLZ5L8uiriomNYTdS7UqP7+7b32s+xDuntqfJhIFMNURaZtT1nDeVLaseb+g==", "path": "microsoft.entityframeworkcore.design/5.0.16", "hashPath": "microsoft.entityframeworkcore.design.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-B1ux0Pf1tTo7AXRldxInBWlFVm0/SVdV/7wSBxUmwy+uRO4oQCIJk69ot1DbL8YCQKjSMMB6IZkRTK95Haetow==", "path": "microsoft.entityframeworkcore.relational/5.0.16", "hashPath": "microsoft.entityframeworkcore.relational.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational.Design/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-fF0sVEUkoGeJutGIzwnYsJJ9o2hxEGJRpSaCxPq63rhSwn0hBmCwf9ET4QqYqO9Pc6+ODXenAQa095CWzuM4Kg==", "path": "microsoft.entityframeworkcore.relational.design/1.1.0", "hashPath": "microsoft.entityframeworkcore.relational.design.1.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-56FD9EtENIN/0SnQ7jPCURL0jgawraHhyJb2Qtf9y8PK8nOE+YfyabLsC4MGWxVgxe4c3pFon+3SWl0I1qqIFg==", "path": "microsoft.entityframeworkcore.tools/5.0.16", "hashPath": "microsoft.entityframeworkcore.tools.5.0.16.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "path": "microsoft.extensions.caching.abstractions/5.0.0", "hashPath": "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d9fFoEYRaBccu/Z2B2BZCil/lEnmoVQ8YiY1dGViERh0qWjixgR9y/M7EGaoTrAunnmvAmfwxuij/gCq6WvL1w==", "path": "microsoft.extensions.configuration/2.0.1", "hashPath": "microsoft.extensions.configuration.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5I1aC5g3+zb10nbNfTEz0YVFuKgvNU4jul0iDX10Q1nVyZoj33TsoNQwcJqBzJBxwjDSSGhejhgsQduREhFm6g==", "path": "microsoft.extensions.configuration.binder/2.0.1", "hashPath": "microsoft.extensions.configuration.binder.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xbA72loiTC3MK89cJZBEEbl4jWi8ugUJjd6Ak4jJN7JXerVURpWhSJ7engn+gZKYwvzdbt0vkr+/u015Pe4gqA==", "path": "microsoft.extensions.configuration.commandline/2.0.1", "hashPath": "microsoft.extensions.configuration.commandline.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ex3C6fEpePj3pekjjDTbSY/+IR371KDv+BFp6Wev/q0uPBmFN5dXlvy2M37fYmfca/VIb3rkOIqHpheWG3Iezg==", "path": "microsoft.extensions.configuration.environmentvariables/2.0.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ig55mY9fpfvVbQLuiT1ETjpYuI33RiSfhdon0nfl3m9cRSCJrrq2X7MXus2ihh2eW3ev+jPBHWNOFjN0YRN3cg==", "path": "microsoft.extensions.configuration.fileextensions/2.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RIh+RKEFkLDNeOhwPasPslqVDr72NVedR0rNKwxWnCZftAlSa4jmKg7nCacB4pU7rK2TMgl85ZaHZmrxC7Rcew==", "path": "microsoft.extensions.configuration.json/2.0.1", "hashPath": "microsoft.extensions.configuration.json.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MZMMOV7cMHnT7bAfcF2NmLywHXcw3krNtrPmjTO/CoimDl4dJbd7YhM29S5EFkr10nwMslH3VQtMccSVKGAcyw==", "path": "microsoft.extensions.configuration.usersecrets/2.0.1", "hashPath": "microsoft.extensions.configuration.usersecrets.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "path": "microsoft.extensions.dependencyinjection/5.0.2", "hashPath": "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-xlzi2IYREJH3/m6+lUrQlujzX8wDitm4QGnUu6kUXTQAWPuZY8i+ticFJbzfqaetLA6KR/rO6Ew/HuYD+bxifg==", "path": "microsoft.extensions.dependencyinjection.abstractions/6.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.6.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Gzc5yXvwIrKpdti0Ev4jC0inVrGZpI86eLZorMVRqAPXowR8JDRbcHjhmID2EqA4rdhL/IsfD42+4upKpHULDw==", "path": "microsoft.extensions.fileproviders.abstractions/2.0.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+6bcXYlGldl7BUhnQKFxL2sMfeg9Gr/AuVexYOCYWmzDsc4iyUoy3NL7i2vkG209wd0ZXf+pZzRDwGPFhmlSw==", "path": "microsoft.extensions.fileproviders.physical/2.0.1", "hashPath": "microsoft.extensions.fileproviders.physical.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-q7KsG2kjwo2Ps0WdV7MFh64cQS0UHikV8qv4HQrUfWQyxim5vNmLzAbuduarS9QWbhRHTtUanx+ohyAQdumdnw==", "path": "microsoft.extensions.filesystemglobbing/2.0.1", "hashPath": "microsoft.extensions.filesystemglobbing.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-gs+TNXCW05ujojZlQj2i9Fj00IAhXrgLZtgGM0XxoSoffgCGfGh7jX4kB/dnaot3xVdw84L1nE98bwQN7+kK8A==", "path": "microsoft.extensions.hosting.abstractions/2.0.2", "hashPath": "microsoft.extensions.hosting.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xIA/im+xMO80xHvfFCa3IQ6/L20pHl7MjyEZjKQKHRNsZgJIk4e8dfdHGeNaXChuTUycQ0EBdyN4kXUFqbAk3A==", "path": "microsoft.extensions.logging.configuration/2.0.1", "hashPath": "microsoft.extensions.logging.configuration.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-lbAWSy/Iwj584V6TAcKK8DU37IDOO7l+fMfktTsQWs14a4NXF/S0DjdbeJ5QoGR3aQiIlKJvNoCPoKLO9XeBMQ==", "path": "microsoft.extensions.logging.console/2.0.1", "hashPath": "microsoft.extensions.logging.console.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Cvsb3YWmuy7R/CRCAjoTVHDG3GDDVROfp3UWjo7CnxGX2Czc89AUPjxH5JFOd7xOplj12BX/KgU5m1KO3VOJIg==", "path": "microsoft.extensions.logging.debug/2.0.1", "hashPath": "microsoft.extensions.logging.debug.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drOmgNZCJiNEqFM/TvyqwtogS8wqoWGQCW5KB/CVGKL6VXHw8OOMdaHyspp8HPstP9UDnrnuq+8eaCaAcQg6tA==", "path": "microsoft.extensions.objectpool/2.0.0", "hashPath": "microsoft.extensions.objectpool.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-O1/MZSjWHdo4NNBD83ibRi83kKkbqbe+XTuoQtyk9NpfzYO6GoeEA+5ClEMJ56BO9DCNZb5SCBCPdlt2MdLFfw==", "path": "microsoft.extensions.options.configurationextensions/2.0.1", "hashPath": "microsoft.extensions.options.configurationextensions.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uRVexwgmsT3kfLKYb1mVOh96DIfo13Jp0rXvVZjFLEL29TV9K3GUeM/qTgm5P+hncWCMU6KOmx/QA+954pBMtw==", "path": "microsoft.extensions.webencoders/2.0.1", "hashPath": "microsoft.extensions.webencoders.2.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.JsonWebTokens/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cT9SCW/dN+ulrvAtbh37c36DR6aArENH3S4UtFmvXRx+VGC0ArDgzRaEbEh+ChS4koxdl2oS691250iZhgKvwg==", "path": "microsoft.identitymodel.jsonwebtokens/5.5.0", "hashPath": "microsoft.identitymodel.jsonwebtokens.5.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-1w/Hz/7+al+ugQn+6y0tAPmpN8U0u1aBtl1QXYCVkiJfbCC4tgyroFOuhdztOq48rgeM+3JW9bGqOtkfVurW8w==", "path": "microsoft.identitymodel.logging/5.5.0", "hashPath": "microsoft.identitymodel.logging.5.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-m1gwAQwZjUxzRBC+4H40vYSo9Cms9yUbMdW492rQoXHU77G/ItiKxpk2+W9bWYcdsKUDKudye7im3T3MlVxEkg==", "path": "microsoft.identitymodel.protocols/5.5.0", "hashPath": "microsoft.identitymodel.protocols.5.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-21F4QlbaD5CXNs2urNRCO6vljbbrhv3gmGT8P18SKGKZ9IYBCn29extoJriHiPfhABd5b8S7RcdKU50XhERkYg==", "path": "microsoft.identitymodel.protocols.openidconnect/5.5.0", "hashPath": "microsoft.identitymodel.protocols.openidconnect.5.5.0.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-cu1klZiuCwVYbXHs0QdnseuoRGG1/85VX9d1Sk0vbJlKp+HJUN/4pAS/fe2m9bTOYyIPdeCHeksMiVHgo1EfAA==", "path": "microsoft.identitymodel.tokens/5.5.0", "hashPath": "microsoft.identitymodel.tokens.5.5.0.nupkg.sha512"}, "Microsoft.Net.Http/2.2.28": {"type": "package", "serviceable": true, "sha512": "sha512-hPL9k+rPIpAj84RZSq3WtJkOD9uMStBovAIAz/0OQ+9hQIwQ2XqQlAhMqI4kw0XDxweJySIqNTMlkhRRuthcPQ==", "path": "microsoft.net.http/2.2.28", "hashPath": "microsoft.net.http.2.2.28.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hNhJU+Sd7Ws/yrBnakUWKWMyGiDUJE5lTkJfWe5xPL8YGTiL6Es07H9CcTyaYYwVlgW06uDVN0YhhH+t4EjdCw==", "path": "microsoft.net.http.headers/2.0.2", "hashPath": "microsoft.net.http.headers.2.0.2.nupkg.sha512"}, "Microsoft.NETCore.App/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-GOxlvyc8hFrnhDjYlm25JJ7PwoyeoOpZzcg6ZgF8n8l6VxezNupRkkTeA2ek1WsspN0CdAoA8e7iDVNU84/F+Q==", "path": "microsoft.netcore.app/2.2.8", "hashPath": "microsoft.netcore.app.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Lh1F6z41levvtfC3KuuiQe9ppWKRP1oIB42vP1QNQE4uumo95h+LpjPDeysX1DlTjCzG0BVGSUEpCW5fHkni7w==", "path": "microsoft.netcore.dotnetapphost/2.2.8", "hashPath": "microsoft.netcore.dotnetapphost.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-rOHr0Dk87vaiq9d1hMpXETB4IKq1jIiPQlVKNUjRGilK/cjOcadhsk+1MsrJ/GnM3eovhy8zW2PGkN8pYEolnw==", "path": "microsoft.netcore.dotnethostpolicy/2.2.8", "hashPath": "microsoft.netcore.dotnethostpolicy.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-culLr+x2GvUkXVGi4ULZ7jmWJEhuAMyS7iTWBlkWnqbKtYJ36ZlgHbw/6qTm82790gJemEFeo9RehDwfRXfJzA==", "path": "microsoft.netcore.dotnethostresolver/2.2.8", "hashPath": "microsoft.netcore.dotnethostresolver.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.Jit/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ok2vWofa6X8WD9vc4pfLHwvJz1/B6t3gOAoZcjrjrQf7lQOlNIuZIZtLn3wnWX28DuQGpPJkRlBxFj7Z5txNqw==", "path": "microsoft.netcore.jit/1.0.2", "hashPath": "microsoft.netcore.jit.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-ZeCe9PRhMpKzVWrNgTvWpLjJigppErzN663lJOqAzcx0xjXpcAMpIImFI46IE1gze18VWw6bbfo7JDkcaRWuOg==", "path": "microsoft.netcore.platforms/2.2.4", "hashPath": "microsoft.netcore.platforms.2.2.4.nupkg.sha512"}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vd+lvLcGwvkedxtKn0U8s9uR4p0Lm+0U2QvDsLaw7g4S1W4KfPDbaW+ROhhLCSOx/gMYC72/b+z+o4fqS/oxVg==", "path": "microsoft.netcore.portable.compatibility/1.0.1", "hashPath": "microsoft.netcore.portable.compatibility.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-A0x1xtTjYJWZr2DRzgfCOXgB0JkQg8twnmtTJ79wFje+IihlLbXtx6Z2AxyVokBM5ruwTedR6YdCmHk39QJdtQ==", "path": "microsoft.netcore.runtime.coreclr/1.0.2", "hashPath": "microsoft.netcore.runtime.coreclr.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Targets/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-odP/tJj1z6GylFpNo7pMtbd/xQgTC3Ex2If63dRTL38bBNMwsBnJ+RceUIyHdRBC0oik/3NehYT+oECwBhIM3Q==", "path": "microsoft.netcore.targets/2.0.0", "hashPath": "microsoft.netcore.targets.2.0.0.nupkg.sha512"}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-SaToCvvsGMxTgtLv/BrFQ5IFMPRE1zpWbnqbpwykJa8W5XiX82CXI6K2o7yf5xS7EP6t/JzFLV0SIDuWpvBZVw==", "path": "microsoft.netcore.windows.apisets/1.0.1", "hashPath": "microsoft.netcore.windows.apisets.1.0.1.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.Win32.Registry/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dA36TlNVn/XfrZtmf0fiI/z1nd3Wfp2QVzTdj26pqgP9LFWq0i1hYEUAW50xUjGFYn1+/cP3KGuxT2Yn1OUNBQ==", "path": "microsoft.win32.registry/4.4.0", "hashPath": "microsoft.win32.registry.4.4.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/13.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ==", "path": "newtonsoft.json/13.0.3", "hashPath": "newtonsoft.json.13.0.3.nupkg.sha512"}, "Npgsql/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-3TB9le3lfu5Hc+LSHqMCVLcA+qUPg1enyM4+u0pMUBmNNGwc0sVPrnfnys2TVZIdkF8Aww/AZlnJHDsnEGqD0g==", "path": "npgsql/5.0.10", "hashPath": "npgsql.5.0.10.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-3K+YNZFdwMcTxY8aW9m6BHyzPce/WFdDKPESP4uCXk6xNS6Ujq6zhi7+KXkzb7iNB0OYretEuF3ezqkk4ZnJMg==", "path": "npgsql.entityframeworkcore.postgresql/5.0.10", "hashPath": "npgsql.entityframeworkcore.postgresql.5.0.10.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-+taA6t8sRI0JTbyCZH0GobEtJYvU4aAGBf5/2/CutWHiYpO2nLfJ8SQSyWYMKakrwn1/NDM9VV85mGEAzBZ7LQ==", "path": "npgsql.entityframeworkcore.postgresql.design/1.1.0", "hashPath": "npgsql.entityframeworkcore.postgresql.design.1.1.0.nupkg.sha512"}, "Portable.BouncyCastle/1.8.9": {"type": "package", "serviceable": true, "sha512": "sha512-wlJo8aFoeyl+W93iFXTK5ShzDYk5WBqoUPjTNEM0Xv9kn1H+4hmuCjF0/n8HLm9Nnp1aY6KNndWqQTNk+NGgRQ==", "path": "portable.bouncycastle/1.8.9", "hashPath": "portable.bouncycastle.1.8.9.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-JFk0+HHUPdjYuPhkpGBMLi2JtnEuWkE2pp0yXQp64DmeMe+Fb0hZyVNq/ENJ2vQNso7Zg+C758WmR/xyAl36bA==", "path": "swashbuckle.aspnetcore/6.3.1", "hashPath": "swashbuckle.aspnetcore.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-idAFh4xhyJHYHfdLVOOn+BmscBul1OQbWsnL6YPJE8tO/0y6S79hDCvs6OY5VI093/9+1pYY3j31Zet9yaDZjA==", "path": "swashbuckle.aspnetcore.swagger/6.3.1", "hashPath": "swashbuckle.aspnetcore.swagger.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-+uoBV4h/6NhCPLoTofSmuOnZ+usu4PW1jP6l4OHwPyu2frbYXGNpJsHs5uUXXn929OiVQkT8wo3Lj/o+P99Ejg==", "path": "swashbuckle.aspnetcore.swaggergen/6.3.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-JLm9hN67jh7RHsX3H30+tb432Li8xm/qV5lRyMMkyHYMfWitIuKAAdrpo2ILcHOIeH7CLMuOO2hp/iLBmE+Bkw==", "path": "swashbuckle.aspnetcore.swaggerui/6.3.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.3.1.nupkg.sha512"}, "System.Buffers/4.5.1": {"type": "package", "serviceable": true, "sha512": "sha512-Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg==", "path": "system.buffers/4.5.1", "hashPath": "system.buffers.4.5.1.nupkg.sha512"}, "System.CodeDom/7.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-GLltyqEsE5/3IE+zYRP5sNa1l44qKl9v+bfdMcwg+M9qnQf47wK3H0SUR/T+3N4JEQXF3vV4CSuuo0rsg+nq2A==", "path": "system.codedom/7.0.0", "hashPath": "system.codedom.7.0.0.nupkg.sha512"}, "System.Collections.Immutable/8.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-AurL6Y5BA1WotzlEvVaIDpqzpIPvYnnldxru8oXJU2yFxFUy3+pNXjXd1ymO+RA0rq0+590Q8gaz2l3Sr7fmqg==", "path": "system.collections.immutable/8.0.0", "hashPath": "system.collections.immutable.8.0.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "path": "system.diagnostics.diagnosticsource/5.0.1", "hashPath": "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/5.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-xa8kptJ+uf9hzj366f3pLcs5HFZ6dQMDKzEGq/yZNF0s3mVfyIhuQwgDcTJlAU4AROne/6Z5+vITwrW3gVNKIA==", "path": "system.identitymodel.tokens.jwt/5.5.0", "hashPath": "system.identitymodel.tokens.jwt.5.5.0.nupkg.sha512"}, "System.IO.Hashing/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-Rfm2jYCaUeGysFEZjDe7j1R4x6Z6BzumS/vUT5a1AA/AWJuGX71PoGB0RmpyX3VmrGqVnAwtfMn39OHR8Y/5+g==", "path": "system.io.hashing/6.0.0", "hashPath": "system.io.hashing.6.0.0.nupkg.sha512"}, "System.Management/7.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-/qEUN91mP/MUQmJnM5y5BdT7ZoPuVrtxnFlbJ8a3kBJGhe2wCzBfnPFtK2wTtEEcf3DMGR9J00GZZfg6HRI6yA==", "path": "system.management/7.0.2", "hashPath": "system.management.7.0.2.nupkg.sha512"}, "System.Memory/4.5.5": {"type": "package", "serviceable": true, "sha512": "sha512-XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw==", "path": "system.memory/4.5.5", "hashPath": "system.memory.4.5.5.nupkg.sha512"}, "System.Memory.Data/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "path": "system.memory.data/1.0.2", "hashPath": "system.memory.data.1.0.2.nupkg.sha512"}, "System.Numerics.Vectors/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ==", "path": "system.numerics.vectors/4.5.0", "hashPath": "system.numerics.vectors.4.5.0.nupkg.sha512"}, "System.Reflection.Metadata/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-423hF/x1/1/aBT6hjgrp8RH2zdKOd1iTujlHisSesTW/cgv1ixUitfk23ZknVzItMm6jnwp9CBwI2P3r9jpitw==", "path": "system.reflection.metadata/1.5.0", "hashPath": "system.reflection.metadata.1.5.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/6.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/iUeP3tq1S0XdNNoMz5C9twLSrM/TH+qElHkXWaPvuNOt+99G75NrV0OS2EqHx5wMN7popYjpc8oTjC1y16DLg==", "path": "system.runtime.compilerservices.unsafe/6.0.0", "hashPath": "system.runtime.compilerservices.unsafe.6.0.0.nupkg.sha512"}, "System.Security.AccessControl/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2NRFPX/V81ucKQmqNgGBZrKGH/5ejsvivSGMRum0SMgPnJxwhuNkzVS1+7gC3R2X0f57CtwrPrXPPSe6nOp82g==", "path": "system.security.accesscontrol/4.4.0", "hashPath": "system.security.accesscontrol.4.4.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-WG3r7EyjUe9CMPFSs6bty5doUqT+q9pbI80hlNzo2SkPkZ4VTuZkGWjpp77JB8+uaL4DFPRdBsAY+DX3dBK92A==", "path": "system.security.cryptography.cng/4.5.0", "hashPath": "system.security.cryptography.cng.4.5.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Xubvo4i+K+DO6YzVh6vBKmCl5xx/cAoiJEze6VQ+XwVQU25KQC9pPrmniz2EbbJnmoQ5Rm2FFjHsfQAi0Rs+Q==", "path": "system.security.cryptography.xml/4.4.0", "hashPath": "system.security.cryptography.xml.4.4.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-pP+AOzt1o3jESOuLmf52YQTF7H3Ng9hTnrOESQiqsnl2IbBh1HInsAMHYtoh75iUYV0OIkHmjvveraYB6zM97w==", "path": "system.security.principal.windows/4.4.0", "hashPath": "system.security.principal.windows.4.4.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-iTUgB/WtrZ1sWZs84F2hwyQhiRH6QNjQv2DkwrH+WP6RoFga2Q1m3f9/Q7FG8cck8AdHitQkmkXSY8qylcDmuA==", "path": "system.text.encodings.web/4.7.2", "hashPath": "system.text.encodings.web.4.7.2.nupkg.sha512"}, "System.Text.Json/4.7.2": {"type": "package", "serviceable": true, "sha512": "sha512-TcMd95wcrubm9nHvJEQs70rC0H/8omiSGGpU4FQ/ZA1URIqD4pjmFJh2Mfv1yH1eHgJDWTi2hMDXwTET+zOOyg==", "path": "system.text.json/4.7.2", "hashPath": "system.text.json.4.7.2.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.5.4": {"type": "package", "serviceable": true, "sha512": "sha512-zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg==", "path": "system.threading.tasks.extensions/4.5.4", "hashPath": "system.threading.tasks.extensions.4.5.4.nupkg.sha512"}, "WindowsAzure.Storage/9.3.3": {"type": "package", "serviceable": true, "sha512": "sha512-VUV8j6dzkU/7vDgk5k+ob5g7nnch2fNByr0p9aOxMGFGk+tAnTehrZ4qnClF04CVozP1GNN2zrnbsxCmr+iZBg==", "path": "windowsazure.storage/9.3.3", "hashPath": "windowsazure.storage.9.3.3.nupkg.sha512"}, "Oberon.BusinessLogic/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Antiforgery/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Cookies/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authentication.OAuth.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Authorization.Policy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Authorization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Forms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Server/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Components.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Connections.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.CookiePolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.Internal.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Cryptography.KeyDerivation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.DataProtection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HostFiltering/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Hosting.Server.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Html.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Connections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Http.Features.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpOverrides.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.HttpsPolicy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Identity/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Localization.Routing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Metadata/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ApiExplorer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Cors/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Formatters.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.RazorPages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.TagHelpers/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Mvc.ViewFeatures/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Razor.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCaching/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.ResponseCompression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Rewrite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Routing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.HttpSys/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IIS/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.IISIntegration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Core.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.Session/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.SignalR.Protocols.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.StaticFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.AspNetCore.WebUtilities.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.CSharp.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Binder.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.CommandLine.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.EnvironmentVariables.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.FileExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Ini/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Json.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.KeyPerFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.UserSecrets.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Configuration.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Diagnostics.HealthChecks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Composite/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Embedded/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileProviders.Physical.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.FileSystemGlobbing.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting.Abstractions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Hosting/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Identity.Stores/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization.Abstractions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Localization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Configuration.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Console.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.Debug.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.EventSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Logging.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ObjectPool.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.ConfigurationExtensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.Options.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.WebEncoders.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.JSInterop/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Net.Http.Headers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic.Core/********": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.VisualBasic/10.0.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "Microsoft.Win32.Registry.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "mscorlib/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "netstandard/2.1.0.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.AppContext/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Buffers.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Concurrent/4.0.15.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.NonGeneric/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Collections.Specialized/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.DataAnnotations/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.EventBasedAsync/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ComponentModel.TypeConverter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Configuration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Console/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Core/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.Common/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data.DataSetExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Data/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Contracts/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Debug/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.EventLog/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.FileVersionInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Process/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.StackTrace/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TextWriterTraceListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tools/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.TraceSource/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Diagnostics.Tracing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Drawing.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Dynamic.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Calendars/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Globalization.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.Brotli/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Compression.ZipFile/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.DriveInfo/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.FileSystem.Watcher/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.IsolatedStorage/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.MemoryMappedFiles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipelines/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.Pipes/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.IO.UnmanagedMemoryStream/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Expressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Linq.Queryable/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Memory.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Http/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.HttpListener/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Mail/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NameResolution/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.NetworkInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Ping/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Requests/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.ServicePoint/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.Sockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebClient/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebHeaderCollection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets.Client/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Net.WebSockets/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Numerics.Vectors.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ObjectModel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.DispatchProxy/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.ILGeneration/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Emit.Lightweight/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Metadata.Reference/1.4.5.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Reflection.TypeExtensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Reader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.ResourceManager/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Resources.Writer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.CompilerServices.VisualC/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Handles/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.RuntimeInformation/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.InteropServices.WindowsRuntime/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Intrinsics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Loader/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Numerics/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Formatters/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Json/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Runtime.Serialization.Xml/4.1.5.0": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.AccessControl.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Claims/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Algorithms/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Cng.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Csp/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Encoding/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Primitives/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.X509Certificates/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Cryptography.Xml.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Permissions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.Principal.Windows.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Security.SecureString/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceModel.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ServiceProcess/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.CodePages/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.Encoding.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Text.RegularExpressions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Channels/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Overlapped/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Dataflow/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Extensions.Reference/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Tasks.Parallel/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Thread/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.ThreadPool/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Threading.Timer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Transactions.Local/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.ValueTuple/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Web.HttpUtility/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Windows.Extensions/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Linq/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.ReaderWriter/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.Serialization/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XmlSerializer/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "System.Xml.XPath.XDocument/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}, "WindowsBase/*******": {"type": "referenceassembly", "serviceable": false, "sha512": ""}}}