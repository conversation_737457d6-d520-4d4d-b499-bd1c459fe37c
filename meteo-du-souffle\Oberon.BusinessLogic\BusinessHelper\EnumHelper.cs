﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Oberon.BusinessLogic.BusinessHelper
{
    public static class EnumHelper
    {
        public static string GetDescription<TEnum>(this TEnum e)
        {
            var type = e.GetType();
            var name = Enum.GetName(type, e);

            if (name == null)
            {
                return e.ToString();
            }

            var field = type.GetField(name);

            if (field == null)
            {
                return e.ToString();
            }

            var attribute = Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute)) as DescriptionAttribute;
            var description = attribute != null ? attribute.Description : e.ToString();

            return description;
        }
    }
}
