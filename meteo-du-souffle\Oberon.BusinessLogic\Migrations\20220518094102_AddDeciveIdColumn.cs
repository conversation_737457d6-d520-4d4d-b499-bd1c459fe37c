﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class AddDeciveIdColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "DeciveId",
                table: "Signup",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "isAndriod",
                table: "Signup",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "DeciveId",
                table: "Signup");

            migrationBuilder.DropColumn(
                name: "isAndriod",
                table: "Signup");
        }
    }
}
