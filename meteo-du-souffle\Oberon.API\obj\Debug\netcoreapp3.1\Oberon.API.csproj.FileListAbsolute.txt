C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.json
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.exe
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.deps.json
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.json
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.dev.json
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.dll
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.dll
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.DataLogic.dll
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.pdb
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.DataLogic.pdb
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csprojAssemblyReference.cache
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfoInputs.cache
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfo.cs
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CoreCompileInputs.cache
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cache
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Manifest.cache
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.xml
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\scopedcss\bundle\Oberon.API.styles.css
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.RazorTargetAssemblyInfo.cache
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CopyComplete
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.dll
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Kiruthika\Projects\Oberon\Oberon.API\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.genruntimeconfig.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.json
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.exe
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.deps.json
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.json
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.dev.json
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\AspNetCore.Firebase.Authentication.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Firebase.Auth.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FireSharp.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.PlatformServices.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Humanizer.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Https.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.Desktop.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Design.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.Design.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Extensions.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Primitives.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ServiceModel.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Collections.Immutable.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Diagnostics.DiagnosticSource.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libuv.so
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libuv.so
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-armel\native\libuv.so
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libuv.so
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\osx\native\libuv.dylib
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-arm\native\libuv.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\libuv.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\libuv.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.pdb
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csprojAssemblyReference.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfoInputs.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfo.cs
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CoreCompileInputs.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cs
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Manifest.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.xml
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\scopedcss\bundle\Oberon.API.styles.css
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.RazorTargetAssemblyInfo.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CopyComplete
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.genruntimeconfig.cache
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\CorePush.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\private_key.json
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Core.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IO.Hashing.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Memory.Data.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Encodings.Web.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Json.dll
C:\Kiruthika\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csprojAssemblyReference.cache
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfoInputs.cache
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfo.cs
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CoreCompileInputs.cache
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cs
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cache
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.json
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\private_key.json
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.exe
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.deps.json
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.json
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.dev.json
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.pdb
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\AspNetCore.Firebase.Authentication.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Core.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\CorePush.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Firebase.Auth.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FireSharp.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.PlatformServices.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Humanizer.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Https.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.Desktop.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Design.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.Design.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Extensions.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Primitives.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ServiceModel.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Collections.Immutable.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Diagnostics.DiagnosticSource.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IO.Hashing.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Memory.Data.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Encodings.Web.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Json.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libuv.so
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libuv.so
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-armel\native\libuv.so
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libuv.so
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\osx\native\libuv.dylib
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-arm\native\libuv.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\libuv.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\libuv.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.pdb
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Manifest.cache
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.xml
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\scopedcss\bundle\Oberon.API.styles.css
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.RazorTargetAssemblyInfo.cache
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CopyComplete
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.dll
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.pdb
D:\Projects\Oberon\backend\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.genruntimeconfig.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.json
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\private_key.json
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.StaticWebAssets.xml
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.exe
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.deps.json
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.json
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.dev.json
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\AspNetCore.Firebase.Authentication.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Core.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\CorePush.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Firebase.Auth.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FireSharp.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.PlatformServices.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Humanizer.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Https.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.Desktop.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Design.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.Design.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Extensions.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Primitives.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ServiceModel.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Collections.Immutable.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Diagnostics.DiagnosticSource.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IO.Hashing.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Memory.Data.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Encodings.Web.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Json.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libuv.so
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libuv.so
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-armel\native\libuv.so
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libuv.so
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\osx\native\libuv.dylib
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-arm\native\libuv.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\libuv.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\libuv.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.pdb
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.AssemblyReference.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.GeneratedMSBuildEditorConfig.editorconfig
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfoInputs.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfo.cs
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CoreCompileInputs.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cs
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.RazorTargetAssemblyInfo.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Manifest.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Pack.cache
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.Oberon.API.Microsoft.AspNetCore.StaticWebAssets.props
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.Oberon.API.props
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.Oberon.API.props
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.Oberon.API.props
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.Up2Date
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.dll
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Oberon\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.genruntimeconfig.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.json
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\private_key.json
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.StaticWebAssets.xml
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.exe
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.deps.json
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.json
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.dev.json
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\AspNetCore.Firebase.Authentication.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Core.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\CorePush.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Firebase.Auth.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FireSharp.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.PlatformServices.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Humanizer.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Https.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.Desktop.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.Design.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Extensions.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Primitives.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ServiceModel.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Collections.Immutable.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IO.Hashing.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Memory.Data.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Encodings.Web.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Json.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libuv.so
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libuv.so
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-armel\native\libuv.so
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libuv.so
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\osx\native\libuv.dylib
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-arm\native\libuv.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\libuv.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\libuv.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.pdb
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.AssemblyReference.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfoInputs.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfo.cs
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.RazorTargetAssemblyInfo.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Manifest.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Pack.cache
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.Oberon.API.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.Oberon.API.props
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.Oberon.API.props
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.Oberon.API.props
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.Up2Date
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.dll
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Users\<USER>\Downloads\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.genruntimeconfig.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\meteo-du-souffle-firebase-adminsdk-e5xql-2c21b70c21.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\private_key.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.exe
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.deps.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.dev.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\AspNetCore.Firebase.Authentication.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Core.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\CorePush.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Firebase.Auth.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FireSharp.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Humanizer.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Https.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.Desktop.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Design.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.Design.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Extensions.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Primitives.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ServiceModel.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.CodeDom.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Collections.Immutable.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Diagnostics.DiagnosticSource.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IO.Hashing.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Management.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Memory.Data.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Runtime.CompilerServices.Unsafe.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Encodings.Web.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Json.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libuv.so
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libuv.so
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-armel\native\libuv.so
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libuv.so
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\osx\native\libuv.dylib
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-arm\native\libuv.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\libuv.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\libuv.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.pdb
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.AssemblyReference.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfoInputs.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfo.cs
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CoreCompileInputs.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cs
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.RazorTargetAssemblyInfo.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Manifest.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.dll
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.genruntimeconfig.cache
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Properties\launchSettings.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Properties\ServiceDependencies\oberondev - Web Deploy1\profile.arm.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Properties\ServiceDependencies\oberondev - Web Deploy\profile.arm.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Properties\ServiceDependencies\OberonProd - Zip Deploy4\profile.arm.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Properties\ServiceDependencies\OberonProd - Zip Deploy5\profile.arm.json
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CopyComplete
C:\Oberon Backup08-08-24\BE\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.xml
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.Development.json
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\appsettings.json
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\meteo-du-souffle-firebase-adminsdk-e5xql-2c21b70c21.json
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\private_key.json
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.StaticWebAssets.xml
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.exe
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.deps.json
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.json
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.runtimeconfig.dev.json
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\AspNetCore.Firebase.Authentication.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Core.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Blobs.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Azure.Storage.Common.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\CorePush.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Firebase.Auth.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FirebaseAdmin.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\FireSharp.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Api.Gax.Rest.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Auth.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Google.Apis.Core.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Humanizer.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Authentication.JwtBearer.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Https.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.Desktop.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.Extensions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Threading.Tasks.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Bcl.AsyncInterfaces.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Abstractions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Design.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.EntityFrameworkCore.Relational.Design.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Abstractions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Caching.Memory.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Configuration.Abstractions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.DependencyInjection.Abstractions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Logging.Abstractions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Options.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.Extensions.Primitives.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.JsonWebTokens.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Logging.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.IdentityModel.Tokens.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Extensions.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Net.Http.Primitives.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ServiceModel.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.OpenApi.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Newtonsoft.Json.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\BouncyCastle.Crypto.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.Swagger.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerGen.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Swashbuckle.AspNetCore.SwaggerUI.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.CodeDom.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Collections.Immutable.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.ComponentModel.Annotations.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Diagnostics.DiagnosticSource.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IdentityModel.Tokens.Jwt.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.IO.Hashing.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Management.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Memory.Data.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Runtime.CompilerServices.Unsafe.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Encodings.Web.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\System.Text.Json.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Microsoft.WindowsAzure.Storage.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm\native\libuv.so
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-arm64\native\libuv.so
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-armel\native\libuv.so
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\linux-x64\native\libuv.so
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\osx\native\libuv.dylib
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-arm\native\libuv.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x64\native\libuv.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\runtimes\win-x86\native\libuv.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\bin\Debug\netcoreapp3.1\Oberon.BusinessLogic.pdb
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.AssemblyReference.cache
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.GeneratedMSBuildEditorConfig.editorconfig
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfoInputs.cache
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.AssemblyInfo.cs
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.CoreCompileInputs.cache
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cs
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.MvcApplicationPartsAssemblyInfo.cache
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.RazorTargetAssemblyInfo.cache
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Manifest.cache
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\Oberon.API.StaticWebAssets.Pack.cache
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.Oberon.API.Microsoft.AspNetCore.StaticWebAssets.props
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.build.Oberon.API.props
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildMultiTargeting.Oberon.API.props
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\staticwebassets\msbuild.buildTransitive.Oberon.API.props
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.csproj.Up2Date
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.dll
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.pdb
C:\Users\<USER>\Documents\Project\BE\meteo-du-souffle\Oberon.API\obj\Debug\netcoreapp3.1\Oberon.API.genruntimeconfig.cache
