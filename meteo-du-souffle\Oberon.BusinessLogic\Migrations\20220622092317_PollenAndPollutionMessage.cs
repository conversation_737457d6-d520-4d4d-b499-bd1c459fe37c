﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;
using Npgsql.EntityFrameworkCore.PostgreSQL.Metadata;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class PollenAndPollutionMessage : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "AdvicesPollenId",
                table: "Advices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AdvicesResProblemId",
                table: "Advices",
                type: "text",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "PollenMessage",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Message = table.Column<string>(type: "text", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PollenMessage", x => x.Id);
                });

            migrationBuilder.CreateTable(
                name: "PollutionMessage",
                columns: table => new
                {
                    Id = table.Column<int>(type: "integer", nullable: false)
                        .Annotation("Npgsql:ValueGenerationStrategy", NpgsqlValueGenerationStrategy.IdentityByDefaultColumn),
                    Message = table.Column<string>(type: "text", nullable: true),
                    CreatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false),
                    UpdatedDate = table.Column<DateTime>(type: "timestamp without time zone", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_PollutionMessage", x => x.Id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "PollenMessage");

            migrationBuilder.DropTable(
                name: "PollutionMessage");

            migrationBuilder.DropColumn(
                name: "AdvicesPollenId",
                table: "Advices");

            migrationBuilder.DropColumn(
                name: "AdvicesResProblemId",
                table: "Advices");
        }
    }
}
