﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class PdffileDetailsNewcolumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TempFileName",
                table: "PdfFileUpload",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "TempFilePath",
                table: "PdfFileUpload",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TempFileName",
                table: "PdfFileUpload");

            migrationBuilder.DropColumn(
                name: "TempFilePath",
                table: "PdfFileUpload");
        }
    }
}
