﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class AddingColumnInUserFeedBack : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "IsEternuements",
                table: "UserFeedBack",
                newName: "IsStuffyNose");

            migrationBuilder.AddColumn<bool>(
                name: "IsAsthAttack",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "Is<PERSON>ry<PERSON><PERSON><PERSON>",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsDifficultyBreathing",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "<PERSON>Itch",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSnee<PERSON>",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSorethroat",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsAsthAttack",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsCryIngeyes",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsDifficultyBreathing",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsItch",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsSneezes",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsSorethroat",
                table: "UserFeedBack");

            migrationBuilder.RenameColumn(
                name: "IsStuffyNose",
                table: "UserFeedBack",
                newName: "IsEternuements");
        }
    }
}
