﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oberon.API.Handlers;
using Oberon.API.Helper;
using Oberon.BusinessLogic.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using Oberon.BusinessLogic.BusinessHelper;
using Oberon.BusinessLogic.DBContext;
using System.Collections;

namespace Oberon.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class UserManagementController : ControllerBase
    {
        private readonly UserDBContext _context;
        private readonly IAzureBlobConnectionHandler _azureBlobConnection;
        AdminLoginHelper _adminHelper;
        UserBusinessHelper _userBusinessHelper;
        FirebaseHelper _fireBaseHelper;
        EncryptionDecryptionHelper _encryption;
        public UserManagementController(UserDBContext context, IAzureBlobConnectionHandler azureBlobConnection)
        {
            _context = context;
            _adminHelper = new AdminLoginHelper();
            _userBusinessHelper = new UserBusinessHelper();
            _fireBaseHelper = new FirebaseHelper();
            _encryption = new EncryptionDecryptionHelper();
            _azureBlobConnection = azureBlobConnection;
        }

        #region Login API
        [HttpPost]
        [Route("validateLogin")]
        public async Task<IActionResult> Login(UserInfo model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var response = await _fireBaseHelper.FirebaseUserLoginValidateAsync(model.EmailId, model.Password);
                if (response.Status)
                {
                    var user = await _context.Signup.Where(u => u.Email == model.EmailId && u.UserType == 2).FirstOrDefaultAsync();
                    DeviceInfo deviceInfo = new DeviceInfo()
                    {
                        UserId = user.Id,
                        Email = user.Email,
                        Devices = model.DeciveInfo,
                        Createddate = DateTime.Now,
                        UpdatedBy = user.Id
                    };
                    _context.Add(deviceInfo);
                    _context.SaveChanges();

                    if(model.DeciveId != null)
                    {
                        user.DeciveId = model.DeciveId;
                        user.isAndriod = model.isAndriod;
                        user.GPSZipCode = model.GPSZipCode;
                        _context.Update(user);
                        _context.SaveChanges();
                    }
                    var profilepath = (from s in _context.ProfileInfo
                                       where s.UserId == user.Id
                                       select s).FirstOrDefault();
                    var output = new
                    {
                        userId = user.Id,
                        name = user.Name,
                        email = user.Email,
                        //token = response.Token,
                        firstName = user.FirstName,
                        yearofBirth = user.YearofBirth,
                        gender = user.Gender,
                        isSmoking = user.IsSmoking,
                        isNewsfromApp = user.IsNewsFromApp,
                        isTermsandconditions = user.IstermAndConditions,
                        createdby = user.createdBy,
                        updatedby = user.UpdatedBy,
                        createdDate = user.Createddate, 
                        updatedDate = user.UpdatedDate,
                        zipCode = user.ZipCode,
                        resProblemId = (user.ResProblemId == "" || user.ResProblemId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : user.ResProblemId.Split(',').Select(Int32.Parse).ToList(),
                        pollenId = (user.PollenId == "" || user.PollenId == null ) ? ("0").Split(',').Select(Int32.Parse).ToList() :  user.PollenId.Split(',').Select(Int32.Parse).ToList(),
                        NotificationFor = (user.NotificationFor == "" || user.NotificationFor == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : user.NotificationFor.Split(',').Select(Int32.Parse).ToList(),
                        //refreshtoken = response.RefreshToken,
                        profilepath = profilepath != null ? profilepath.FilePath : ""
                    };

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LOGIN.GetDescription();
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = response.Message == "EMAIL_NOT_FOUND" ? ResponseMessageHelper.VALIDATEEMAIL.GetDescription() : 
                                           response.Message == "INVALID_PASSWORD" ? ResponseMessageHelper.VALIDATEPASSWORD.GetDescription() : 
                                           response.Message == "INVALID_EMAIL" ? ResponseMessageHelper.INVAILDEMAIL.GetDescription() : ResponseMessageHelper.VALIDATEEMAILANDPASSWORD.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region  Registration API
        [HttpPost]
        [Route("registration")]
        public async Task<IActionResult> Registration(UserModel model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var response = await _fireBaseHelper.RegisterUserToFirebaseAsync(model.Email, model.Password);
                if (response.Status)
                {
                    User user = new User()
                    {
                        Id = model.Id,
                        Uid = response.Uid,
                        Email = model.Email,
                        Name = model.Name,
                        FirstName = model.FirstName,
                        Password = _encryption.Encrypt(model.Password),
                        YearofBirth = model.YearofBirth,
                        Gender = model.Gender,
                        IsSmoking = model.IsSmoking,
                        Createddate = model.Createddate,
                        createdBy = model.createdBy,
                        UpdatedDate = model.UpdatedDate,
                        UpdatedBy = model.UpdatedBy,
                        MultiFactorAuth = model.MultiFactorAuth,
                        IsNewsFromApp = model.IsNewsFromApp,
                        IstermAndConditions = model.IstermAndConditions,
                        ResProblemId = string.Join(",", model.ResProblemId.ToArray()),
                        PollenId = string.Join(",", model.PollenId.ToArray()),
                        ZipCode = model.ZipCode,
                        UserType = 2,
                        NotificationFor = string.Join(",", model.NotificationFor.ToArray())                        
                    };
                    _context.Add(user);
                    _context.SaveChanges();
                    var data = _context.Signup.OrderBy(x => x.Id).LastOrDefault();
                    var profilepath = (from s in _context.ProfileInfo
                                       where s.UserId == data.Id
                                       select s).FirstOrDefault();
                    var output = new
                    {
                        userId = data.Id,
                        name = data.Name,
                        email = data.Email,
                        firstName = data.FirstName,
                        yearofBirth = data.YearofBirth,
                        gender = data.Gender,
                        isSmoking = data.IsSmoking,
                        isNewsfromApp = data.IsNewsFromApp,
                        isTermsandconditions = data.IstermAndConditions,
                        createdby = data.createdBy,
                        updatedby = data.UpdatedBy,
                        createdDate = data.Createddate,
                        updatedDate = data.UpdatedDate,
                        zipCode = data.ZipCode,
                        resProblemId = (data.ResProblemId == "" || data.ResProblemId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : data.ResProblemId.Split(',').Select(Int32.Parse).ToList(),
                        pollenId = (data.PollenId == "" || data.PollenId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : data.PollenId.Split(',').Select(Int32.Parse).ToList(),
                        profilepath = profilepath != null ? profilepath.FilePath : "",
                        notificationFor = (data.NotificationFor == "" || data.NotificationFor == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : data.NotificationFor.Split(',').Select(Int32.Parse).ToList(),
                        refreshtoken = ""
                    };
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.REGISTRATION.GetDescription();
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = response.Message == "EMAIL_EXISTS" ? ResponseMessageHelper.EMAILEXISTS.GetDescription() : response.Message;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Forgot Password
        [HttpPost]
        [Route("forgotpassword")]
        public async Task<IActionResult> ForgotPassword(ForgotPasswordModel model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var response = await _fireBaseHelper.FirebaseResetPasswordAsync(model.EmailId);
                if (response.Status)
                {
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.RESETPASSWORD.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.VALIDATEEMAIL.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.VALIDATEEMAIL.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region AdminLogin API
        [HttpPost]
        [Route("adminLogin")]
        public async Task<IActionResult> AdminLogin(UserInfo model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                if (model.EmailId == _adminHelper.AdminEmailId)
                {
                    var firebaseresponse = await _fireBaseHelper.FirebaseUserLoginValidateAsync(model.EmailId, model.Password);
                    if (firebaseresponse.Status)
                    {
                        var response = await _context.Signup.Where(u => u.Email == model.EmailId && u.UserType == 1).FirstOrDefaultAsync();
                        DeviceInfo deviceInfo = new DeviceInfo()
                        {
                            UserId = response.Id,
                            Email = response.Email,
                            Devices = model.DeciveInfo,
                            Createddate = DateTime.Now,
                            UpdatedBy = response.Id
                        };
                        _context.Add(deviceInfo);
                        _context.SaveChanges();

                        var output = new
                        {
                            userId = response.Id,
                            name = response.Name,
                            email = response.Email,
                            //token = firebaseresponse.Token,
                            firstName = response.FirstName,
                            yearofBirth = response.YearofBirth,
                            gender = response.Gender,
                            isSmoking = response.IsSmoking,
                            createdby = response.createdBy,
                            updatedby = response.UpdatedBy,
                            createdDate = response.Createddate,
                            updatedDate = response.UpdatedDate
                            //,refreshtoken = firebaseresponse.RefreshToken
                        };

                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.LOGIN.GetDescription();
                        responseData.Data = output;
                        result = StatusCode((int)HttpStatusCode.OK, responseData);
                    }
                    else
                    {
                        responseData.Status = true;
                        responseData.Message = firebaseresponse.Message == "INVALID_PASSWORD" ? ResponseMessageHelper.VALIDATEPASSWORD.GetDescription() :firebaseresponse.Message;
                        result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                    }                
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.ADMINVALIDATE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }          
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region ProfileImage Delete
        [HttpDelete]
        [Route("profiledelete")]
        public async Task<IActionResult> ProfileDelete(int userId)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "profile-pic";
                //Database existing check code here
                var user = await _context.ProfileInfo.Where(u => u.UserId == userId).FirstOrDefaultAsync();
                if (user != null)
                {
                    //Delete file from Azure function call
                    var response = await blobStorage.DeleteFileAsync(user.FileName, containerName);
                    if (response)
                    {
                        //Delete data from DB
                        _context.ProfileInfo.Remove(user);
                        _context.SaveChanges();
                    }
                }

                ProfileInfo profileInfo = new ProfileInfo()
                {
                    FileName = "",
                    FilePath = "",
                    UpdatedDate = DateTime.Now
                };
                _context.Update(profileInfo);
                _context.SaveChanges();

                responseData.Status = true;
                responseData.Message = ResponseMessageHelper.PROFILEUPLOADSUCCESS.GetDescription();
                responseData.Data = null;
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Users List
        //[Authorize]
        [HttpPost]
        [Route("userslist")]
        public async Task<IActionResult> UsersList(UserList model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<User> userlistResponse = new List<User>();
            try
            {
                
                userlistResponse = await _context.Signup.ToListAsync();

                var response = (from s in userlistResponse
                                where (model.searchId != 0 ? s.Id.ToString().Trim().Contains(model.searchId.ToString().Trim()) : s.Name != "") && 
                                (model.searchFirstName != "" ? s.Name.ToLower().Trim().Contains(model.searchFirstName.ToLower().Trim()) : s.Name != "") &&
                                (model.searchLastName != "" ? s.FirstName.ToLower().Trim().Contains(model.searchLastName.ToLower().Trim()) : s.FirstName != "") &&
                                (model.searchEmail != "" ? s.Email.ToLower().Trim().Contains(model.searchEmail.ToLower().Trim()) : s.Email != "") &&
                                (model.searchYOB != 0 ? s.YearofBirth.ToString().Contains(model.searchYOB.ToString()) : s.YearofBirth != 0) &&
                                (model.searchGender != 0 ? s.Gender == model.searchGender : s.Gender != 0) &&
                                (model.searchSmoker != "" ? (model.searchSmoker == "Yes".ToLower().Trim() ? s.IsSmoking == true : s.IsSmoking == false) : s.IsSmoking == true || s.IsSmoking == false) &&
                                (model.searchzipCode != "" ? s.ZipCode.ToString().Contains(model.searchzipCode.ToString()) : s.ZipCode != 0) &&
                                (model.searchRespProb != "" ? s.ResProblemId.Split(',').Any(x=>model.searchRespProb.Split(',').Contains(x)) : s.ResProblemId != "") &&
                                (model.searchPollen != "" ? s.PollenId.Split(',').Any(x=>model.searchPollen.Split(',').Contains(x)) : s.PollenId != "")&& (s.UserType == 2)
                                select s).ToList();

                if (response.Count == 0 || response == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else 
                {
                    List<UserModel> groupUserDataList = new List<UserModel>();
                    model.pageNumber = model.pageNumber == 0 ? 1 : model.pageNumber;
                    model.pageSize = model.pageSize == 0 ? 5 : model.pageSize;
                    var userData = _userBusinessHelper.ResponseSortHelper(model.sortBy, response);
                    var userlist = userData.Skip((model.pageNumber - 1) * model.pageSize).Take(model.pageSize).ToList();
                    foreach (var item in userlist)
                    {
                        UserModel groupUser = new UserModel();
                        groupUser.Id = item.Id;
                        groupUser.Name = item.Name;
                        groupUser.Email = item.Email;
                        groupUser.FirstName = item.FirstName;
                        groupUser.YearofBirth = item.YearofBirth;
                        groupUser.Gender = item.Gender;
                        groupUser.IsSmoking = item.IsSmoking;
                        groupUser.IsNewsFromApp = item.IsNewsFromApp;
                        groupUser.IstermAndConditions = item.IstermAndConditions;
                        groupUser.createdBy = item.createdBy;
                        groupUser.UpdatedBy = item.UpdatedBy;
                        groupUser.Createddate = item.Createddate;
                        groupUser.UpdatedDate = item.UpdatedDate;
                        groupUser.ZipCode = item.ZipCode;
                        groupUser.ResProblemId = (item.ResProblemId == "" || item.ResProblemId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : item.ResProblemId.Split(',').Select(Int32.Parse).ToList();
                        groupUser.PollenId = (item.PollenId == "" || item.PollenId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : item.PollenId.Split(',').Select(Int32.Parse).ToList();
                        groupUser.NotificationFor = (item.NotificationFor == "" || item.NotificationFor == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : item.NotificationFor.Split(',').Select(Int32.Parse).ToList();
                        groupUserDataList.Add(groupUser);
                    }                        
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    responseData.Data = groupUserDataList;
                    responseData.TotalCount = response.Count;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Get User By Id
        //[Authorize]
        [HttpGet]
        [Route("userbyid")]
        public  IActionResult GetUser(int id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var User = _context.Find<User>(id);
                if (User == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                    responseData.Data = User;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    var profilepath = (from s in _context.ProfileInfo
                                       where s.UserId == User.Id
                                       select s).FirstOrDefault();
                    var output = new
                    {
                        userId = User.Id,
                        name = User.Name,
                        email = User.Email,
                        firstName = User.FirstName,
                        yearofBirth = User.YearofBirth,
                        gender = User.Gender,
                        isSmoking = User.IsSmoking,
                        isNewsfromApp = User.IsNewsFromApp,
                        isTermsandconditions = User.IstermAndConditions,
                        createdby = User.createdBy,
                        updatedby = User.UpdatedBy,
                        createdDate = User.Createddate,
                        updatedDate = User.UpdatedDate,
                        zipCode = User.ZipCode,
                        resProblemId = (User.ResProblemId == "" || User.ResProblemId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : User.ResProblemId.Split(',').Select(Int32.Parse).ToList(),                        
                        pollenId = (User.PollenId == "" || User.PollenId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : User.PollenId.Split(',').Select(Int32.Parse).ToList(),
                        NotificationFor = (User.NotificationFor == "" || User.NotificationFor == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : User.NotificationFor.Split(',').Select(Int32.Parse).ToList(),
                        profilepath = profilepath != null ? profilepath.FilePath : "",
                        refreshtoken = ""
                    };
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region  User Update
        //[Authorize]
        [HttpPost]
        [Route("userupdate")]
        public IActionResult UserUpdate(UserUpdate model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                if (model.Id == 0)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                    
                }
                else
                {
                    var userdata = _context.Find<User>(model.Id);
                    if (userdata == null)
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                        result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                    }
                    else
                    {
                        userdata.Name = model.Name;
                        userdata.FirstName = model.FirstName;
                        userdata.YearofBirth = model.YearofBirth;
                        userdata.IsSmoking = model.IsSmoking;
                        userdata.Gender = model.Gender;
                        userdata.UpdatedBy = model.UpdatedBy;
                        userdata.UpdatedDate = DateTime.Now;
                        userdata.IsNewsFromApp = model.IsNewsFromApp;
                        userdata.IstermAndConditions = model.IstermAndConditions;
                        userdata.ZipCode = model.ZipCode;
                        userdata.ResProblemId = string.Join(",",model.ResProblemId.ToArray());
                        userdata.PollenId = string.Join(",", model.PollenId.ToArray());
                        userdata.NotificationFor = string.Join(",", model.NotificationFor.ToArray());
                        _context.Update(userdata);
                        _context.SaveChanges();

                        var data = _context.Find<User>(model.Id);
                        var profilepath = (from s in _context.ProfileInfo
                                           where s.UserId == data.Id
                                           select s).FirstOrDefault();
                        var output = new
                        {
                            userId = data.Id,
                            name = data.Name,
                            email = data.Email,
                            firstName = data.FirstName,
                            yearofBirth = data.YearofBirth,
                            gender = data.Gender,
                            isSmoking = data.IsSmoking,                            
                            isNewsfromApp = data.IsNewsFromApp,
                            isTermsandconditions = data.IstermAndConditions,
                            createdby = data.createdBy,
                            updatedby = data.UpdatedBy,
                            createdDate = data.Createddate,
                            updatedDate = data.UpdatedDate,
                            zipCode = data.ZipCode,
                            resProblemId = (data.ResProblemId == "" || data.ResProblemId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : data.ResProblemId.Split(',').Select(Int32.Parse).ToList(),
                            pollenId = (data.PollenId == "" || data.PollenId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : data.PollenId.Split(',').Select(Int32.Parse).ToList(),
                            NotificationFor = (data.NotificationFor == "" || data.NotificationFor == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : data.NotificationFor.Split(',').Select(Int32.Parse).ToList(),
                            profilepath = profilepath != null ? profilepath.FilePath : "",
                            refreshtoken = ""
                        };

                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                        responseData.Data = output;
                        result = StatusCode((int)HttpStatusCode.OK, responseData);
                    }
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region DropDown List
        [HttpGet]
        [Route("getdropdownlist")]
        public async Task<IActionResult> GetDropDownList()
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                Hashtable resultSet = new Hashtable();
                var respiratoryProblemsList = await _context.RespiratoryProblems.ToListAsync();
                var pollenList = await _context.PollenList.OrderBy(x => x.Id).ToListAsync();
                var customNotificationList = await _context.CustomNotifications.OrderBy(x => x.Id).ToListAsync();

                resultSet.Add("respiratoryProblemsList", respiratoryProblemsList);
                resultSet.Add("pollenList", pollenList);
                resultSet.Add("customNotificationList", customNotificationList);

                responseData.Status = true;
                responseData.Data = resultSet;
                responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Refresh Token
        [HttpPost]
        [Route("refreshtoken")]
        public async Task<IActionResult> GetRefreshToken(string refreshtoken)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var response = await _fireBaseHelper.RefreshTokenAsync(refreshtoken);
                if (response.Status)
                {
                    responseData.Status = true;
                    responseData.Data = response.Token;
                    responseData.Message = ResponseMessageHelper.NEWTOKEN.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = response.Message;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Update User GPSZipCode
        //[Authorize]
        [HttpGet]
        [Route("updateGPSZipcode")]
        public IActionResult UpdateGPSZipCode(int id,long gpsZipCode)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var User = _context.Find<User>(id);
                if (User == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                    responseData.Data = User;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
                else
                {                    
                    User.GPSZipCode = gpsZipCode;
                    _context.Update(User);
                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.GPSZIPCODE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Logout
        //[Authorize]
        [HttpGet]
        [Route("logout")]
        public IActionResult Logout(int id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var User = _context.Find<User>(id);
                if (User == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                    responseData.Data = User;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
                else
                {
                    User.DeciveId = "";
                    _context.Update(User);
                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LOGOUT.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region DeleteAccount
        [HttpDelete]
        [Route("deleteaccount")]
        public async Task<IActionResult> Delete(int id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                //var user = (from s in _context.Signup where s.Id == id select s).FirstOrDefault();
                //var res = await _fireBaseDelete.DeleteUserToFirebaseAsync(user.Email);
                var response = await _context.Signup.Where(x => x.Id == id).FirstOrDefaultAsync();
                if (response != null)
                {
                    _context.Signup.Remove(response);
                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.DELETE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion


    }
}
