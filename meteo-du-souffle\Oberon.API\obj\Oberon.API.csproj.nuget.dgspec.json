{"format": 1, "restore": {"C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.API\\Oberon.API.csproj": {}}, "projects": {"C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.API\\Oberon.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.API\\Oberon.API.csproj", "projectName": "Oberon.API", "projectPath": "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.API\\Oberon.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {"C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.BusinessLogic\\Oberon.BusinessLogic.csproj": {"projectPath": "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.BusinessLogic\\Oberon.BusinessLogic.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AspNetCore.Firebase.Authentication": {"target": "Package", "version": "[2.0.1, )"}, "Azure.Storage.Blobs": {"target": "Package", "version": "[12.19.1, )"}, "CorePush": {"target": "Package", "version": "[3.0.10, )"}, "FireSharp": {"target": "Package", "version": "[2.0.4, )"}, "Firebase.Auth": {"target": "Package", "version": "[1.0.0, )"}, "FirebaseAdmin": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.AspNetCore.Authentication.JwtBearer": {"target": "Package", "version": "[3.1.32, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.16, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.32, )"}, "Microsoft.NETCore.App": {"target": "Package", "version": "[2.2.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[5.0.10, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design": {"target": "Package", "version": "[1.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.Swagger": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[6.3.1, )"}, "WindowsAzure.Storage": {"target": "Package", "version": "[9.3.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}, "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.BusinessLogic\\Oberon.BusinessLogic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.BusinessLogic\\Oberon.BusinessLogic.csproj", "projectName": "Oberon.BusinessLogic", "projectPath": "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.BusinessLogic\\Oberon.BusinessLogic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Users\\<USER>\\Desktop\\Test\\BE\\meteo-du-souffle\\Oberon.BusinessLogic\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"AspNetCore.Firebase.Authentication": {"target": "Package", "version": "[2.0.1, )"}, "FireSharp": {"target": "Package", "version": "[2.0.4, )"}, "Firebase.Auth": {"target": "Package", "version": "[1.0.0, )"}, "FirebaseAdmin": {"target": "Package", "version": "[3.2.0, )"}, "Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.16, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[3.1.32, )"}, "Microsoft.NETCore.App": {"target": "Package", "version": "[2.2.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[5.0.10, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design": {"target": "Package", "version": "[1.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.Swagger": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[6.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300\\RuntimeIdentifierGraph.json"}}}}}