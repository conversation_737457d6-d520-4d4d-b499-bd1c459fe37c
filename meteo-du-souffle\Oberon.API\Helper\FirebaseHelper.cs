﻿using System.Threading.Tasks;
using Firebase.Auth;
using Oberon.BusinessLogic.Model;
using Oberon.BusinessLogic.BusinessHelper;
using Newtonsoft.Json;
using FirebaseAdmin;
using Google.Apis.Auth.OAuth2;
using System.Collections.Generic;

namespace Oberon.API.Helper
{
    public class FirebaseHelper
    {
        FirebaseAuthProvider _auth;
        EncryptionDecryptionHelper _encryption;
        public FirebaseHelper()
        {
            _auth = new FirebaseAuthProvider(new FirebaseConfig("AIzaSyAVdHwIXovp3e_Tsoa2M5QthioEIFGxvGE"));
            _encryption = new EncryptionDecryptionHelper();
        }

        #region Firebase Registration
        public async Task<FirebaseResponse> RegisterUserToFirebaseAsync(string email, string password)
        {
            FirebaseResponse responseData = new FirebaseResponse();
            try
            {
                var response = await _auth.CreateUserWithEmailAndPasswordAsync(email, password);
                await _auth.SendEmailVerificationAsync(response);
                responseData.Uid = response.User.LocalId;
                responseData.Status = true;
                return responseData;
            }
            catch (FirebaseAuthException ex)
            {
                var firebaseEx = JsonConvert.DeserializeObject<FirebaseError>(ex.ResponseData);
                responseData.Message = firebaseEx.error.message;
                responseData.Status = false;
                return responseData;
            }
        }
        #endregion

        #region Login API
        public async Task<FirebaseResponse> FirebaseUserLoginValidateAsync(string email, string password)
        {
            FirebaseResponse responseData = new FirebaseResponse();

            try
            {
                var fbAuthLink = await _auth.SignInWithEmailAndPasswordAsync(email, password);
                var user = await _auth.GetUserAsync(fbAuthLink.FirebaseToken);
                if (user.IsEmailVerified)
                {
                    //var refreshtoken = _encryption.Encrypt(user.LocalId);
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LOGIN.GetDescription();
                    responseData.UserDetail = user;
                    //responseData.Token = fbAuthLink.FirebaseToken;
                    //responseData.RefreshToken = refreshtoken;
                    return responseData;
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.VERIFIYEMAIL.GetDescription();
                    return responseData;
                }
            }
            catch (FirebaseAuthException ex)
            {
                var firebaseEx = JsonConvert.DeserializeObject<FirebaseError>(ex.ResponseData);
                responseData.Status = false;
                responseData.Message = firebaseEx.error.message;
                return responseData;
            }
        }
        #endregion

        #region Firebase Reset Password
        public async Task<FirebaseResponse> FirebaseResetPasswordAsync(string email)
        {
            FirebaseResponse responseData = new FirebaseResponse();

            try
            {
                await _auth.SendPasswordResetEmailAsync(email);
                responseData.Status = true;
                return responseData;                
            }
            catch (FirebaseAuthException ex)
            {
                var firebaseEx = JsonConvert.DeserializeObject<FirebaseError>(ex.ResponseData);
                responseData.Status = false;
                responseData.Message = firebaseEx.error.message;
                return responseData;
            }
        }
        #endregion

        #region Firebase Refresh token
        public async Task<FirebaseResponse> RefreshTokenAsync(string token)
        {
            FirebaseResponse responseData = new FirebaseResponse();
            try
            {
                if (FirebaseApp.DefaultInstance == null)
                {
                    FirebaseApp.Create(new AppOptions()
                    {
                        Credential = GoogleCredential.FromFile("meteo-du-souffle-firebase-adminsdk-e5xql-2c21b70c21.json")
                    });
                }
                var auth = FirebaseAdmin.Auth.FirebaseAuth.DefaultInstance;
                var uid = _encryption.Decrypt(token);
                var additionalClaims = new Dictionary<string, object>()
                        {
                            { "premiumAccount", true },
                        };

                string customToken = await auth.CreateCustomTokenAsync(uid, additionalClaims);
                var response = await _auth.SignInWithCustomTokenAsync(customToken);
                responseData.Status = true;
                responseData.Token = response.FirebaseToken;
                return responseData;
            }
            catch (FirebaseAuthException ex)
            {
                var firebaseEx = JsonConvert.DeserializeObject<FirebaseError>(ex.ResponseData);
                responseData.Status = false;
                responseData.Message = firebaseEx.error.message;
                return responseData;
            }
        }
        #endregion
    }
}
