"use strict";(self.webpackChunkMeteodusouffle=self.webpackChunkMeteodusouffle||[]).push([[466],{2466:(rt,T,c)=>{c.r(T),c.d(T,{AdvicesModule:()=>at});var d=c(9808),r=c(3075),l=c(8744),t=c(5e3),x=c(520),O=c(1135),w=c(8345);let g=(()=>{class n{constructor(i,e){this.http=i,this.config=e,this.httpOptions={headers:new x.WM({"Content-Type":"application/json"})},this.isStartWatched=new O.X(!1)}getList(i){return this.http.post(this.config.adviceList,JSON.stringify(i),this.httpOptions)}getViewList(i){return this.http.post(this.config.adviceViewList+"/"+i.adviceId,this.httpOptions)}update(i){return this.http.post(this.config.updateAdvice,JSON.stringify(i),this.httpOptions)}updatepollen(i){return this.http.get(this.config.updatepollen+"?message="+i)}updatefile(i){const e=new FormData;return e.append("name",i.name),e.append("FileName",i.FileName),e.append("File",i.File),this.http.post(this.config.updatefile,e)}updatepollution(i){return this.http.get(this.config.updatepollution+"?message="+i)}getDetail(i){return this.http.get(this.config.getAdviceDetail+"?id="+i)}getlistPollenpollution(){return this.http.get(this.config.getpollenPollutionList)}deleteAdvice(i){return this.http.delete(this.config.deleteAdvice+"/"+i)}getDropdown(){return this.http.get(this.config.getDropdown)}getpollen(){return this.http.get(this.config.getpollen)}getpollution(){return this.http.get(this.config.getpollution)}getNotification(i){return this.http.post(this.config.notification,JSON.stringify(i),this.httpOptions)}getSponsorDetail(i){return this.http.get(this.config.getSponsorDetail+"?id="+i)}sponsorGetList(){return this.http.get(this.config.sponsorList)}updateSponsor(i){return this.http.post(this.config.updateSponsor,JSON.stringify(i),this.httpOptions)}deleteSponsor(i){return this.http.delete(this.config.sponsorDelete+"/"+i)}}return n.\u0275fac=function(i){return new(i||n)(t.LFG(x.eN),t.LFG(w.V))},n.\u0275prov=t.Yz7({token:n,factory:n.\u0275fac,providedIn:"root"}),n})();var h=c(8966),f=c(2290),v=c(7322),m=c(7531),M=c(7993);function U(n,s){1&n&&(t.TgZ(0,"div",22),t._uU(1,"Entrez le code postal s'il vous plait."),t.qZA())}function F(n,s){1&n&&(t.TgZ(0,"div",22),t._uU(1,"Ecrivez le conseil s'il vous plait."),t.qZA())}function L(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"button",23),t.NdJ("click",function(){return t.CHM(i),t.oxw().save()}),t._uU(1,"Save"),t.qZA()}}function J(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"button",23),t.NdJ("click",function(){return t.CHM(i),t.oxw().submit()}),t._uU(1,"Update"),t.qZA()}}const y=function(n){return{"is-invalid":n}};let C=(()=>{class n{constructor(i,e,o,a,p,A,b){this.router=i,this.service=e,this.formBuilder=o,this.dialog=a,this.route=p,this.datePipe=A,this.toastr=b,this.isEditView=!1,this.currentDate=new Date,this.route.params.subscribe(Z=>{this.id=Z.id})}ngOnInit(){this.createForm(),this.onBindForm()}createForm(){this.adviceForm=this.formBuilder.group({id:0,zipcode:["",r.kI.required],advice:["",r.kI.required],adviceType:["",r.kI.required],historyDate:this.currentDate,createdBy:0})}get f(){return this.adviceForm.controls}save(){this.submitted=!0,this.adviceForm.invalid||this.service.update({id:0,zipcode:+this.adviceForm.value.zipcode,advice:this.adviceForm.value.advice,adviceType:+this.adviceForm.value.adviceType?+this.adviceForm.value.adviceType:0,historyDate:this.adviceForm.value.historyDate,createdBy:0}).subscribe(e=>{e.status&&(this.toastr.success(e.message),this.dialog.closeAll())},e=>{this.toastr.error(e.error.message)})}onBindForm(){var i,e,o;this.isEditView=!0,this.data&&this.adviceForm.patchValue({zipcode:null===(i=this.data)||void 0===i?void 0:i.zipcode,advice:null===(e=this.data)||void 0===e?void 0:e.advice,adviceType:null===(o=this.data)||void 0===o?void 0:o.adviceType})}submit(){this.adviceForm.invalid||this.service.update({id:this.data.id,zipcode:+this.adviceForm.value.zipcode,advice:this.adviceForm.value.advice,adviceType:+this.adviceForm.value.adviceType,historyDate:this.adviceForm.value.historyDate,createdBy:1,updatedBy:1}).subscribe(e=>{(e.status=!0)?(this.toastr.success(e.message),this.dialog.closeAll()):this.toastr.error(e.message)})}openDialog(){this.dialog.open(n,{width:"45%",height:"50%"}).afterClosed().subscribe(e=>{})}cancel(){this.dialog.closeAll()}}return n.\u0275fac=function(i){return new(i||n)(t.Y36(l.F0),t.Y36(g),t.Y36(r.qu),t.Y36(h.uw),t.Y36(l.gz),t.Y36(d.uU),t.Y36(f._W))},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-add-advices"]],decls:45,vars:11,consts:[[1,"col-md-12","p-0"],[1,"headerWiz"],["mat-button","",1,"cancelbtn",3,"click"],["src","../../../../../assets/images/cancel.png"],[1,"popup-input",3,"formGroup"],[1,"row","m-0"],[1,"col-md-6"],[1,"inputLabel"],[2,"color","#ff0000"],["appearance","outline",1,"w-100","p-0"],["matInput","","type","text","formControlName","zipcode","maxlength","5","size","5","numbersOnly","",1,"formcontrol","box-width",3,"ngClass"],["class","box",4,"ngIf"],["formControlName","adviceType",1,"search-field1","select-option","option","box-width"],["value","1"],["value","2"],["value","3"],[1,"col-md-12","textfield"],["appearance","outline",1,"w-100","text-pd"],["matInput","","type","text","formControlName","advice",1,"advices",3,"ngClass"],[1,"col-lg-12","select-btn","pb-5"],["type","button","class","submit-btn",3,"click",4,"ngIf"],["type","button",1,"cancel-btn",3,"click"],[1,"box"],["type","button",1,"submit-btn",3,"click"]],template:function(i,e){1&i&&(t.TgZ(0,"div",0)(1,"h4",1),t._uU(2,"ADD ADVICES"),t.TgZ(3,"button",2),t.NdJ("click",function(){return e.cancel()}),t._UZ(4,"img",3),t.qZA()()(),t.TgZ(5,"form",4)(6,"section")(7,"div",5)(8,"div",6)(9,"div",7),t._uU(10,"Zip Code"),t.TgZ(11,"span",8),t._uU(12," *"),t.qZA()(),t.TgZ(13,"mat-form-field",9),t._UZ(14,"input",10),t.qZA(),t.YNc(15,U,2,0,"div",11),t.qZA(),t.TgZ(16,"div",6)(17,"div",7),t._uU(18,"Type"),t.TgZ(19,"span",8),t._uU(20," *"),t.qZA()(),t.TgZ(21,"select",12)(22,"option",13),t._uU(23,"Pollen"),t.qZA(),t.TgZ(24,"option",14),t._uU(25,"Pollution"),t.qZA(),t.TgZ(26,"option",15),t._uU(27,"Tips"),t.qZA()()()()(),t.TgZ(28,"section")(29,"div",5)(30,"div",16)(31,"div",7),t._uU(32,"Advices"),t.TgZ(33,"span",8),t._uU(34," *"),t.qZA()(),t.TgZ(35,"mat-form-field",17),t._UZ(36,"textarea",18),t.qZA(),t.YNc(37,F,2,0,"div",11),t.qZA()()(),t.TgZ(38,"section")(39,"div",5)(40,"div",19),t.YNc(41,L,2,0,"button",20),t.YNc(42,J,2,0,"button",20),t.TgZ(43,"button",21),t.NdJ("click",function(){return e.cancel()}),t._uU(44,"Cancel"),t.qZA()()()()()),2&i&&(t.xp6(5),t.Q6J("formGroup",e.adviceForm),t.xp6(9),t.Q6J("ngClass",t.VKq(7,y,e.submitted&&e.f.zipcode.errors||!e.adviceForm.controls.zipcode.valid&&e.f.zipcode.touched&&e.f.zipcode.invalid)),t.xp6(1),t.Q6J("ngIf",!e.adviceForm.controls.zipcode.valid&&e.submitted||!e.adviceForm.controls.zipcode.valid&&e.f.zipcode.touched&&e.f.zipcode.invalid),t.xp6(21),t.Q6J("ngClass",t.VKq(9,y,e.submitted&&e.f.advice.errors||!e.adviceForm.controls.advice.valid&&e.f.advice.touched&&e.f.advice.invalid)),t.xp6(1),t.Q6J("ngIf",!e.adviceForm.controls.advice.valid&&e.submitted||!e.adviceForm.controls.advice.valid&&e.f.advice.touched&&e.f.advice.invalid),t.xp6(4),t.Q6J("ngIf",void 0===e.isEdit),t.xp6(1),t.Q6J("ngIf",!0===e.isEdit))},directives:[r._Y,r.JL,r.sg,v.KE,m.Nt,r.Fj,M.N,r.JJ,r.u,r.nD,d.mk,d.O5,r.EJ,r.YN,r.Kr],styles:["h4.headerWiz[_ngcontent-%COMP%]{background-color:#3761d8;margin-bottom:12px;padding:25px;font-size:16px;color:#fff}.box[_ngcontent-%COMP%]{color:#e9453b;margin:10px}.option[_ngcontent-%COMP%]{color:#0f0f0f}.search-button[_ngcontent-%COMP%]{margin-top:100px;margin-left:-100px}.col-md-12[_ngcontent-%COMP%]{padding:0}.cancelbtn[_ngcontent-%COMP%]{float:right;background-color:transparent;border:none;outline:none!important}.mat-button[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-icon-button[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%]{opacity:0!important;box-shadow:none!important}.search-field1[_ngcontent-%COMP%]{padding:7px;outline:13px;border:1px solid;border-radius:5px;margin:5px 0;font-size:15px;text-align:left;width:150px;font-family:Quicksand,sans-serif;font-style:normal;border-color:#bababa!important}"]}),n})();var u=c(8567);function P(n,s){if(1&n&&(t.TgZ(0,"tr",11)(1,"td"),t._uU(2),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA(),t.TgZ(5,"td"),t._uU(6),t.qZA(),t.TgZ(7,"td"),t._uU(8),t.ALo(9,"date"),t.qZA()()),2&n){const i=s.$implicit,e=s.index,o=t.oxw();t.xp6(2),t.Oqu((o.pageNumber-1)*o.pageCount+e+1),t.xp6(2),t.Oqu(i.comments),t.xp6(2),t.Oqu(i.username),t.xp6(2),t.Oqu(t.xi3(9,4,i.createdDate,"dd/MM/yyyy"))}}function D(n,s){1&n&&(t.TgZ(0,"tr",12)(1,"td",13)(2,"p",14),t._uU(3,"Aucun Enregistrement Trouv\xe9"),t.qZA()()())}function I(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"div",14),t._UZ(1,"div",15),t.TgZ(2,"div",16)(3,"pagination-controls",17),t.NdJ("pageChange",function(o){t.CHM(i);const a=t.oxw();return a.pageChanged(a.pageNumber=o)}),t.qZA()(),t._UZ(4,"div",15),t.qZA()}}const S=function(n,s,i){return{itemsPerPage:n,currentPage:s,totalItems:i}};let N=(()=>{class n{constructor(i,e,o){this.dialog=i,this.route=e,this.service=o,this.adviceViewlist=[],this.totalCount=0,this.isEditView=!1,this.pageNumber=1,this.pageSize=0,this.pageCount=5,this.route.params.subscribe(a=>{this.id=a.id})}ngOnInit(){this.getList()}pageChanged(i){this.pageNumber=i,this.getList()}getList(){this.service.getViewList({adviceId:this.data.id}).subscribe(e=>{this.adviceViewlist=e.data,e.totalCount>0?this.totalCount=e.totalCount:(this.totalCount=0,this.adviceViewlist=[])},e=>{})}openDialog(){this.dialog.open(n,{width:"45%",height:"50%"}).afterClosed().subscribe(e=>{})}cancel(){this.dialog.closeAll()}}return n.\u0275fac=function(i){return new(i||n)(t.Y36(h.uw),t.Y36(l.gz),t.Y36(g))},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-view-advice"]],decls:22,vars:10,consts:[[1,"col-md-12","p-0"],[1,"headerWiz"],["mat-button","",1,"cancelbtn"],["src","../../../../../assets/images/cancel.png",3,"click"],[1,"row","m-0"],[1,"table-responsive","table-height2"],[2,"width","90%","margin","20px auto","box-shadow","0 5px 5px 5px #efefef","border","2px solid #efefef"],[1,"stracture"],["class","border-bottom table-color",4,"ngFor","ngForOf"],["class","mat-row table-color",4,"ngIf"],["class"," text-center ",4,"ngIf"],[1,"border-bottom","table-color"],[1,"mat-row","table-color"],["colspan","12",1,"mat-cell","record"],[1,"text-center"],[1,"col-md-1"],[1,"col-md-10",2,"margin-bottom","20px","margin-top","25px"],["direction-links","true","previousLabel","Previous","nextLabel","Next","max-size","5",3,"pageChange"]],template:function(i,e){1&i&&(t.TgZ(0,"div",0)(1,"h4",1),t._uU(2,"Comments"),t.TgZ(3,"button",2)(4,"img",3),t.NdJ("click",function(){return e.cancel()}),t.qZA()()()(),t.TgZ(5,"section")(6,"div",4)(7,"div",5)(8,"table",6)(9,"tr",7)(10,"th"),t._uU(11,"S.No"),t.qZA(),t.TgZ(12,"th"),t._uU(13,"Comments"),t.qZA(),t.TgZ(14,"th"),t._uU(15,"UserName"),t.qZA(),t.TgZ(16,"th"),t._uU(17,"CreatedDate"),t.qZA()(),t.YNc(18,P,10,7,"tr",8),t.ALo(19,"paginate"),t.YNc(20,D,4,0,"tr",9),t.qZA(),t.YNc(21,I,5,0,"div",10),t.qZA()()()),2&i&&(t.xp6(18),t.Q6J("ngForOf",t.xi3(19,3,e.adviceViewlist,t.kEZ(6,S,e.pageCount,e.pageNumber,e.totalCount))),t.xp6(2),t.Q6J("ngIf",0==e.totalCount),t.xp6(1),t.Q6J("ngIf",e.totalCount>0))},directives:[d.sg,d.O5,u.LS],pipes:[u._s,d.uU],styles:["h4.headerWiz[_ngcontent-%COMP%]{background-color:#3761d8;margin-bottom:12px;padding:25px;font-size:16px;color:#fff}.box[_ngcontent-%COMP%]{color:#e9453b;margin:10px}.search-button[_ngcontent-%COMP%]{margin-top:100px;margin-left:-100px}.col-md-12[_ngcontent-%COMP%]{padding:0}.cancelbtn[_ngcontent-%COMP%]{float:right;background-color:transparent;border:none;outline:none!important}.mat-button[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-icon-button[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%]{opacity:0!important;box-shadow:none!important}"]}),n})();function z(n,s){1&n&&(t.TgZ(0,"div",15),t._uU(1," S'il vous pla\xeet entrez une URL valide "),t.qZA())}function E(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"button",16),t.NdJ("click",function(){return t.CHM(i),t.oxw().save()}),t._uU(1,"Save"),t.qZA()}}function Y(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"button",16),t.NdJ("click",function(){return t.CHM(i),t.oxw().submit()}),t._uU(1,"Update"),t.qZA()}}let k=(()=>{class n{constructor(i,e,o,a,p,A,b){this.router=i,this.service=e,this.formBuilder=o,this.dialog=a,this.route=p,this.datePipe=A,this.toastr=b,this.isEditView=!1,this.currentDate=new Date,this.sponsorlist=[],this.route.params.subscribe(Z=>{this.id=Z.id})}ngOnInit(){this.createForm(),this.onBindForm()}createForm(){this.sponsorForm=this.formBuilder.group({content:["",r.kI.required],urlLink:["",[n=>{const s=n.value;return s?Q.test(s)?null:{invalidUrl:!0}:null}]]})}onBindForm(){var i,e;this.data&&this.sponsorForm.patchValue({content:null===(i=this.data)||void 0===i?void 0:i.content,urlLink:null===(e=this.data)||void 0===e?void 0:e.link})}submit(){this.sponsorForm.invalid||this.service.updateSponsor({id:this.data.id,content:this.sponsorForm.value.content,link:this.sponsorForm.value.urlLink}).subscribe(e=>{(e.status=!0)?(this.toastr.success(e.message),this.dialog.closeAll()):this.toastr.error(e.message)})}save(){this.submitted=!0,this.sponsorForm.invalid||this.service.updateSponsor({id:0,content:this.sponsorForm.value.content,link:this.sponsorForm.value.urlLink}).subscribe(e=>{e.status&&(this.toastr.success(e.message),this.dialog.closeAll())},e=>{this.toastr.error(e.error.message)})}openDialog(){this.dialog.open(n,{width:"45%",height:"50%"}).afterClosed().subscribe(e=>{})}cancel(){this.dialog.closeAll()}}return n.\u0275fac=function(i){return new(i||n)(t.Y36(l.F0),t.Y36(g),t.Y36(r.qu),t.Y36(h.uw),t.Y36(l.gz),t.Y36(d.uU),t.Y36(f._W))},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-add-sponsor"]],decls:27,vars:4,consts:[[1,"col-md-12","p-0"],[1,"headerWiz"],["mat-button","",1,"cancelbtn",3,"click"],["src","../../../../../assets/images/cancel.png"],[1,"popup-input",3,"formGroup"],[1,"row","m-0"],[1,"col-md-12","p-4"],[1,"inputLabel"],[2,"color","#ff0000"],["appearance","outline",1,"w-100","p-0"],["matInput","","type","text","formControlName","content",1,"formcontrol","box-width"],["matInput","","type","text","formControlName","urlLink","maxlength","255",1,"formcontrol","box-width"],["class","box",4,"ngIf"],[1,"col-lg-12","select-btn","pb-5"],["type","button","class","submit-btn",3,"click",4,"ngIf"],[1,"box"],["type","button",1,"submit-btn",3,"click"]],template:function(i,e){1&i&&(t.TgZ(0,"div",0)(1,"h4",1),t._uU(2,"ADD SPONSORS"),t.TgZ(3,"button",2),t.NdJ("click",function(){return e.cancel()}),t._UZ(4,"img",3),t.qZA()()(),t.TgZ(5,"form",4)(6,"section")(7,"div",5)(8,"div",6)(9,"div",7),t._uU(10,"Content"),t.TgZ(11,"span",8),t._uU(12," *"),t.qZA()(),t.TgZ(13,"mat-form-field",9),t._UZ(14,"input",10),t.qZA(),t.TgZ(15,"div",7),t._uU(16,"Link"),t.TgZ(17,"span",8),t._uU(18," *"),t.qZA()(),t.TgZ(19,"mat-form-field",9),t._UZ(20,"input",11),t.qZA(),t.YNc(21,z,2,0,"div",12),t.qZA()()(),t.TgZ(22,"section")(23,"div",5)(24,"div",13),t.YNc(25,E,2,0,"button",14),t.YNc(26,Y,2,0,"button",14),t.qZA()()()()),2&i&&(t.xp6(5),t.Q6J("formGroup",e.sponsorForm),t.xp6(16),t.Q6J("ngIf",e.sponsorForm.get("urlLink").hasError("invalidUrl")&&e.sponsorForm.get("urlLink").touched),t.xp6(4),t.Q6J("ngIf",void 0===e.isEdit),t.xp6(1),t.Q6J("ngIf",!0===e.isEdit))},directives:[r._Y,r.JL,r.sg,v.KE,m.Nt,r.Fj,r.JJ,r.u,r.nD,d.O5],styles:["h4.headerWiz[_ngcontent-%COMP%]{background-color:#3761d8;margin-bottom:12px;padding:25px;font-size:16px;color:#fff}.box[_ngcontent-%COMP%]{color:#e9453b;margin:10px}.option[_ngcontent-%COMP%]{color:#0f0f0f}.search-button[_ngcontent-%COMP%]{margin-top:100px;margin-left:-100px}.col-md-12[_ngcontent-%COMP%]{padding:0}.cancelbtn[_ngcontent-%COMP%]{float:right;background-color:transparent;border:none;outline:none!important}.mat-button[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%], .mat-icon-button[_ngcontent-%COMP%]   .mat-button-focus-overlay[_ngcontent-%COMP%]{opacity:0!important;box-shadow:none!important}.search-field1[_ngcontent-%COMP%]{padding:7px;outline:13px;border:1px solid;border-radius:5px;margin:5px 0;font-size:15px;text-align:left;width:150px;font-family:Quicksand,sans-serif;font-style:normal;border-color:#bababa!important}"]}),n})();const Q=/^(?:(?:http|https|ftp):\/\/)?[\w.-]+(?:\.[\w\.-]+)+[\w\-._~:/?#[\]@!$&'()*+,;=%]+$/;var q=c(5864);function B(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"img",17),t.NdJ("click",function(){return t.CHM(i),t.oxw().add1()}),t.qZA()}}function R(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"tr",34)(1,"td"),t._uU(2),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA(),t.TgZ(5,"td"),t._uU(6),t.qZA(),t.TgZ(7,"td",35)(8,"i",36),t.NdJ("click",function(){const a=t.CHM(i).$implicit;return t.oxw().sponsorEdit(a)}),t.qZA()(),t.TgZ(9,"td",37)(10,"i",38),t.NdJ("click",function(){const a=t.CHM(i).$implicit;return t.oxw().sponsorDelete(a.id)}),t.qZA()()()}if(2&n){const i=s.$implicit,e=s.index;t.xp6(2),t.Oqu(e+1),t.xp6(2),t.Oqu(i.content),t.xp6(2),t.Oqu(i.link)}}function H(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"i",39),t.NdJ("click",function(){return t.CHM(i),t.oxw().clear()}),t.qZA()}}function G(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"i",36),t.NdJ("click",function(){t.CHM(i);const o=t.oxw().$implicit;return t.oxw().edit(o)}),t.qZA()}}function $(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"i",45),t.NdJ("click",function(){t.CHM(i);const o=t.oxw().$implicit;return t.oxw().notification(o)}),t.qZA()}}function W(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"tr",34)(1,"td"),t._uU(2),t.qZA(),t.TgZ(3,"td"),t._uU(4),t.qZA(),t.TgZ(5,"td"),t._uU(6),t.qZA(),t.TgZ(7,"td"),t._uU(8),t.qZA(),t.TgZ(9,"td"),t._uU(10),t.ALo(11,"date"),t.qZA(),t.TgZ(12,"td"),t._uU(13),t.qZA(),t.TgZ(14,"td")(15,"i",40),t.NdJ("click",function(){const a=t.CHM(i).$implicit;return t.oxw().View(a)}),t.qZA()(),t.TgZ(16,"td")(17,"div",6)(18,"div",41),t.YNc(19,G,1,0,"i",42),t.qZA(),t.TgZ(20,"div",41),t.YNc(21,$,1,0,"i",43),t.qZA(),t.TgZ(22,"div",41)(23,"i",44),t.NdJ("click",function(){const a=t.CHM(i).$implicit;return t.oxw().delete(a.id)}),t.qZA()()()()()}if(2&n){const i=s.$implicit,e=s.index,o=t.oxw();t.xp6(2),t.Oqu((o.pageNumber-1)*o.pageCount+e+1),t.xp6(2),t.Oqu(i.zipcode),t.xp6(2),t.Oqu(i.adviceTypeName),t.xp6(2),t.Oqu(i.advice),t.xp6(2),t.Oqu(t.xi3(11,8,i.createdDate,"dd/MM/yyyy")),t.xp6(3),t.Oqu(i.likesCount),t.xp6(6),t.Q6J("ngIf","0"==i.isSendMessage),t.xp6(2),t.Q6J("ngIf","0"==i.isSendMessage)}}function j(n,s){1&n&&(t.TgZ(0,"tr",46)(1,"td",47)(2,"p",48),t._uU(3,"Aucun Enregistrement Trouv\xe9"),t.qZA()()())}function X(n,s){if(1&n){const i=t.EpF();t.TgZ(0,"div",48),t._UZ(1,"div",49),t.TgZ(2,"div",50)(3,"pagination-controls",51),t.NdJ("pageChange",function(o){t.CHM(i);const a=t.oxw();return a.pageChanged(a.pageNumber=o)}),t.qZA()(),t._UZ(4,"div",49),t.qZA()}}const _=function(){return{standalone:!0}},K=function(n,s,i){return{itemsPerPage:n,currentPage:s,totalItems:i}},tt=[{path:"",component:(()=>{class n{constructor(i,e,o,a){this.toastr=i,this.service=e,this.dialog=o,this.datePipe=a,this.pageNumber=1,this.totalCount=0,this.pageSize=0,this.pageCount=5,this.sorting="",this.SortOrder="asc",this.searchZipcode="",this.searchAdvice="",this.searchType=0,this.searchDate=null,this.isEditView=!1}ngOnInit(){this.getList(),this.sponsorGetList()}getList(){this.service.getList({pageNumber:this.pageNumber,pageSize:this.pageSize,sortBy:this.sorting,zipcode:this.searchZipcode,advice:this.searchAdvice,adviceType:+this.searchType?+this.searchType:0,modifiedDate:this.visitStartDate?this.visitStartDate:null,like:+this.searchLike?+this.searchLike:0}).subscribe(e=>{this.advicelist=e.data,e.totalCount>0?this.totalCount=e.totalCount:(this.totalCount=0,this.advicelist=[])},e=>{})}searchZip(){this.pageNumber=1,this.getList()}searchAdd(){this.pageNumber=1,this.getList()}searchAddLike(){this.pageNumber=1,this.getList()}searchNotType(){this.pageNumber=1,this.getList()}searchAddDate(i){this.pageNumber=1,this.visitStartDate=this.datePipe.transform(i.startDate._d,"yyyy/MM/dd"),this.getList()}clear(){this.searchDate=null,this.visitStartDate=void 0,this.getList()}edit(i){let e=this.dialog.open(C,{width:"50%",panelClass:"editpopup"});e.componentInstance.isEdit=!0,e.componentInstance.data=i,e.afterClosed().subscribe(o=>{this.animal=o,this.getList()})}add(){this.dialog.open(C,{width:"50%",panelClass:"popupbox"}).afterClosed().subscribe(e=>{this.animal=e,this.getList()})}add1(){this.dialog.open(k,{width:"50%",panelClass:"popupbox"}).afterClosed().subscribe(e=>{this.animal=e,this.sponsorGetList()})}View(i){let e=this.dialog.open(N,{width:"50%",panelClass:"popupbox"});e.componentInstance.isEdit=!0,e.componentInstance.data=i,e.afterClosed().subscribe(o=>{this.animal=o,this.getList()})}delete(i){this.service.deleteAdvice(i).subscribe(e=>{(e.status=!0)&&(this.toastr.success(e.message),this.getList())})}sponsorDelete(i){this.service.deleteSponsor(i).subscribe(e=>{(e.status=!0)&&(this.toastr.success(e.message),this.sponsorGetList())})}sort(i){this.SortOrder="desc"==this.SortOrder?"asc":"desc",this.sorting="asc"==this.SortOrder?`${i}_asc`:`${i}_desc`,this.getList()}pageChanged(i){this.pageNumber=i,this.getList()}notification(i){this.titleName="Pollen"==i.adviceTypeName?"Alerte pollen":"Pollution"==i.adviceTypeName?"Alerte pollution":"Le Conseil du jour";let e={id:i.id,zipcode:+i.zipcode,title:this.titleName,body:i.advice,notificationFor:i.adviceType.toString()};this.service.getNotification(e).subscribe(o=>{o.status?(this.toastr.success(o.message),this.getList()):this.toastr.error(o.message)},o=>{this.toastr.error(o.error.message)}),this.getList()}sponsorGetList(){this.service.sponsorGetList().subscribe(i=>{this.sponsorlist=i.data,i.totalCount>0?this.totalCount=i.totalCount:(this.totalCount=0,this.sponsorlist=[])},i=>{})}sponsorEdit(i){let e=this.dialog.open(k,{width:"50%",panelClass:"editpopup"});e.componentInstance.isEdit=!0,e.componentInstance.data=i,e.afterClosed().subscribe(o=>{this.animal=o,this.sponsorGetList()})}}return n.\u0275fac=function(i){return new(i||n)(t.Y36(f._W),t.Y36(g),t.Y36(h.uw),t.Y36(d.uU))},n.\u0275cmp=t.Xpm({type:n,selectors:[["app-advices"]],decls:86,vars:30,consts:[[1,"list-view"],[1,"breadcrumb","navigation","headertext"],[1,"breadcrumb-item","active","headertext"],[1,"breadcrumb-item"],["href","javascript:void(0);"],[1,"p-4",2,"margin-top","-13px"],[1,"row","m-0"],[1,"col-lg-6","userlist"],[1,"col-lg-6","text-right","p-4","list-sec","plus-icon"],["src","../../../../assets/images/plus.png","alt","plus","class","pr-4",3,"click",4,"ngIf"],[1,"table-responsive","table-height1"],[2,"width","100%"],[1,"stracture"],[2,"width","40%"],["colspan","2",1,"text-center"],["class","border-bottom table-color",4,"ngFor","ngForOf"],[1,"p-4"],["src","../../../../assets/images/plus.png","alt","plus",1,"pr-4",3,"click"],["aria-hidden","true",1,"fa","fa-sort",3,"click"],["type","text","id","search","name","search","placeholder","search","numbersOnly","",1,"search-field1",3,"ngModel","ngModelOptions","ngModelChange"],["numbersOnly","",1,"search-field1","select-option","option",3,"ngModel","ngModelOptions","ngModelChange"],["value","0"],["value","1"],["value","2"],["value","3"],["type","text","id","search","name","search","placeholder","search",1,"search-field1",3,"ngModel","ngModelOptions","ngModelChange"],["aria-hidden","true",1,"fa","fa-calendar"],[1,"date-selection","close-select"],["matInputtype","text","id","search","name","search","ngxDaterangepickerMd","","readonly","","placeholder","Created Date",1,"leavingIcon",3,"autoApply","ngModel","minDate","showDropdowns","singleDatePicker","ngModelChange"],[1,"icone-sec"],["class","fa fa-times","aria-hidden","true",3,"click",4,"ngIf"],[1,"section-width"],["class","mat-row table-color",4,"ngIf"],["class"," text-center ",4,"ngIf"],[1,"border-bottom","table-color"],[2,"width","10px"],["aria-hidden","true",1,"fa","fa-pencil-square-o",3,"click"],[2,"width","1px"],["aria-hidden","true",1,"fa","fa-trash","mr-3",3,"click"],["aria-hidden","true",1,"fa","fa-times",3,"click"],["aria-hidden","true",1,"fa","fa-eye",2,"cursor","pointer",3,"click"],[1,"col-lg-4","p-0"],["class","fa fa-pencil-square-o","aria-hidden","true",3,"click",4,"ngIf"],["class","fa fa-check-square-o","aria-hidden","true",3,"click",4,"ngIf"],["aria-hidden","true",1,"fa","fa-trash",3,"click"],["aria-hidden","true",1,"fa","fa-check-square-o",3,"click"],[1,"mat-row","table-color"],["colspan","12",1,"mat-cell","record"],[1,"text-center"],[1,"col-md-1"],[1,"col-md-10",2,"margin-bottom","20px","margin-top","25px"],["direction-links","true","previousLabel","Previous","nextLabel","Next","max-size","5",3,"pageChange"]],template:function(i,e){1&i&&(t.TgZ(0,"section")(1,"div")(2,"nav",0)(3,"p"),t._uU(4,"Advices"),t.qZA(),t.TgZ(5,"ul",1)(6,"li",2),t._uU(7,"Home"),t.qZA(),t.TgZ(8,"li",3)(9,"a",4),t._uU(10,"Advices"),t.qZA()()()()()(),t.TgZ(11,"section",5)(12,"div",6)(13,"div",7)(14,"p"),t._uU(15,"Sponsored"),t.qZA()(),t.TgZ(16,"div",8),t.YNc(17,B,1,0,"img",9),t.qZA()(),t.TgZ(18,"div",10)(19,"table",11)(20,"tr",12)(21,"th"),t._uU(22,"S.No"),t.qZA(),t.TgZ(23,"th",13),t._uU(24,"Content "),t.qZA(),t.TgZ(25,"th"),t._uU(26,"Link "),t.qZA(),t.TgZ(27,"th",14),t._uU(28,"Action"),t.qZA()(),t.YNc(29,R,11,3,"tr",15),t.qZA()()(),t.TgZ(30,"section",16)(31,"div",6)(32,"div",7)(33,"p"),t._uU(34,"Advices"),t.qZA()(),t.TgZ(35,"div",8)(36,"img",17),t.NdJ("click",function(){return e.add()}),t.qZA()()(),t.TgZ(37,"div",10)(38,"table",11)(39,"tr",12)(40,"th"),t._uU(41,"S.No"),t.qZA(),t.TgZ(42,"th")(43,"i",18),t.NdJ("click",function(){return e.sort("zip")}),t.qZA(),t._uU(44,"Zip Code"),t.TgZ(45,"div")(46,"input",19),t.NdJ("ngModelChange",function(a){return e.searchZipcode=a})("ngModelChange",function(){return e.searchZip()}),t.qZA()()(),t.TgZ(47,"th")(48,"i",18),t.NdJ("click",function(){return e.sort("advicetype")}),t.qZA(),t._uU(49,"Type"),t.TgZ(50,"div")(51,"select",20),t.NdJ("ngModelChange",function(a){return e.searchType=a})("ngModelChange",function(){return e.searchNotType()}),t.TgZ(52,"option",21),t._uU(53,"All"),t.qZA(),t.TgZ(54,"option",22),t._uU(55,"Pollen"),t.qZA(),t.TgZ(56,"option",23),t._uU(57,"Pollution"),t.qZA(),t.TgZ(58,"option",24),t._uU(59,"Tips"),t.qZA()()()(),t.TgZ(60,"th")(61,"i",18),t.NdJ("click",function(){return e.sort("advice")}),t.qZA(),t._uU(62,"Advice"),t.TgZ(63,"div")(64,"input",25),t.NdJ("ngModelChange",function(a){return e.searchAdvice=a})("ngModelChange",function(){return e.searchAdd()}),t.qZA()()(),t.TgZ(65,"th")(66,"i",18),t.NdJ("click",function(){return e.sort("date")}),t.qZA(),t._uU(67,"CreatedDate"),t._UZ(68,"i",26),t.TgZ(69,"div",27)(70,"input",28),t.NdJ("ngModelChange",function(a){return e.searchDate=a})("ngModelChange",function(a){return e.searchAddDate(a)}),t.qZA(),t.TgZ(71,"div",29),t.YNc(72,H,1,0,"i",30),t.qZA()()(),t.TgZ(73,"th")(74,"i",18),t.NdJ("click",function(){return e.sort("like")}),t.qZA(),t._uU(75,"Like"),t.TgZ(76,"div")(77,"input",19),t.NdJ("ngModelChange",function(a){return e.searchLike=a})("ngModelChange",function(){return e.searchAddLike()}),t.qZA()()(),t.TgZ(78,"th"),t._uU(79,"Comments "),t.qZA(),t.TgZ(80,"th",31),t._uU(81,"Action"),t.qZA()(),t.YNc(82,W,24,11,"tr",15),t.ALo(83,"paginate"),t.YNc(84,j,4,0,"tr",32),t.qZA(),t.YNc(85,X,5,0,"div",33),t.qZA()()),2&i&&(t.xp6(17),t.Q6J("ngIf",0===e.sponsorlist.length),t.xp6(12),t.Q6J("ngForOf",e.sponsorlist),t.xp6(17),t.Q6J("ngModel",e.searchZipcode)("ngModelOptions",t.DdM(22,_)),t.xp6(5),t.Q6J("ngModel",e.searchType)("ngModelOptions",t.DdM(23,_)),t.xp6(13),t.Q6J("ngModel",e.searchAdvice)("ngModelOptions",t.DdM(24,_)),t.xp6(6),t.Q6J("autoApply",!0)("ngModel",e.searchDate)("minDate",e.minDate)("showDropdowns",!1)("singleDatePicker",!0),t.xp6(2),t.Q6J("ngIf",e.searchDate!==e.visitStartDate),t.xp6(5),t.Q6J("ngModel",e.searchLike)("ngModelOptions",t.DdM(25,_)),t.xp6(5),t.Q6J("ngForOf",t.xi3(83,19,e.advicelist,t.kEZ(26,K,e.pageCount,e.pageNumber,e.totalCount))),t.xp6(2),t.Q6J("ngIf",0==e.totalCount),t.xp6(1),t.Q6J("ngIf",e.totalCount>0))},directives:[d.O5,d.sg,M.N,r.Fj,r.JJ,r.On,r.EJ,r.YN,r.Kr,q.SP,u.LS],pipes:[u._s,d.uU],styles:[".record[_ngcontent-%COMP%]{margin-top:36px;font-size:16px;color:#414c96}.option[_ngcontent-%COMP%]{color:#a0a0a0}.dropdown-btn[_ngcontent-%COMP%]{background-color:#fff!important}.disableBtn[disabled][_ngcontent-%COMP%]{cursor:not-allowed!important}label.polNone[_ngcontent-%COMP%]{display:none}.textarea-btn[_ngcontent-%COMP%]   img[_ngcontent-%COMP%]{float:left;margin-left:10px}.custom-file-input[_ngcontent-%COMP%]{display:none}.closeImg[_ngcontent-%COMP%]{position:absolute;right:20px}.table-responsive[_ngcontent-%COMP%]{display:block;width:100%;overflow-x:visible!important}  .table-height1{height:100px!important}"]}),n})()},{path:"add-advice",component:C},{path:"View-advice",component:N}];let et=(()=>{class n{}return n.\u0275fac=function(i){return new(i||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({imports:[[l.Bz.forChild(tt)],l.Bz]}),n})();var it=c(4107),nt=c(508),ot=c(8971),st=c(3056);let at=(()=>{class n{}return n.\u0275fac=function(i){return new(i||n)},n.\u0275mod=t.oAB({type:n}),n.\u0275inj=t.cJS({providers:[],imports:[[d.ez,et,r.UX,r.u5,u.JX,v.lN,it.LD,nt.BQ,ot.Z,q.n1.forRoot(),st.ZQ,m.c],m.c]}),n})()}}]);