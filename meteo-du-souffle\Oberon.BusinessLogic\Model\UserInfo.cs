﻿using Oberon.BusinessLogic.BusinessHelper;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Oberon.BusinessLogic.Model
{
    public class UserInfo
    {
        public string EmailId { get; set;}
        public string Password { get; set;}
        public string DeciveInfo { get; set; }
        public string DeciveId { get; set; }
        public bool isAndriod { get; set; }
        public long GPSZipCode { get; set; }
    }

    public class ForgotPasswordModel
    {
        public string EmailId { get; set; }
    }

    public class UserList
    {
        public int pageNumber { get; set; }
        public int pageSize { get; set; }
        public long searchId { get; set; }
        public string searchFirstName { get; set; }
        public string searchLastName { get; set; }
        public string searchEmail { get; set; }
        public long searchYOB { get; set; }
        public long searchGender { get; set; }
        public string searchSmoker { get; set; }
        public string searchRespProb { get; set; }
        public string searchPollen { get; set; }
        public string searchzipCode { get; set; }
        public string sortBy { get; set; }
    }

    public class AdvicesModel
    {
        public int Id { get; set; }
        public long Zipcode { get; set; }
        public string Advice { get; set; }
        public NotificationTypeHelper AdviceType { get; set; }
        public DateTime HistoryDate { get; set; }
        public int CreatedBy { get; set; }
        public bool IsSendMessage { get; set; }
    }

    public class AdviceList
    {
        public int pageNumber { get; set; }
        public int pageSize { get; set; }
        public string sortBy { get; set; }
        public string zipcode { get; set; }
        public short AdviceType { get; set; }
        public string Advice { get; set; }
        public string ModifiedDate { get; set; }
        public int Like { get; set; }
    }

    public class UserModel
    {
        public int Id { get; set; }
        public string Email { get; set; }
        public string Name { get; set; }
        public string FirstName { get; set; }
        public string Password { get; set; }
        public long YearofBirth { get; set; }
        public int Gender { get; set; }
        public bool IsSmoking { get; set; }
        public DateTime Createddate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int createdBy { get; set; }
        public int UpdatedBy { get; set; }
        public bool MultiFactorAuth { get; set; }
        public bool IsNewsFromApp { get; set; }
        public bool IstermAndConditions { get; set; }
        public long ZipCode { get; set; }
        public List<int> ResProblemId { get; set; }
        public List<int> PollenId { get; set; }
        public List<int> NotificationFor { get; set; }

    }

    public class AdviceInfoList
    {
        public int id { get; set; }
        public int zipcode { get; set; }
        public string title { get; set; }
        public string body { get; set; }
        //public string searchRespProb { get; set; }
        //public string searchPollen { get; set; }
        public string NotificationFor { get; set; }
    }

    public class AdvicesResponse
    {
        public int Id { get; set; }
        public string Zipcode { get; set; }
        public int AdviceType { get; set; }
        public string AdviceTypeName { get; set; }
        public string Advice { get; set; }
        public int LikesCount { get; set; }
        public DateTime CreatedDate { get; set; }
        //public DateTime UpdatedDate { get; set; }
        //public int CreatedBy { get; set; }
        //public int UpdatedBy { get; set; }
        public DateTime AdviceHistoryDate { get; set; }
        public bool IsSendMessage { get; set; }
    }

    public class AdviceLike
    {
        //public int Id { get; set; }
        public int AdviceId { get; set; }
        public int UserId { get; set; }
        public bool IsLiked { get; set; }
    }
    public class CommentLike
    {
        public int AdviceId { get; set; }
        public int CommentId { get; set; }
        public int UserId { get; set; }
        public bool IsLiked { get; set; }
    }
    public class AdviceComment
    {
        //public int Id { get; set; }
        public int AdviceId { get; set; }
        public int UserId { get; set; }
        public string Comments { get; set; }
    }

    public class UserAdvice
    {
        public int Id { get; set; }
        public string Zipcode { get; set; }
        public int AdviceType { get; set; }
        public string AdviceTypeName { get; set; }
        public string Advice { get; set; }
        public int LikesCount { get; set; }
        public bool UserLiked { get; set; }
        //public DateTime UpdatedDate { get; set; }
        //public int CreatedBy { get; set; }
        //public int UpdatedBy { get; set; }
        public int UserId { get; set; }
        public bool IsSendMessage { get; set; }
    }
    public class SponsorModel
    {
        public int Id { get; set; }
        public string Content { get; set; }
        public string Link { get; set; }
    }
}
