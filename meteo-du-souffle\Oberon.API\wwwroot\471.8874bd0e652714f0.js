"use strict";(self.webpackChunkMeteodusouffle=self.webpackChunkMeteodusouffle||[]).push([[471],{5471:(m,n,o)=>{o.r(n),o.d(n,{ActivitiesModule:()=>v});var l=o(9808),c=o(8744),t=o(5e3);const r=[{path:"",component:(()=>{class e{constructor(){}ngOnInit(){}}return e.\u0275fac=function(i){return new(i||e)},e.\u0275cmp=t.Xpm({type:e,selectors:[["app-activities"]],decls:20,vars:0,consts:[[1,"list-view"],[1,"breadcrumb","navigation","headertext"],[1,"breadcrumb-item","active"],[1,"breadcrumb-item"],["href","javascript:void(0);"],[1,"p-4","analytics-sec"],[1,"google-analytics"],["src","../../../../assets/images/googleanalytics.png","alt","googleana"],[1,"analytics-btn"],["href","https://analytics.google.com/analytics/web/?authuser=1&hl=fr#/p312649321/reports/dashboard?r=firebase-overview","target","_blank"],["type","button"],["src","../../../../assets/images/expand.png"]],template:function(i,g){1&i&&(t.TgZ(0,"section")(1,"div")(2,"nav",0)(3,"p"),t._uU(4,"Activities"),t.qZA(),t.TgZ(5,"ul",1)(6,"li",2),t._uU(7,"Home"),t.qZA(),t.TgZ(8,"li",3)(9,"a",4),t._uU(10,"Activities"),t.qZA()()()()()(),t.TgZ(11,"section",5)(12,"div",6),t._UZ(13,"img",7),t.qZA(),t.TgZ(14,"div",8)(15,"a",9)(16,"button",10),t._uU(17,"Google Analytics"),t.TgZ(18,"span"),t._UZ(19,"img",11),t.qZA()()()()())},styles:[""]}),e})()}];let u=(()=>{class e{}return e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[c.Bz.forChild(r)],c.Bz]}),e})();var a=o(3075);let v=(()=>{class e{}return e.\u0275fac=function(i){return new(i||e)},e.\u0275mod=t.oAB({type:e}),e.\u0275inj=t.cJS({imports:[[l.ez,u,a.UX,a.u5]]}),e})()}}]);