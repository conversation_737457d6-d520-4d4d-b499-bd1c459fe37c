﻿using FirebaseAdmin.Auth;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Text;
using System.Text.Json.Serialization;

namespace Oberon.BusinessLogic.Model
{
    public class FeedBack
    {
        //public int Id { get; set; }
        public int UserId { get; set; }
        [DataType(DataType.Date)]
        public DateTime Date { get; set; }
        public int Level { get; set; }
        public string Symptoms { get; set; }
        public string Pollen { get; set; }

    }
    public class FeedBackList
    {
        public int UserId { get; set; }
        public int TargetYear { get; set; }
        public int TargetMonth { get; set; }
    }
}
