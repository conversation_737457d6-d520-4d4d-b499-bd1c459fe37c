﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class AddingNewTableCustomNotificationsUpdate : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_customNotifications",
                table: "customNotifications");

            migrationBuilder.RenameTable(
                name: "customNotifications",
                newName: "CustomNotifications");

            migrationBuilder.AddPrimaryKey(
                name: "PK_CustomNotifications",
                table: "CustomNotifications",
                column: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_CustomNotifications",
                table: "CustomNotifications");

            migrationBuilder.RenameTable(
                name: "CustomNotifications",
                newName: "customNotifications");

            migrationBuilder.AddPrimaryKey(
                name: "PK_customNotifications",
                table: "customNotifications",
                column: "Id");
        }
    }
}
