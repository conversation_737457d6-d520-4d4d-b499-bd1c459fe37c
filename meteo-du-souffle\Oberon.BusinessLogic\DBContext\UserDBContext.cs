﻿using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Text;

namespace Oberon.BusinessLogic.DBContext
{
    public class UserDBContext : DbContext
    {
        public UserDBContext(DbContextOptions options)
            : base(options)
        {
        }
        public DbSet<User> Signup { get; set; }
        public DbSet<DeviceInfo> DeviceInfo { get; set; }
        public DbSet<Notification> Notification { get; set; }
        public DbSet<Advices> Advices { get; set; }
        public DbSet<Pharmacy> Pharmacy { get; set; }
        public DbSet<RespiratoryProblems> RespiratoryProblems { get; set; }
        public DbSet<PollenList> PollenList { get; set; }
        public DbSet<ProfileInfo> ProfileInfo { get; set; }
        public DbSet<PollenMessage> PollenMessage { get; set; }
        public DbSet<PollutionMessage> PollutionMessage { get; set; }
        public DbSet<PdfFileUpload> PdfFileUpload { get; set; }
        public DbSet<SplashImage> SplashImage { get; set; }
        public DbSet<UserFeedBack> UserFeedBack { get; set; }
        public DbSet<AdviceLikes> AdviceLikes { get; set; }
        public DbSet<AdviceComments> AdviceComments { get; set; }
        public DbSet<CustomNotifications> CustomNotifications { get; set; }
        public DbSet<CommentsLikes> CommentsLikes { get; set; }
        public DbSet<ReadNotification> ReadNotification { get; set; }
        public DbSet<Sponsor> Sponsors { get; set; }
    }

    public class User
    {
        public int Id { get; set; }
        public string Uid { get; set; }
        public string Email { get; set; }
        public string Name { get; set; }
        public string FirstName { get; set; }
        public string Password { get; set; }
        public long YearofBirth { get; set; }
        public int Gender { get; set; }
        public bool IsSmoking { get; set; }
        public DateTime Createddate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int createdBy { get; set; }
        public int UpdatedBy { get; set; }
        public bool MultiFactorAuth { get; set; }
        public bool IsNewsFromApp { get; set; }
        public bool IstermAndConditions { get; set; }
        public long ZipCode { get; set; }
        public string ResProblemId { get; set; }
        public string PollenId { get; set; }
        public string DeciveId { get; set; }
        public bool isAndriod { get; set; }
        public long GPSZipCode { get; set; }
        public short UserType { get; set; }
        public string NotificationFor { get; set; }
    }

    public class DeviceInfo
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Email { get; set; }
        public string Devices { get; set; }
        public DateTime Createddate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
    }
    public class Notification
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Email { get; set; }
        public string Devices { get; set; }
        public string Message { get; set; }
        public string Title { get; set; }
        public DateTime Createddate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
    }

    public class UserUpdate
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public string FirstName { get; set; }
        public long YearofBirth { get; set; }
        public int Gender { get; set; }
        public bool IsSmoking { get; set; }
        public DateTime Createddate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public int createdBy { get; set; }
        public int UpdatedBy { get; set; }
        public bool IsNewsFromApp { get; set; }
        public bool IstermAndConditions { get; set; }
        public long ZipCode { get; set; }
        public List<int> ResProblemId { get; set; }
        public List<int> PollenId { get; set; }
        public List<int> NotificationFor { get; set; }
    }

    public class Advices
    {
        public int Id { get; set; }
        public string Zipcode { get; set; }
        public int AdviceType { get; set; }
        public string Advice { get; set; }
        public int LikesCount { get; set; }
        public int CommentsCount { get; set; }
        public int CreatedBy { get; set; }
        public DateTime CreatedDate { get; set; }
        public int UpdatedBy { get; set; }
        public DateTime UpdatedDate { get; set; }
        public DateTime AdviceHistoryDate { get; set; }
        public bool IsSendMessage { get; set; }
    }

    public class Pharmacy
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string Name { get; set; }
        public string Location { get; set; }
        public long ZipCode { get; set; }
        public DateTime DateOfVist { get; set; }
        public DateTime CreatedDate { get; set; }
        public int CreatedBy { get; set; }
        public int UpdatedBy { get; set; }
    }

    public class RespiratoryProblems
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class PollenList
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public DateTime CreatedDate { get; set; }
    }

    public class ProfileInfo
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public string FileName { get; set; }
        public string EmailId { get; set; }
        public string FilePath { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }
    public class PollenMessage
    {
        public int Id { get; set; }
        public string Message { get; set; }
        public string FileName { get; set; }
        public string UniqueFileName { get; set; }
        public bool IsFile { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }
    public class PollutionMessage
    {
        public int Id { get; set; }
        public string Message { get; set; }
        public string FileName { get; set; }
        public string UniqueFileName { get; set; }
        public bool IsFile { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }
    public class PdfFileUpload
    {
        public int Id { get; set; }
        public string PdfFileName { get; set; }
        public string FileName { get; set; }
        public string UniqueFileName { get; set; }
        public string TempFileName { get; set; }
        public string TempUniqueFileName { get; set; }
        public string FilePath { get; set; }
        public string TempFilePath { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }
    public class UserFeedBack
    {
        public int Id { get; set; }
        public int UserId { get; set; }
        public DateTime Date { get; set; }
        public int Level { get; set; }
        public string Symptoms { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
        public string Pollen { get; set; }
    }
    public class AdviceLikes
    {
        public int Id { get; set; }
        public int AdviceId { get; set; }
        public int UserId { get; set; }
    }
    public class CommentsLikes
    {
        public int Id { get; set; }
        public int AdviceId { get; set; }
        public int CommentId { get; set; }
        public int UserId { get; set; }
    }
    public class AdviceComments
    {
        public int Id { get; set; }
        public int AdviceId { get; set; }
        public int UserId { get; set; }
        public int LikesCount { get; set; }
        public string Comments { get; set; }
        public DateTime CreatedDate { get; set; }
    }
    public class CustomNotifications
    {
        public int Id { get; set; }
        public string Name { get; set; }
        public DateTime CreatedDate { get; set; }
    }
    public class SplashImage
    {
        public int Id { get; set; }
        public string FileName { get; set; }
        public string UniqueFileName { get; set; }
        public string FilePath { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }
    public class ReadNotification
    {
        [Key]
        public int ReadId { get; set; }
        public int UserId { get; set; }
        public int AdviceId { get; set; }
        //public int NotificationId { get; set; }
        public DateTime ReadDate { get; set; }
    }
    public class Sponsor
    {
        [Key]
        public int Id { get; set; }
        public string Content { get; set; }
        public string Link { get; set; }
        public DateTime CreatedDate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }
}