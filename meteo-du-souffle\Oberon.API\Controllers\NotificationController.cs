﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oberon.API.Helper;
using Oberon.BusinessLogic.BusinessHelper;
using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Oberon.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class NotificationController : ControllerBase
    {
        private readonly UserDBContext _context;
        NotificationHelper _notificationHelper;
        public NotificationController(UserDBContext context)
        {
            _context = context;
            _notificationHelper = new NotificationHelper();
        }

        #region Get AdviceList By ZipCode
        [HttpPost]
        [Route("advicebyzipCode")]
        public IActionResult GetAdviceList(AdviceInfoList model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<User> userlistResponse = new List<User>();

            try
            {
                userlistResponse = _context.Signup.Where(user => user.NotificationFor != null && user.NotificationFor != "").ToList();

                var response = (from s in userlistResponse
                                where (s.GPSZipCode.ToString().StartsWith(model.zipcode.ToString().Substring(0, 2))) &&
                                (model.NotificationFor != "" ? s.NotificationFor.Split(',').Any(x => model.NotificationFor.Split(',').Contains(x)) : s.NotificationFor != "") &&
                                s.DeciveId != null && s.DeciveId != ""
                                select s).ToList();

                if (response.Count() == 0 || response == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    #region Push Notification
                    var notificationresult = _notificationHelper.SendNotification(response, model.title, model.body).Result;
                    #endregion
                    if (notificationresult)
                    {
                        var data = _context.Find<Advices>(model.id);
                        data.IsSendMessage = true;
                        _context.Update(data);
                        _context.SaveChanges();

                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.NOTIFICATIONSUCCESS.GetDescription();
                        result = StatusCode((int)HttpStatusCode.OK, responseData);
                    }
                    else
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.NOTIFICATIONERROR.GetDescription();
                        result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                    }
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Save Notification Read
        [HttpPost]
        [Route("savenotificationread")]
        public IActionResult SaveNotificationRead(int userId, int adviceId)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                var readNotification = _context.ReadNotification.FirstOrDefault(rn => rn.UserId == userId && rn.AdviceId == adviceId);
                if (readNotification != null)
                {
                    _context.ReadNotification.Remove(readNotification);
                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    var newReadNotification = new ReadNotification
                    {
                        UserId = userId,
                        AdviceId = adviceId,
                        ReadDate = DateTime.UtcNow
                    };
                    _context.ReadNotification.Add(newReadNotification);
                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region unread Notification
        [HttpGet]
        [Route("notificationList")]
        public async Task<ActionResult<int>> GetNotificationList(int userId, string zipcode)
        {
            try
            {
                var unreadNotifications = await _context.Advices
                    .Where(notification => notification.Zipcode.StartsWith(zipcode.ToString().Substring(0, 2)) && notification.IsSendMessage == true)
                    .Where(notification => !_context.ReadNotification.Any(x => x.UserId == userId && x.AdviceId == notification.Id))
                    .Select(notification => new
                    {
                        NotificationId = notification.Id,
                        ZipCode = notification.Zipcode,
                        AdviceType = notification.AdviceType,
                        AdviceTypeName = notification.AdviceType == 1 ? "Pollen" : notification.AdviceType == 2 ? "Pollution" : notification.AdviceType == 3 ? "Tips Of The Day" : "",
                        Advice = notification.Advice,
                        LikeCount = notification.LikesCount,
                        CommentCount = _context.AdviceComments.Count(ac => ac.AdviceId == notification.Id),
                        CreatedBy = notification.CreatedBy,
                        CreatedDate = notification.CreatedDate,
                        UpdatedBy = notification.UpdatedBy,
                        UpdatedDate = notification.UpdatedDate,
                        AdviceHistoryDate = notification.AdviceHistoryDate,
                        IsSendMessage = notification.IsSendMessage,
                        UserLiked = _context.AdviceLikes.Any(x => x.UserId == userId && x.AdviceId == notification.Id),
                        CommentsLiked = _context.CommentsLikes.Any(x => x.UserId == userId && x.AdviceId == notification.Id)
                    }).OrderByDescending(notification => notification.CreatedDate).ToListAsync();

                return StatusCode((int)HttpStatusCode.OK, new ResponseData
                {
                    Status = true,
                    Message = ResponseMessageHelper.LIST.GetDescription(),
                    Data = unreadNotifications,
                    TotalCount = unreadNotifications.Count,
                });
            }
            catch (Exception)
            {
                return StatusCode((int)HttpStatusCode.BadRequest, new ResponseData
                {
                    Status = false,
                    Message = ResponseMessageHelper.MISTAKE.GetDescription()
                });
            }
        }
        #endregion

        #region Take Unread-Notification total Count
        [HttpGet]
        [Route("UnReadNotificationTotalCount")]
        public async Task<ActionResult<int>> GetUnReadNotificationTotalCount(int userId, string zipcode)
        {
            var totalCount = await _context.Advices
                .Where(notification => notification.Zipcode.StartsWith(zipcode.ToString().Substring(0, 2))&& notification.IsSendMessage == true && !_context.ReadNotification
                .Any(readNotification => readNotification.AdviceId == notification.Id && readNotification.UserId == userId)).ToListAsync();

            return StatusCode((int)HttpStatusCode.OK, new ResponseData
            {
                Status = true,
                Message = ResponseMessageHelper.LIST.GetDescription(),
                TotalCount = totalCount.Count,
            });
        }
        #endregion
    }
}