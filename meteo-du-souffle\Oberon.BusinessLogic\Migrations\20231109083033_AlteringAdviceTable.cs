﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class AlteringAdviceTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "AdvicesPollenId",
                table: "Advices");

            migrationBuilder.DropColumn(
                name: "AdvicesResProblemId",
                table: "Advices");

            migrationBuilder.DropColumn(
                name: "Duration",
                table: "Advices");

            migrationBuilder.DropColumn(
                name: "DurationEndDate",
                table: "Advices");

            migrationBuilder.DropColumn(
                name: "DurationStartDate",
                table: "Advices");

            migrationBuilder.DropColumn(
                name: "History",
                table: "Advices");

            migrationBuilder.AddColumn<int>(
                name: "NotificationType",
                table: "Advices",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "NotificationType",
                table: "Advices");

            migrationBuilder.AddColumn<string>(
                name: "AdvicesPollenId",
                table: "Advices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "AdvicesResProblemId",
                table: "Advices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "Duration",
                table: "Advices",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<DateTime>(
                name: "DurationEndDate",
                table: "Advices",
                type: "timestamp without time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<DateTime>(
                name: "DurationStartDate",
                table: "Advices",
                type: "timestamp without time zone",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified));

            migrationBuilder.AddColumn<string>(
                name: "History",
                table: "Advices",
                type: "text",
                nullable: true);
        }
    }
}
