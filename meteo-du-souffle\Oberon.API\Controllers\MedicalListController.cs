﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oberon.BusinessLogic.BusinessHelper;
using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Oberon.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class MedicalListController : ControllerBase
    {

        private readonly UserDBContext _context;
        UserBusinessHelper _userBusinessHelper;

        public MedicalListController(UserDBContext context)
        {
            _context = context;
            _userBusinessHelper = new UserBusinessHelper();
        }

        #region  Pharmacy Update
        //[Authorize]
        [HttpPost]
        [Route("pharmacyupdate")]
        public IActionResult PharmacyUpdate(Pharmacy model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                if (model.Id == 0)
                {
                    model.CreatedDate = DateTime.Now;
                    _context.Add(model);
                    _context.SaveChanges();
                    var data = _context.Pharmacy.OrderBy(x => x.Id).LastOrDefault();
                    var output = new
                    {
                        id = data.Id,
                        userId = data.UserId,
                        name = data.Name,
                        location = data.Location,
                        zipCode = data.ZipCode,
                        dateofVisit = data.DateOfVist,
                        createdDate = data.CreatedDate,
                        createdBy = data.CreatedBy
                    };
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    var datainfo = _context.Find<Pharmacy>(model.Id);
                    if (datainfo == null)
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                        result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                    }
                    else
                    {
                        datainfo.Name = model.Name;
                        datainfo.ZipCode = model.ZipCode;
                        datainfo.Location = model.Location;
                        datainfo.DateOfVist = model.DateOfVist;
                        datainfo.UpdatedBy = model.UpdatedBy;
                        _context.Update(datainfo);
                        _context.SaveChanges();

                        var data = _context.Find<Pharmacy>(model.Id);
                        var output = new
                        {
                            id = data.Id,
                            userId = data.UserId,
                            name = data.Name,
                            location = data.Location,
                            zipCode = data.ZipCode,
                            dateofVisit = data.DateOfVist,
                            createdDate = data.CreatedDate,
                            createdBy = data.CreatedBy
                        };

                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                        responseData.Data = output;
                        result = StatusCode((int)HttpStatusCode.OK, responseData);
                    }
                }
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Pharmacy List
        //[Authorize]
        [HttpPost]
        [Route("pharmacylist")]
        public async Task<IActionResult> PharmacyList(PharmacyInfo model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                List<PharmacyListResponse> userlistResponse = await _context.Pharmacy.GroupBy(x => new { x.Name, x.Location, x.ZipCode })
                     .Select(s => new PharmacyListResponse
                     {
                         Location = s.Key.Location,
                         Name = s.Key.Name,
                         ZipCode = s.Key.ZipCode,
                         dateofVisit = _context.Pharmacy.Where(y => y.Name == s.Key.Name).Select(d => d.DateOfVist).OrderByDescending(i => i.Date).FirstOrDefault(),
                         UserCount = s.Select(z => new { z.UserId, z.Name }).Select(y => y.UserId).Distinct().Count()
                     }).ToListAsync();

                var response = (from s in userlistResponse
                                where (model.searchName != "" ? s.Name.ToLower().Trim().Contains(model.searchName.ToLower().Trim()) : s.Name != "") &&
                                (model.searchLocation != "" ? s.Location.ToLower().Trim().Contains(model.searchLocation.ToLower().Trim()) : s.Location != "") &&
                                (model.searchZipCode != "" ? s.ZipCode.ToString().Trim().Contains(model.searchZipCode.ToString().Trim()) : s.ZipCode != 0) &&
                                (model.searchusercount != 0 ? s.UserCount.ToString().Contains(model.searchusercount.ToString()) : s.UserCount != 0)
                                select s).ToList();

                if (model.startDateofVisit != null && model.endDateofVisit != null)
                {
                    response = response.Where(a => a.dateofVisit.Date >= model.startDateofVisit && a.dateofVisit.Date <= model.endDateofVisit).ToList();
                }

                if (response.Count() == 0 || response == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    model.pageNumber = model.pageNumber == 0 ? 1 : model.pageNumber;
                    model.pageSize = model.pageSize == 0 ? 5 : model.pageSize;
                    var userData = _userBusinessHelper.PhamracyResponseSortHelper(model.sortBy, response);
                    responseData.Status = true;
                    responseData.Data = userData.Skip((model.pageNumber - 1) * model.pageSize).Take(model.pageSize).ToList();
                    responseData.TotalCount = response.Count;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
            
            }
            catch (Exception)
            { 
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Get Pharmacy User By Id
        //[Authorize]
        [HttpPost]
        [Route("pharmacyuserbyid")]
        public async Task<IActionResult> GetUser(PharmacyDeatils model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                List<PharmacyListResponse> advicelist = await _context.Pharmacy.Where(c => c.UserId == model.id).Select(x => new PharmacyListResponse
                {
                    Location = x.Location,
                    Name = x.Name,
                    ZipCode = x.ZipCode,
                    dateofVisit = x.DateOfVist.Date,
                }).Distinct().ToListAsync();

                var User = (from s in advicelist
                            where (model.searchName != "" ? s.Name.ToLower().Trim().Contains(model.searchName.ToLower().Trim()) : s.Name != "") &&
                                (model.searchLocation != "" ? s.Location.ToLower().Trim().Contains(model.searchLocation.ToLower().Trim()) : s.Location != "") &&
                                (model.searchZipCode != "" ? s.ZipCode.ToString().Trim().Contains(model.searchZipCode.ToString().Trim()) : s.ZipCode != 0)
                                select s).ToList();

                if (model.startDateofVisit != null && model.endDateofVisit != null)
                {
                    User = User.Where(a => a.dateofVisit.Date >= model.startDateofVisit && a.dateofVisit.Date <= model.endDateofVisit).ToList();
                }

                if (User.Count() == 0 || User == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    responseData.Data = User;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    model.pageNumber = model.pageNumber == 0 ? 1 : model.pageNumber;
                    model.pageSize = model.pageSize == 0 ? 5 : model.pageSize;
                    var userData = _userBusinessHelper.PhamracyResponseSortHelper(model.sortBy, User);
                    responseData.Status = true;
                    responseData.Data = userData.Skip((model.pageNumber - 1) * model.pageSize).Take(model.pageSize).ToList();
                    responseData.TotalCount = User.Count;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion
    }
}
