{"runtimeTarget": {"name": ".NETCoreApp,Version=v3.1", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v3.1": {"Oberon.BusinessLogic/1.0.0": {"dependencies": {"AspNetCore.Firebase.Authentication": "2.0.1", "FireSharp": "2.0.4", "Firebase.Auth": "1.0.0", "FirebaseAdmin": "2.3.0", "Microsoft.EntityFrameworkCore.Design": "5.0.16", "Microsoft.EntityFrameworkCore.Tools": "5.0.16", "Microsoft.NETCore.App": "2.2.8", "Npgsql.EntityFrameworkCore.PostgreSQL": "5.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL.Design": "1.1.0", "Swashbuckle.AspNetCore": "6.3.1", "Swashbuckle.AspNetCore.Swagger": "6.3.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.3.1"}, "runtime": {"Oberon.BusinessLogic.dll": {}}}, "AspNetCore.Firebase.Authentication/2.0.1": {"dependencies": {"Microsoft.AspNetCore": "2.0.2", "Microsoft.AspNetCore.Authentication.JwtBearer": "2.0.3", "Microsoft.AspNetCore.Authentication.OAuth": "2.0.3"}, "runtime": {"lib/netstandard2.0/AspNetCore.Firebase.Authentication.dll": {"assemblyVersion": "2.0.1.0", "fileVersion": "2.0.1.0"}}}, "Firebase.Auth/1.0.0": {"dependencies": {"Microsoft.NETCore.Portable.Compatibility": "1.0.1", "NETStandard.Library": "2.0.3", "Newtonsoft.Json": "12.0.3"}, "runtime": {"lib/netstandard1.1/Firebase.Auth.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "1.0.0.0"}}}, "FirebaseAdmin/2.3.0": {"dependencies": {"Google.Api.Gax.Rest": "3.2.0", "Google.Apis.Auth": "1.49.0", "System.Collections.Immutable": "5.0.0"}, "runtime": {"lib/netstandard2.0/FirebaseAdmin.dll": {"assemblyVersion": "2.3.0.0", "fileVersion": "2.3.0.0"}}}, "FireSharp/2.0.4": {"dependencies": {"Microsoft.Bcl.Async": "1.0.168", "Microsoft.Net.Http": "2.2.28", "Newtonsoft.Json": "12.0.3"}, "runtime": {"lib/portable-net45+sl5+wp8+win8/FireSharp.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Google.Api.Gax/3.2.0": {"dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.0.0", "Newtonsoft.Json": "12.0.3"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Google.Api.Gax.Rest/3.2.0": {"dependencies": {"Google.Api.Gax": "3.2.0", "Google.Apis.Auth": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Api.Gax.Rest.dll": {"assemblyVersion": "3.2.0.0", "fileVersion": "3.2.0.0"}}}, "Google.Apis/1.49.0": {"dependencies": {"Google.Apis.Core": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}}, "Google.Apis.Auth/1.49.0": {"dependencies": {"Google.Apis": "1.49.0", "Google.Apis.Core": "1.49.0"}, "runtime": {"lib/netstandard2.0/Google.Apis.Auth.PlatformServices.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}, "lib/netstandard2.0/Google.Apis.Auth.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}}, "Google.Apis.Core/1.49.0": {"dependencies": {"Newtonsoft.Json": "12.0.3"}, "runtime": {"lib/netstandard2.0/Google.Apis.Core.dll": {"assemblyVersion": "1.49.0.0", "fileVersion": "1.49.0.0"}}}, "Humanizer.Core/2.8.26": {"runtime": {"lib/netstandard2.0/Humanizer.dll": {"assemblyVersion": "2.8.0.0", "fileVersion": "2.8.26.1919"}}}, "Libuv/1.10.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4"}, "runtimeTargets": {"runtimes/linux-arm/native/libuv.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libuv.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-armel/native/libuv.so": {"rid": "linux-armel", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libuv.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/osx/native/libuv.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-arm/native/libuv.dll": {"rid": "win-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libuv.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libuv.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Microsoft.AspNetCore/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Diagnostics": "2.0.2", "Microsoft.AspNetCore.Hosting": "2.0.2", "Microsoft.AspNetCore.Routing": "2.0.2", "Microsoft.AspNetCore.Server.IISIntegration": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Https": "2.0.2", "Microsoft.Extensions.Configuration.CommandLine": "2.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.0.1", "Microsoft.Extensions.Configuration.Json": "2.0.1", "Microsoft.Extensions.Configuration.UserSecrets": "2.0.1", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Logging.Configuration": "2.0.1", "Microsoft.Extensions.Logging.Console": "2.0.1", "Microsoft.Extensions.Logging.Debug": "2.0.1"}}, "Microsoft.AspNetCore.Authentication/2.0.3": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.0.2", "Microsoft.AspNetCore.DataProtection": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.WebEncoders": "2.0.1"}}, "Microsoft.AspNetCore.Authentication.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Authentication.Core/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2"}}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.0.3": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.0.3", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "2.1.4"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Authentication.JwtBearer.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.3.18051"}}}, "Microsoft.AspNetCore.Authentication.OAuth/2.0.3": {"dependencies": {"Microsoft.AspNetCore.Authentication": "2.0.3", "Newtonsoft.Json": "12.0.3"}}, "Microsoft.AspNetCore.Cryptography.Internal/2.0.2": {}, "Microsoft.AspNetCore.DataProtection/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Cryptography.Internal": "2.0.2", "Microsoft.AspNetCore.DataProtection.Abstractions": "2.0.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Win32.Registry": "4.4.0", "System.Security.Cryptography.Xml": "4.4.0"}}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.0.2": {}, "Microsoft.AspNetCore.Diagnostics/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Diagnostics.Abstractions": "2.0.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.FileProviders.Physical": "2.0.1", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Reflection.Metadata": "1.5.0"}}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.0.2": {}, "Microsoft.AspNetCore.Hosting/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.Extensions.Configuration": "2.0.1", "Microsoft.Extensions.Configuration.EnvironmentVariables": "2.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.0.1", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.FileProviders.Physical": "2.0.1", "Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Reflection.Metadata": "1.5.0"}}, "Microsoft.AspNetCore.Hosting.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting.Server.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "2.0.1", "Microsoft.Extensions.Hosting.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.2", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.AspNetCore.Http/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.ObjectPool": "2.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.0.2"}}, "Microsoft.AspNetCore.Http.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.2", "System.Text.Encodings.Web": "4.4.0"}}, "Microsoft.AspNetCore.Http.Extensions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.Extensions.FileProviders.Abstractions": "2.0.1", "Microsoft.Net.Http.Headers": "2.0.2", "System.Buffers": "4.4.0"}}, "Microsoft.AspNetCore.Http.Features/2.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.AspNetCore.HttpOverrides/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Routing/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.AspNetCore.Routing.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.ObjectPool": "2.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2"}}, "Microsoft.AspNetCore.Server.IISIntegration/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Authentication.Core": "2.0.2", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Http": "2.0.2", "Microsoft.AspNetCore.Http.Extensions": "2.0.2", "Microsoft.AspNetCore.HttpOverrides": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Security.Principal.Windows": "4.4.0"}}, "Microsoft.AspNetCore.Server.Kestrel/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv": "2.0.2"}}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.0.2", "Microsoft.AspNetCore.WebUtilities": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Net.Http.Headers": "2.0.2", "System.Threading.Tasks.Extensions": "4.4.0"}}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Abstractions": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Core": "2.0.2"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Https.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.2.18051"}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.0.2": {"dependencies": {"Microsoft.AspNetCore.Http.Features": "2.0.2", "System.Buffers": "4.4.0", "System.Numerics.Vectors": "4.4.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.2.18051"}}}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv/2.0.2": {"dependencies": {"Libuv": "1.10.0", "Microsoft.AspNetCore.Hosting.Abstractions": "2.0.2", "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions": "2.0.2", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv.dll": {"assemblyVersion": "*******", "fileVersion": "2.0.2.18051"}}}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"dependencies": {"Microsoft.Net.Http.Headers": "2.0.2", "System.Text.Encodings.Web": "4.4.0"}}, "Microsoft.Bcl/1.1.9": {"dependencies": {"Microsoft.Bcl.Build": "1.0.14"}}, "Microsoft.Bcl.Async/1.0.168": {"dependencies": {"Microsoft.Bcl": "1.1.9"}, "runtime": {"lib/net40/Microsoft.Threading.Tasks.Extensions.Desktop.dll": {"assemblyVersion": "*********", "fileVersion": "*********"}, "lib/net40/Microsoft.Threading.Tasks.Extensions.dll": {"assemblyVersion": "********", "fileVersion": "*********"}, "lib/net40/Microsoft.Threading.Tasks.dll": {"assemblyVersion": "********", "fileVersion": "*********"}}}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"runtime": {"lib/netstandard2.1/Microsoft.Bcl.AsyncInterfaces.dll": {"assemblyVersion": "1.0.0.0", "fileVersion": "4.700.19.46214"}}}, "Microsoft.Bcl.Build/1.0.14": {}, "Microsoft.CSharp/4.7.0": {}, "Microsoft.EntityFrameworkCore/5.0.16": {"dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.16", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.16", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1622.16102"}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.16": {"runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1622.16102"}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.16": {}, "Microsoft.EntityFrameworkCore.Design/5.0.16": {"dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "5.0.16"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Design.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1622.16102"}}}, "Microsoft.EntityFrameworkCore.Relational/5.0.16": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.16", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {"assemblyVersion": "********", "fileVersion": "5.0.1622.16102"}}}, "Microsoft.EntityFrameworkCore.Relational.Design/1.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore.Relational": "5.0.16", "NETStandard.Library": "2.0.3"}, "runtime": {"lib/netstandard1.3/Microsoft.EntityFrameworkCore.Relational.Design.dll": {"assemblyVersion": "*******", "fileVersion": "1.1.0.21115"}}}, "Microsoft.EntityFrameworkCore.Tools/5.0.16": {"dependencies": {"Microsoft.EntityFrameworkCore.Design": "5.0.16"}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Configuration.Binder/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1"}}, "Microsoft.Extensions.Configuration.CommandLine/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1"}}, "Microsoft.Extensions.Configuration.FileExtensions/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1", "Microsoft.Extensions.FileProviders.Physical": "2.0.1"}}, "Microsoft.Extensions.Configuration.Json/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration": "2.0.1", "Microsoft.Extensions.Configuration.FileExtensions": "2.0.1", "Newtonsoft.Json": "12.0.3"}}, "Microsoft.Extensions.Configuration.UserSecrets/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Json": "2.0.1"}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {"assemblyVersion": "5.0.0.1", "fileVersion": "5.0.821.31504"}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.FileProviders.Abstractions/2.0.1": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}}, "Microsoft.Extensions.FileProviders.Physical/2.0.1": {"dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "2.0.1", "Microsoft.Extensions.FileSystemGlobbing": "2.0.1"}}, "Microsoft.Extensions.FileSystemGlobbing/2.0.1": {}, "Microsoft.Extensions.Hosting.Abstractions/2.0.2": {}, "Microsoft.Extensions.Logging/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Logging.Configuration/2.0.1": {"dependencies": {"Microsoft.Extensions.Logging": "5.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "2.0.1"}}, "Microsoft.Extensions.Logging.Console/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Logging": "5.0.0"}}, "Microsoft.Extensions.Logging.Debug/2.0.1": {"dependencies": {"Microsoft.Extensions.Logging": "5.0.0"}}, "Microsoft.Extensions.ObjectPool/2.0.0": {}, "Microsoft.Extensions.Options/5.0.0": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.1": {"dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "5.0.0", "Microsoft.Extensions.Configuration.Binder": "2.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0"}}, "Microsoft.Extensions.Primitives/5.0.0": {"runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "Microsoft.Extensions.WebEncoders/2.0.1": {"dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Text.Encodings.Web": "4.4.0"}}, "Microsoft.IdentityModel.Logging/1.1.4": {"dependencies": {"System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Logging.dll": {"assemblyVersion": "1.1.4.0", "fileVersion": "1.1.4.216"}}}, "Microsoft.IdentityModel.Protocols/2.1.4": {"dependencies": {"System.Collections.Specialized": "4.3.0", "System.Diagnostics.Contracts": "4.3.0", "System.IdentityModel.Tokens.Jwt": "5.1.4", "System.Net.Http": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.dll": {"assemblyVersion": "2.1.4.0", "fileVersion": "2.1.4.216"}}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/2.1.4": {"dependencies": {"Microsoft.IdentityModel.Protocols": "2.1.4", "System.Dynamic.Runtime": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Protocols.OpenIdConnect.dll": {"assemblyVersion": "2.1.4.0", "fileVersion": "2.1.4.216"}}}, "Microsoft.IdentityModel.Tokens/5.1.4": {"dependencies": {"Microsoft.IdentityModel.Logging": "1.1.4", "Newtonsoft.Json": "12.0.3", "System.Collections": "4.3.0", "System.Diagnostics.Tools": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.InteropServices.RuntimeInformation": "4.3.0", "System.Security.Claims": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading": "4.3.0", "System.Xml.ReaderWriter": "4.3.0"}, "runtime": {"lib/netstandard1.4/Microsoft.IdentityModel.Tokens.dll": {"assemblyVersion": "5.1.4.0", "fileVersion": "5.1.4.216"}}}, "Microsoft.Net.Http/2.2.28": {"dependencies": {"Microsoft.Bcl": "1.1.9", "Microsoft.Bcl.Build": "1.0.14"}, "runtime": {"lib/net45/System.Net.Http.Extensions.dll": {"assemblyVersion": "********", "fileVersion": "********"}, "lib/net45/System.Net.Http.Primitives.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Microsoft.Net.Http.Headers/2.0.2": {"dependencies": {"Microsoft.Extensions.Primitives": "5.0.0", "System.Buffers": "4.4.0"}}, "Microsoft.NETCore.App/2.2.8": {"dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.2.8", "Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "NETStandard.Library": "2.0.3"}}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.8"}}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.8"}}, "Microsoft.NETCore.Jit/1.0.2": {}, "Microsoft.NETCore.Platforms/2.2.4": {}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"dependencies": {"Microsoft.NETCore.Runtime.CoreCLR": "1.0.2"}, "runtime": {"lib/netstandard1.0/System.ServiceModel.dll": {"assemblyVersion": "*******", "fileVersion": "4.6.24214.0"}}}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"dependencies": {"Microsoft.NETCore.Jit": "1.0.2", "Microsoft.NETCore.Windows.ApiSets": "1.0.1"}}, "Microsoft.NETCore.Targets/2.0.0": {}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Microsoft.Win32.Registry/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Security.AccessControl": "4.4.0", "System.Security.Principal.Windows": "4.4.0"}}, "NETStandard.Library/2.0.3": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4"}}, "Newtonsoft.Json/12.0.3": {"runtime": {"lib/netstandard2.0/Newtonsoft.Json.dll": {"assemblyVersion": "********", "fileVersion": "12.0.3.23909"}}}, "Npgsql/5.0.10": {"dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.6.0"}, "runtime": {"lib/netcoreapp3.1/Npgsql.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/5.0.10": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.16", "Microsoft.EntityFrameworkCore.Abstractions": "5.0.16", "Microsoft.EntityFrameworkCore.Relational": "5.0.16", "Npgsql": "5.0.10"}, "runtime": {"lib/netstandard2.1/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design/1.1.0": {"dependencies": {"Microsoft.EntityFrameworkCore": "5.0.16", "Microsoft.EntityFrameworkCore.Relational": "5.0.16", "Microsoft.EntityFrameworkCore.Relational.Design": "1.1.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Npgsql": "5.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL": "5.0.10"}, "runtime": {"lib/netstandard1.3/Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.native.System/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0"}}, "runtime.native.System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0"}}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"dependencies": {"runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple": "4.3.0"}}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0", "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {}, "Swashbuckle.AspNetCore/6.3.1": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.3.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.3.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.3.1"}}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.3.1"}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Buffers/4.4.0": {}, "System.Collections/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Collections.Concurrent/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Collections.Immutable/5.0.0": {"runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Collections.NonGeneric/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Collections.Specialized/4.3.0": {"dependencies": {"System.Collections.NonGeneric": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.ComponentModel.Annotations/5.0.0": {"runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "System.Diagnostics.Contracts/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Diagnostics.Debug/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.220.61120"}}}, "System.Diagnostics.Tools/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Tracing/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Dynamic.Runtime/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Globalization/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Calendars/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Globalization": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Globalization.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0"}}, "System.IdentityModel.Tokens.Jwt/5.1.4": {"dependencies": {"Microsoft.IdentityModel.Tokens": "5.1.4"}, "runtime": {"lib/netstandard1.4/System.IdentityModel.Tokens.Jwt.dll": {"assemblyVersion": "5.1.4.0", "fileVersion": "5.1.4.216"}}}, "System.IO/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.IO": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.IO.FileSystem.Primitives/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Linq/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Net.Http/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Diagnostics.DiagnosticSource": "5.0.1", "System.Diagnostics.Tracing": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Extensions": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.Net.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Security.Cryptography.X509Certificates": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Net.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Numerics.Vectors/4.4.0": {}, "System.ObjectModel/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit/4.3.0": {"dependencies": {"System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.ILGeneration/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata/1.5.0": {}, "System.Reflection.Primitives/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0"}}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {}, "System.Runtime.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Runtime.Handles/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Runtime.InteropServices/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Handles": "4.3.0"}}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0"}}, "System.Runtime.Numerics/4.3.0": {"dependencies": {"System.Globalization": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Security.AccessControl/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Security.Principal.Windows": "4.4.0"}}, "System.Security.Claims/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Security.Principal": "4.3.0"}}, "System.Security.Cryptography.Algorithms/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.Apple": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Cng/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Security.Cryptography.Csp/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.IO": "4.3.0", "System.Reflection": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0"}}, "System.Security.Cryptography.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Collections": "4.3.0", "System.Collections.Concurrent": "4.3.0", "System.Linq": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.OpenSsl/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Primitives/4.3.0": {"dependencies": {"System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Security.Cryptography.X509Certificates/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.Globalization.Calendars": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.Handles": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Runtime.Numerics": "4.3.0", "System.Security.Cryptography.Algorithms": "4.3.0", "System.Security.Cryptography.Cng": "4.3.0", "System.Security.Cryptography.Csp": "4.3.0", "System.Security.Cryptography.Encoding": "4.3.0", "System.Security.Cryptography.OpenSsl": "4.3.0", "System.Security.Cryptography.Primitives": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading": "4.3.0", "runtime.native.System": "4.3.0", "runtime.native.System.Net.Http": "4.3.0", "runtime.native.System.Security.Cryptography.OpenSsl": "4.3.0"}}, "System.Security.Cryptography.Xml/4.4.0": {}, "System.Security.Principal/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Security.Principal.Windows/4.4.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4"}}, "System.Text.Encoding/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding.Extensions/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0"}}, "System.Text.Encodings.Web/4.4.0": {}, "System.Text.RegularExpressions/4.3.0": {"dependencies": {"System.Runtime": "4.3.0"}}, "System.Threading/4.3.0": {"dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks/4.3.0": {"dependencies": {"Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions/4.4.0": {}, "System.Xml.ReaderWriter/4.3.0": {"dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.IO.FileSystem": "4.3.0", "System.IO.FileSystem.Primitives": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Runtime.InteropServices": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Text.Encoding.Extensions": "4.3.0", "System.Text.RegularExpressions": "4.3.0", "System.Threading.Tasks": "4.3.0", "System.Threading.Tasks.Extensions": "4.4.0"}}}}, "libraries": {"Oberon.BusinessLogic/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "AspNetCore.Firebase.Authentication/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ANZ0Ty2nDI0RygVpFgabAAEj8CWwMgHArjEFWvgnVMVKKOaTAzbLeaGGpg+hI/39OWAOzNFytTG0vofWQjaQPA==", "path": "aspnetcore.firebase.authentication/2.0.1", "hashPath": "aspnetcore.firebase.authentication.2.0.1.nupkg.sha512"}, "Firebase.Auth/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MRAGIRDXqB0jRvtJx0yRmTMjZInQcUSyHN7LuOwxL/nfyceDEA+Gfiimn7HtI2ntHw3hdUAoeeCA6BCc3qg0/A==", "path": "firebase.auth/1.0.0", "hashPath": "firebase.auth.1.0.0.nupkg.sha512"}, "FirebaseAdmin/2.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ror9V68bFWdr3VXoCPQrAAJ1ZA8dyLzIHJvy53BmoD83Ze9VuWC9hPBicPqqOANP3VTMWnylC7xci59UEMuU4A==", "path": "firebaseadmin/2.3.0", "hashPath": "firebaseadmin.2.3.0.nupkg.sha512"}, "FireSharp/2.0.4": {"type": "package", "serviceable": true, "sha512": "sha512-laSHpVUBG6iCTUixmpeUmYxie2zbziOBM+LU0v2A0ETE9dUyBA+8aY63xql/o8LXj+kFybBLYUKBUrLCZWaq+A==", "path": "firesharp/2.0.4", "hashPath": "firesharp.2.0.4.nupkg.sha512"}, "Google.Api.Gax/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-0<PERSON>jahFAHTOoprSgvJiQ6/fIQLrUYU4QIFgkuJ51/lcmhZbuXxB3ycPk3JTVEvx6A5yQBL14wgmHgwXLcEsnu3Q==", "path": "google.api.gax/3.2.0", "hashPath": "google.api.gax.3.2.0.nupkg.sha512"}, "Google.Api.Gax.Rest/3.2.0": {"type": "package", "serviceable": true, "sha512": "sha512-YY4mD0nGxTx1uez7Perm+zAd3FH50dd3+7HTYsRFCywDEtj3RkrMjcAmw6mNpKkw2sRICu7aYNy1mgMjd3nVbw==", "path": "google.api.gax.rest/3.2.0", "hashPath": "google.api.gax.rest.3.2.0.nupkg.sha512"}, "Google.Apis/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-fmXQQTxZFOBlnvokvdQMzq0Lt+g2QzpZ4H6U8lMMFHgAR8ZqxQnPN5yHDpoHf7a++hJHb5W3pALxauGsq+afKQ==", "path": "google.apis/1.49.0", "hashPath": "google.apis.1.49.0.nupkg.sha512"}, "Google.Apis.Auth/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-3V9ohvixQtjaEvk7T9Ac7E/KvwEPa7eL4aMNreJDI0CEPzCdQdk2zCvaJPRrNIjhe+UuBeOeom1oAOMFB74JHg==", "path": "google.apis.auth/1.49.0", "hashPath": "google.apis.auth.1.49.0.nupkg.sha512"}, "Google.Apis.Core/1.49.0": {"type": "package", "serviceable": true, "sha512": "sha512-9DgGdtyzgrCfHWwc/HiDXDbykNMeKQozbEHYEUEcezRuH+YR3fvq7YGVBDmUM8g6qEL3kDk6h5EU4h0IJwue1w==", "path": "google.apis.core/1.49.0", "hashPath": "google.apis.core.1.49.0.nupkg.sha512"}, "Humanizer.Core/2.8.26": {"type": "package", "serviceable": true, "sha512": "sha512-OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "path": "humanizer.core/2.8.26", "hashPath": "humanizer.core.2.8.26.nupkg.sha512"}, "Libuv/1.10.0": {"type": "package", "serviceable": true, "sha512": "sha512-GsCf4q+eyaI49rCPlgYxdxa1SQCysXFFdSJWdstrwxytg4+VPYLYrXD4AT2rjHVJ+UF7SSWX9CapWEYaU4ejVQ==", "path": "libuv/1.10.0", "hashPath": "libuv.1.10.0.nupkg.sha512"}, "Microsoft.AspNetCore/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-M1kweIFWsyqHnY4W8Jqwz/tuVKF7Ff1mokn9+jpMs+S8m1wlGKeqmy9ovNF1rJoSTnF97cb4Wn0JoTA84bCYSQ==", "path": "microsoft.aspnetcore/2.0.2", "hashPath": "microsoft.aspnetcore.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-11a6DvTSur4T62bf/l0nb1uS0h0vXfOiAMCwDYqFuR1Pkox8v9eiTgduyxDppmEQuAh3TboPhYY3TzufEAFK3Q==", "path": "microsoft.aspnetcore.authentication/2.0.3", "hashPath": "microsoft.aspnetcore.authentication.2.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-12+IIkf+5eM/fNch3k+nj8nzIeaQYBF87TxZZ3Uf42wPoMuGzc8nMx8fMQDyqKtzJJ+9WCnH7N9N8ekTz9F7xg==", "path": "microsoft.aspnetcore.authentication.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.authentication.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.Core/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-qA2YEcpU02rBZvtOaZk4RPIBqneGAzkS0dBQXcHk31cvf5bbzj+FHENmTKgsXDADyKVR0U1+7kS+bc44JxGCVA==", "path": "microsoft.aspnetcore.authentication.core/2.0.2", "hashPath": "microsoft.aspnetcore.authentication.core.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.JwtBearer/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-AwYc5nGOWkpUHRd5JI3ummWJTciuvjskL7zIfgGgFwhaK3l8ZeDTHpHyTXW+Zjn69Cq+FRSLNiuEkAWQVJ8APQ==", "path": "microsoft.aspnetcore.authentication.jwtbearer/2.0.3", "hashPath": "microsoft.aspnetcore.authentication.jwtbearer.2.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Authentication.OAuth/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-cuQYTKA/u5/uY5Wxu8OyLRUAt3U7kGyBmHwHvWz83vseBsnvso+qp+KX9syr/5PfkEvzub1RCvctB2NCRz5vNQ==", "path": "microsoft.aspnetcore.authentication.oauth/2.0.3", "hashPath": "microsoft.aspnetcore.authentication.oauth.2.0.3.nupkg.sha512"}, "Microsoft.AspNetCore.Cryptography.Internal/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-pCJyY7vC6YWY94ssKcgGzVFGsK/bk7RVEH/BxwHmc+T3t5VmXlBq7VvUmhLfk+P5Uc1l0hDIJX0ZJRLy9Sz1jg==", "path": "microsoft.aspnetcore.cryptography.internal/2.0.2", "hashPath": "microsoft.aspnetcore.cryptography.internal.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-BXVpydukX6AjcnELAZHtTNexSdGLwJ21suskAtDgQshDz/mfySm0Z/voNzQyPFF6SMzDf7iXnXpEBMZchL18Rg==", "path": "microsoft.aspnetcore.dataprotection/2.0.2", "hashPath": "microsoft.aspnetcore.dataprotection.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.DataProtection.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Q4eEkEE527CR1qzfyVeTGDVL3mss2D0VKSMWJCwhzxVmSDFy3zyXaJfCDu39GnExAVM9gLKzkoU6KoJGu3vyAg==", "path": "microsoft.aspnetcore.dataprotection.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.dataprotection.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-fAsBgV/202K4ZMB3eFLWAXYRqUz4uf9CR9MwpNYJhMhO+yHxNPGDFBatsiKUVxG4oeMdhFXzYwUbUSaWUYU/7Q==", "path": "microsoft.aspnetcore.diagnostics/2.0.2", "hashPath": "microsoft.aspnetcore.diagnostics.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Diagnostics.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-4Zb2/cIFGfyHhPMr1tg1Tyuur4PK9Nr5uKnRLxHPJJh1OuAwEAZtUsPHcUa6HHNoA5tZhUFonHJwiFTy9+ZLLA==", "path": "microsoft.aspnetcore.diagnostics.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.diagnostics.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-qKV9PnsiVC2J1ws1DPoQ1fX3bowLTK2WjXPXpItgKVbuuLSWM1ECoObX2fOkQt6FKt4vJ9i4j/hktFavxova1Q==", "path": "microsoft.aspnetcore.hosting/2.0.2", "hashPath": "microsoft.aspnetcore.hosting.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-358NTTCWJWpDKno3S85BU0hjxWQ8EzsyjZ5OSMi2XpQ9SrYwzTq6tlXSpVS3cV2RJ2Jx9lXc8uSXFwrOVyUieQ==", "path": "microsoft.aspnetcore.hosting.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.hosting.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Hosting.Server.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-tvz7D661JTyJXRxWLqOSH0s1zF9bLviZd14aA8poR+srvldS0gg1j62e7SaM5LQrUn+Z4dPwJqBtLXZDj5PtYw==", "path": "microsoft.aspnetcore.hosting.server.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.hosting.server.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-oVmQJvA1dHr96VcJVyUYEPcQH+FHSJSEu52Fq6aB7rmpjtyxlcFzyvRNumD4J1QJjlhE/V8jF10lY2hH0J6h4w==", "path": "microsoft.aspnetcore.http/2.0.2", "hashPath": "microsoft.aspnetcore.http.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-yQM9JzPAExsxTqvJBBr3yC+6XyOETi2T/eOOBjrOOnYgQOO+7M7J8VvAW0wQID9zh7QqWO6kh3BGCT/aqvydtg==", "path": "microsoft.aspnetcore.http.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.http.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Extensions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-z9uJ6w3BnhjWZZW+i5rVCqKIVLmngLP1AutfOJXJKtXKjAOBqWSTBgySGROqzWkPuDXot1dHVP7NAMnhtloIiQ==", "path": "microsoft.aspnetcore.http.extensions/2.0.2", "hashPath": "microsoft.aspnetcore.http.extensions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Http.Features/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-1U5fPSOtIq+cPuqJTjN+EFN3dWn4ptSjybd8minSbyhy0oXr8ujYla86kb9kM3rddUBgrGCyTp/hf0/tMZab+g==", "path": "microsoft.aspnetcore.http.features/2.0.2", "hashPath": "microsoft.aspnetcore.http.features.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.HttpOverrides/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hZPYYSnG17A+fFws1R5eQBmzF/9zewVlsBk/XeXTQ8fmjY8fUaOyBQGrs3OWKRXtRt3D1VetJ+ngZFl3a5YS9g==", "path": "microsoft.aspnetcore.httpoverrides/2.0.2", "hashPath": "microsoft.aspnetcore.httpoverrides.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Routing/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-v0f0iRS9H71g49cwNH8hezpZalluUc1Ok3sModvqC4heLdqfAAO52GxWYVtB6lOw5JR6YYy3KvINOx+YghsdHg==", "path": "microsoft.aspnetcore.routing/2.0.2", "hashPath": "microsoft.aspnetcore.routing.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Routing.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-sqI4xsQYm/11KsY8P892yrpL3ALAp6e6u12mrnbdWhQt/IiWhK4X9OIQVVMM+ofrPkAKsjP96ctEkJcDKysNVw==", "path": "microsoft.aspnetcore.routing.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.routing.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.IISIntegration/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-UUbQIZp5dmEnDrgjIGjiTqqMBlus1+q+nL0JTmo40UveFVMO4rQSBMwv7M9QzR+T1qFCWNcysbutHIOdoYl8bA==", "path": "microsoft.aspnetcore.server.iisintegration/2.0.2", "hashPath": "microsoft.aspnetcore.server.iisintegration.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-rPDyGoafAZwRvovro5wzmeaOScYjehjy7yABvgMfkkiPTUeSDdtm020XR3HFU+GxCAmhU8bQhLUH0CKk9NNGDQ==", "path": "microsoft.aspnetcore.server.kestrel/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Core/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-+d7WB++otIdpV10mbHsUEcPmL+676Zljsls4DUkaSB8toiYndEeK+yxXj9OsGtTCzQhv4FjLqEcgw01oA0JYbw==", "path": "microsoft.aspnetcore.server.kestrel.core/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.core.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Https/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-v8WKn9TCiGvgocbCFDxeOj3neAgEHwfpqu/J4W2GbwprRDawFLP5XbTDjbNjo5J2UVgFH5NHaRJocNWc3raQ9g==", "path": "microsoft.aspnetcore.server.kestrel.https/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.https.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-25BwaKnlKHZqPnOT1De2Oe7kpwWWxb7eMrnJx2FPyN5N4rfn/3GaSC72nZzwT4us9e8vKUJP+uzo1yFEBblbXA==", "path": "microsoft.aspnetcore.server.kestrel.transport.abstractions/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.Server.Kestrel.Transport.Libuv/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-3H5R93EodGu8WsPYJwjXyDwks+nvpso6F01qPiowWU1dHpPGsY8px3XX3QTX3vPlwCXjpwvwlDXY8AT7kgBJzg==", "path": "microsoft.aspnetcore.server.kestrel.transport.libuv/2.0.2", "hashPath": "microsoft.aspnetcore.server.kestrel.transport.libuv.2.0.2.nupkg.sha512"}, "Microsoft.AspNetCore.WebUtilities/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-dvn80+p1AIQKOfJ+VrOhVMUktWRvJs7Zb+UapZGBNSyrCzTsYiXbb9C7Mzw+nGj5UevnLNFcWWc7BUlLMD2qpw==", "path": "microsoft.aspnetcore.webutilities/2.0.2", "hashPath": "microsoft.aspnetcore.webutilities.2.0.2.nupkg.sha512"}, "Microsoft.Bcl/1.1.9": {"type": "package", "serviceable": true, "sha512": "sha512-USQ55innJy8K+tAXvVa1O8dUTp2s7pmJ5cJj6Tl02HtGc2xBDj0P2QH5620HbGXIWKWYhydoeQF8Rm/JRDBGhw==", "path": "microsoft.bcl/1.1.9", "hashPath": "microsoft.bcl.1.1.9.nupkg.sha512"}, "Microsoft.Bcl.Async/1.0.168": {"type": "package", "serviceable": true, "sha512": "sha512-tUNC02eBwDKpGre0BcNIvblLv1q0Q3DnS/vtkRHj2FE1sXwt386HAudztyl5C0U88hllrqHDvtlz8bK0Y8cHDA==", "path": "microsoft.bcl.async/1.0.168", "hashPath": "microsoft.bcl.async.1.0.168.nupkg.sha512"}, "Microsoft.Bcl.AsyncInterfaces/1.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-K63Y4hORbBcKLWH5wnKgzyn7TOfYzevIEwIedQHBIkmkEBA9SCqgvom+XTuE+fAFGvINGkhFItaZ2dvMGdT5iw==", "path": "microsoft.bcl.asyncinterfaces/1.0.0", "hashPath": "microsoft.bcl.asyncinterfaces.1.0.0.nupkg.sha512"}, "Microsoft.Bcl.Build/1.0.14": {"type": "package", "serviceable": true, "sha512": "sha512-cDLKSvNvRa519hplsbSoYqO69TjdDIhfjtKUM0g20/nVROoWsGav9KCI9HtnGjLmdV1+TcUUDhbotcllibjPEA==", "path": "microsoft.bcl.build/1.0.14", "hashPath": "microsoft.bcl.build.1.0.14.nupkg.sha512"}, "Microsoft.CSharp/4.7.0": {"type": "package", "serviceable": true, "sha512": "sha512-pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "path": "microsoft.csharp/4.7.0", "hashPath": "microsoft.csharp.4.7.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-niMGaRvTPPGAhuhKxFdmdfeW5Vo6rvhNXy2vnRotC7ToR/1ST6iDqZCIvW4L/Pt2+ISiFbvcqRQIE9MrNSMUZQ==", "path": "microsoft.entityframeworkcore/5.0.16", "hashPath": "microsoft.entityframeworkcore.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-nGESdyRnKGQ0PBUBrtVwGz3U13/C7RKcOctps2WozCGo5fHnQmcVtZ23mYc3Ri1LSMY+l4vLblBPJUzRaPaAWA==", "path": "microsoft.entityframeworkcore.abstractions/5.0.16", "hashPath": "microsoft.entityframeworkcore.abstractions.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-BiSrqLpJ6dv6xOH1xuHG/85edPfQo5DbuUZJqXNCOm3DD9BjnxaeVYnl2EUdadEUbCTTNVhXT0iKnaOpD5bv6A==", "path": "microsoft.entityframeworkcore.analyzers/5.0.16", "hashPath": "microsoft.entityframeworkcore.analyzers.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Design/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-LoOs2a4OhPPPv4AdjRMDEn4FJXhLZ5L8uiriomNYTdS7UqP7+7b32s+xDuntqfJhIFMNURaZtT1nDeVLaseb+g==", "path": "microsoft.entityframeworkcore.design/5.0.16", "hashPath": "microsoft.entityframeworkcore.design.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-B1ux0Pf1tTo7AXRldxInBWlFVm0/SVdV/7wSBxUmwy+uRO4oQCIJk69ot1DbL8YCQKjSMMB6IZkRTK95Haetow==", "path": "microsoft.entityframeworkcore.relational/5.0.16", "hashPath": "microsoft.entityframeworkcore.relational.5.0.16.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Relational.Design/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-fF0sVEUkoGeJutGIzwnYsJJ9o2hxEGJRpSaCxPq63rhSwn0hBmCwf9ET4QqYqO9Pc6+ODXenAQa095CWzuM4Kg==", "path": "microsoft.entityframeworkcore.relational.design/1.1.0", "hashPath": "microsoft.entityframeworkcore.relational.design.1.1.0.nupkg.sha512"}, "Microsoft.EntityFrameworkCore.Tools/5.0.16": {"type": "package", "serviceable": true, "sha512": "sha512-56FD9EtENIN/0SnQ7jPCURL0jgawraHhyJb2Qtf9y8PK8nOE+YfyabLsC4MGWxVgxe4c3pFon+3SWl0I1qqIFg==", "path": "microsoft.entityframeworkcore.tools/5.0.16", "hashPath": "microsoft.entityframeworkcore.tools.5.0.16.nupkg.sha512"}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "path": "microsoft.extensions.caching.abstractions/5.0.0", "hashPath": "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "path": "microsoft.extensions.caching.memory/5.0.0", "hashPath": "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-d9fFoEYRaBccu/Z2B2BZCil/lEnmoVQ8YiY1dGViERh0qWjixgR9y/M7EGaoTrAunnmvAmfwxuij/gCq6WvL1w==", "path": "microsoft.extensions.configuration/2.0.1", "hashPath": "microsoft.extensions.configuration.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "hashPath": "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Binder/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-5I1aC5g3+zb10nbNfTEz0YVFuKgvNU4jul0iDX10Q1nVyZoj33TsoNQwcJqBzJBxwjDSSGhejhgsQduREhFm6g==", "path": "microsoft.extensions.configuration.binder/2.0.1", "hashPath": "microsoft.extensions.configuration.binder.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.CommandLine/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xbA72loiTC3MK89cJZBEEbl4jWi8ugUJjd6Ak4jJN7JXerVURpWhSJ7engn+gZKYwvzdbt0vkr+/u015Pe4gqA==", "path": "microsoft.extensions.configuration.commandline/2.0.1", "hashPath": "microsoft.extensions.configuration.commandline.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.EnvironmentVariables/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Ex3C6fEpePj3pekjjDTbSY/+IR371KDv+BFp6Wev/q0uPBmFN5dXlvy2M37fYmfca/VIb3rkOIqHpheWG3Iezg==", "path": "microsoft.extensions.configuration.environmentvariables/2.0.1", "hashPath": "microsoft.extensions.configuration.environmentvariables.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.FileExtensions/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-ig55mY9fpfvVbQLuiT1ETjpYuI33RiSfhdon0nfl3m9cRSCJrrq2X7MXus2ihh2eW3ev+jPBHWNOFjN0YRN3cg==", "path": "microsoft.extensions.configuration.fileextensions/2.0.1", "hashPath": "microsoft.extensions.configuration.fileextensions.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.Json/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-RIh+RKEFkLDNeOhwPasPslqVDr72NVedR0rNKwxWnCZftAlSa4jmKg7nCacB4pU7rK2TMgl85ZaHZmrxC7Rcew==", "path": "microsoft.extensions.configuration.json/2.0.1", "hashPath": "microsoft.extensions.configuration.json.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Configuration.UserSecrets/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-MZMMOV7cMHnT7bAfcF2NmLywHXcw3krNtrPmjTO/CoimDl4dJbd7YhM29S5EFkr10nwMslH3VQtMccSVKGAcyw==", "path": "microsoft.extensions.configuration.usersecrets/2.0.1", "hashPath": "microsoft.extensions.configuration.usersecrets.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "path": "microsoft.extensions.dependencyinjection/5.0.2", "hashPath": "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512"}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "hashPath": "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Abstractions/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Gzc5yXvwIrKpdti0Ev4jC0inVrGZpI86eLZorMVRqAPXowR8JDRbcHjhmID2EqA4rdhL/IsfD42+4upKpHULDw==", "path": "microsoft.extensions.fileproviders.abstractions/2.0.1", "hashPath": "microsoft.extensions.fileproviders.abstractions.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileProviders.Physical/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-h+6bcXYlGldl7BUhnQKFxL2sMfeg9Gr/AuVexYOCYWmzDsc4iyUoy3NL7i2vkG209wd0ZXf+pZzRDwGPFhmlSw==", "path": "microsoft.extensions.fileproviders.physical/2.0.1", "hashPath": "microsoft.extensions.fileproviders.physical.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.FileSystemGlobbing/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-q7KsG2kjwo2Ps0WdV7MFh64cQS0UHikV8qv4HQrUfWQyxim5vNmLzAbuduarS9QWbhRHTtUanx+ohyAQdumdnw==", "path": "microsoft.extensions.filesystemglobbing/2.0.1", "hashPath": "microsoft.extensions.filesystemglobbing.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Hosting.Abstractions/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-gs+TNXCW05ujojZlQj2i9Fj00IAhXrgLZtgGM0XxoSoffgCGfGh7jX4kB/dnaot3xVdw84L1nE98bwQN7+kK8A==", "path": "microsoft.extensions.hosting.abstractions/2.0.2", "hashPath": "microsoft.extensions.hosting.abstractions.2.0.2.nupkg.sha512"}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "path": "microsoft.extensions.logging/5.0.0", "hashPath": "microsoft.extensions.logging.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "path": "microsoft.extensions.logging.abstractions/5.0.0", "hashPath": "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Logging.Configuration/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-xIA/im+xMO80xHvfFCa3IQ6/L20pHl7MjyEZjKQKHRNsZgJIk4e8dfdHGeNaXChuTUycQ0EBdyN4kXUFqbAk3A==", "path": "microsoft.extensions.logging.configuration/2.0.1", "hashPath": "microsoft.extensions.logging.configuration.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Console/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-lbAWSy/Iwj584V6TAcKK8DU37IDOO7l+fMfktTsQWs14a4NXF/S0DjdbeJ5QoGR3aQiIlKJvNoCPoKLO9XeBMQ==", "path": "microsoft.extensions.logging.console/2.0.1", "hashPath": "microsoft.extensions.logging.console.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Logging.Debug/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Cvsb3YWmuy7R/CRCAjoTVHDG3GDDVROfp3UWjo7CnxGX2Czc89AUPjxH5JFOd7xOplj12BX/KgU5m1KO3VOJIg==", "path": "microsoft.extensions.logging.debug/2.0.1", "hashPath": "microsoft.extensions.logging.debug.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.ObjectPool/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-drOmgNZCJiNEqFM/TvyqwtogS8wqoWGQCW5KB/CVGKL6VXHw8OOMdaHyspp8HPstP9UDnrnuq+8eaCaAcQg6tA==", "path": "microsoft.extensions.objectpool/2.0.0", "hashPath": "microsoft.extensions.objectpool.2.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "path": "microsoft.extensions.options/5.0.0", "hashPath": "microsoft.extensions.options.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.Options.ConfigurationExtensions/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-O1/MZSjWHdo4NNBD83ibRi83kKkbqbe+XTuoQtyk9NpfzYO6GoeEA+5ClEMJ56BO9DCNZb5SCBCPdlt2MdLFfw==", "path": "microsoft.extensions.options.configurationextensions/2.0.1", "hashPath": "microsoft.extensions.options.configurationextensions.2.0.1.nupkg.sha512"}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "path": "microsoft.extensions.primitives/5.0.0", "hashPath": "microsoft.extensions.primitives.5.0.0.nupkg.sha512"}, "Microsoft.Extensions.WebEncoders/2.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uRVexwgmsT3kfLKYb1mVOh96DIfo13Jp0rXvVZjFLEL29TV9K3GUeM/qTgm5P+hncWCMU6KOmx/QA+954pBMtw==", "path": "microsoft.extensions.webencoders/2.0.1", "hashPath": "microsoft.extensions.webencoders.2.0.1.nupkg.sha512"}, "Microsoft.IdentityModel.Logging/1.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-j7t22EsDOuo0IXqAbp6ijdB1GuaY8cu3YoPNZpymOhUMTVC+wRTV0IHqxL31HacCnJHU/igsqe70fDKZgZu3oA==", "path": "microsoft.identitymodel.logging/1.1.4", "hashPath": "microsoft.identitymodel.logging.1.1.4.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-9aefRN9sL8XZo90Aix88IHHpAvfBl6UOiYpcKHiXbCYE2nB+zA3B8dZdNMOUH4pqXdnpYrHRDQZ2k7A4/CUgTQ==", "path": "microsoft.identitymodel.protocols/2.1.4", "hashPath": "microsoft.identitymodel.protocols.2.1.4.nupkg.sha512"}, "Microsoft.IdentityModel.Protocols.OpenIdConnect/2.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-LF8JcG9BqGRwVjhu/IebuZQer6TJGDv2uxNnmg2Zkzh/d+MIC1ShsC1U3U7pVaw03SKyXmCgYm+JG0WM0mcOUw==", "path": "microsoft.identitymodel.protocols.openidconnect/2.1.4", "hashPath": "microsoft.identitymodel.protocols.openidconnect.2.1.4.nupkg.sha512"}, "Microsoft.IdentityModel.Tokens/5.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-SsJbZVPvjSlKFDAQmR2wpL6ZD/vCFlIsf0jxRlBJwyzKXZy3Wi/Xo+fE2MzAerLsJgG1UCdtplRwqDyq1euayw==", "path": "microsoft.identitymodel.tokens/5.1.4", "hashPath": "microsoft.identitymodel.tokens.5.1.4.nupkg.sha512"}, "Microsoft.Net.Http/2.2.28": {"type": "package", "serviceable": true, "sha512": "sha512-hPL9k+rPIpAj84RZSq3WtJkOD9uMStBovAIAz/0OQ+9hQIwQ2XqQlAhMqI4kw0XDxweJySIqNTMlkhRRuthcPQ==", "path": "microsoft.net.http/2.2.28", "hashPath": "microsoft.net.http.2.2.28.nupkg.sha512"}, "Microsoft.Net.Http.Headers/2.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-hNhJU+Sd7Ws/yrBnakUWKWMyGiDUJE5lTkJfWe5xPL8YGTiL6Es07H9CcTyaYYwVlgW06uDVN0YhhH+t4EjdCw==", "path": "microsoft.net.http.headers/2.0.2", "hashPath": "microsoft.net.http.headers.2.0.2.nupkg.sha512"}, "Microsoft.NETCore.App/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-GOxlvyc8hFrnhDjYlm25JJ7PwoyeoOpZzcg6ZgF8n8l6VxezNupRkkTeA2ek1WsspN0CdAoA8e7iDVNU84/F+Q==", "path": "microsoft.netcore.app/2.2.8", "hashPath": "microsoft.netcore.app.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-Lh1F6z41levvtfC3KuuiQe9ppWKRP1oIB42vP1QNQE4uumo95h+LpjPDeysX1DlTjCzG0BVGSUEpCW5fHkni7w==", "path": "microsoft.netcore.dotnetapphost/2.2.8", "hashPath": "microsoft.netcore.dotnetapphost.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-rOHr0Dk87vaiq9d1hMpXETB4IKq1jIiPQlVKNUjRGilK/cjOcadhsk+1MsrJ/GnM3eovhy8zW2PGkN8pYEolnw==", "path": "microsoft.netcore.dotnethostpolicy/2.2.8", "hashPath": "microsoft.netcore.dotnethostpolicy.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"type": "package", "serviceable": true, "sha512": "sha512-culLr+x2GvUkXVGi4ULZ7jmWJEhuAMyS7iTWBlkWnqbKtYJ36ZlgHbw/6qTm82790gJemEFeo9RehDwfRXfJzA==", "path": "microsoft.netcore.dotnethostresolver/2.2.8", "hashPath": "microsoft.netcore.dotnethostresolver.2.2.8.nupkg.sha512"}, "Microsoft.NETCore.Jit/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-Ok2vWofa6X8WD9vc4pfLHwvJz1/B6t3gOAoZcjrjrQf7lQOlNIuZIZtLn3wnWX28DuQGpPJkRlBxFj7Z5txNqw==", "path": "microsoft.netcore.jit/1.0.2", "hashPath": "microsoft.netcore.jit.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Platforms/2.2.4": {"type": "package", "serviceable": true, "sha512": "sha512-ZeCe9PRhMpKzVWrNgTvWpLjJigppErzN663lJOqAzcx0xjXpcAMpIImFI46IE1gze18VWw6bbfo7JDkcaRWuOg==", "path": "microsoft.netcore.platforms/2.2.4", "hashPath": "microsoft.netcore.platforms.2.2.4.nupkg.sha512"}, "Microsoft.NETCore.Portable.Compatibility/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-Vd+lvLcGwvkedxtKn0U8s9uR4p0Lm+0U2QvDsLaw7g4S1W4KfPDbaW+ROhhLCSOx/gMYC72/b+z+o4fqS/oxVg==", "path": "microsoft.netcore.portable.compatibility/1.0.1", "hashPath": "microsoft.netcore.portable.compatibility.1.0.1.nupkg.sha512"}, "Microsoft.NETCore.Runtime.CoreCLR/1.0.2": {"type": "package", "serviceable": true, "sha512": "sha512-A0x1xtTjYJWZr2DRzgfCOXgB0JkQg8twnmtTJ79wFje+IihlLbXtx6Z2AxyVokBM5ruwTedR6YdCmHk39QJdtQ==", "path": "microsoft.netcore.runtime.coreclr/1.0.2", "hashPath": "microsoft.netcore.runtime.coreclr.1.0.2.nupkg.sha512"}, "Microsoft.NETCore.Targets/2.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-odP/tJj1z6GylFpNo7pMtbd/xQgTC3Ex2If63dRTL38bBNMwsBnJ+RceUIyHdRBC0oik/3NehYT+oECwBhIM3Q==", "path": "microsoft.netcore.targets/2.0.0", "hashPath": "microsoft.netcore.targets.2.0.0.nupkg.sha512"}, "Microsoft.NETCore.Windows.ApiSets/1.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-SaToCvvsGMxTgtLv/BrFQ5IFMPRE1zpWbnqbpwykJa8W5XiX82CXI6K2o7yf5xS7EP6t/JzFLV0SIDuWpvBZVw==", "path": "microsoft.netcore.windows.apisets/1.0.1", "hashPath": "microsoft.netcore.windows.apisets.1.0.1.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "Microsoft.Win32.Registry/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-dA36TlNVn/XfrZtmf0fiI/z1nd3Wfp2QVzTdj26pqgP9LFWq0i1hYEUAW50xUjGFYn1+/cP3KGuxT2Yn1OUNBQ==", "path": "microsoft.win32.registry/4.4.0", "hashPath": "microsoft.win32.registry.4.4.0.nupkg.sha512"}, "NETStandard.Library/2.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "path": "netstandard.library/2.0.3", "hashPath": "netstandard.library.2.0.3.nupkg.sha512"}, "Newtonsoft.Json/12.0.3": {"type": "package", "serviceable": true, "sha512": "sha512-6mgjfnRB4jKMlzHSl+VD+oUc1IebOZabkbyWj2RiTgWwYPPuaK1H97G1sHqGwPlS5npiF5Q0OrxN1wni2n5QWg==", "path": "newtonsoft.json/12.0.3", "hashPath": "newtonsoft.json.12.0.3.nupkg.sha512"}, "Npgsql/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-3TB9le3lfu5Hc+LSHqMCVLcA+qUPg1enyM4+u0pMUBmNNGwc0sVPrnfnys2TVZIdkF8Aww/AZlnJHDsnEGqD0g==", "path": "npgsql/5.0.10", "hashPath": "npgsql.5.0.10.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL/5.0.10": {"type": "package", "serviceable": true, "sha512": "sha512-3K+YNZFdwMcTxY8aW9m6BHyzPce/WFdDKPESP4uCXk6xNS6Ujq6zhi7+KXkzb7iNB0OYretEuF3ezqkk4ZnJMg==", "path": "npgsql.entityframeworkcore.postgresql/5.0.10", "hashPath": "npgsql.entityframeworkcore.postgresql.5.0.10.nupkg.sha512"}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design/1.1.0": {"type": "package", "serviceable": true, "sha512": "sha512-+taA6t8sRI0JTbyCZH0GobEtJYvU4aAGBf5/2/CutWHiYpO2nLfJ8SQSyWYMKakrwn1/NDM9VV85mGEAzBZ7LQ==", "path": "npgsql.entityframeworkcore.postgresql.design/1.1.0", "hashPath": "npgsql.entityframeworkcore.postgresql.design.1.1.0.nupkg.sha512"}, "runtime.debian.8-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-HdSSp5MnJSsg08KMfZThpuLPJpPwE5hBXvHwoKWosyHHfe8Mh5WKT0ylEOf6yNzX6Ngjxe4Whkafh5q7Ymac4Q==", "path": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.debian.8-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.23-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-+yH1a49wJMy8Zt4yx5RhJrxO/DBDByAiCzNwiETI+1S4mPdCu0OY4djdciC7Vssk0l22wQaDLrXxXkp+3+7bVA==", "path": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.23-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.fedora.24-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c3YNH1GQJbfIPJeCnr4avseugSqPrxwIqzthYyZDN6EuOyNOzq+y2KSUfRcXauya1sF4foESTgwM5e1A8arAKw==", "path": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.fedora.24-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.native.System/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-c/qWt2LieNZIj1jGnVNsE2Kl23Ya2aSTBuXMD6V7k9KWr6l16Tqdwq+hJScEpWER9753NWC8h96PaVNY5Ld7Jw==", "path": "runtime.native.system/4.3.0", "hashPath": "runtime.native.system.4.3.0.nupkg.sha512"}, "runtime.native.System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZVuZJqnnegJhd2k/PtAbbIcZ3aZeITq3sj06oKfMBSfphW3HDmk/t4ObvbOk/JA/swGR0LNqMksAh/f7gpTROg==", "path": "runtime.native.system.net.http/4.3.0", "hashPath": "runtime.native.system.net.http.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-DloMk88juo0OuOWr56QG7MNchmafTLYWvABy36izkrLI5VledI0rq28KGs1i9wbpeT9NPQrx/wTf8U2vazqQ3Q==", "path": "runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-NS1U+700m4KFRHR5o4vo9DSlTmlCKu/u7dtE5sUHVIPB+xpXxYQvgBgA6wEIeCz6Yfn0Z52/72WYsToCEPJnrw==", "path": "runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.13.2-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-b3pthNgxxFcD+Pc0WSEoC0+md3MyhRS6aCEeenvNE3Fdw1HyJ18ZhRFVJJzIeR/O/jpxPboB805Ho0T3Ul7w8A==", "path": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.13.2-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.opensuse.42.1-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KeLz4HClKf+nFS7p/6Fi/CqyLXh81FpiGzcmuS8DGi9lUqSnZ6Es23/gv2O+1XVGfrbNmviF7CckBpavkBoIFQ==", "path": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.opensuse.42.1-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.Apple/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kVXCuMTrTlxq4XOOMAysuNwsXWpYeboGddNGpIgNSZmv1b6r/s/DPk0fYMB7Q5Qo4bY68o48jt4T4y5BVecbCQ==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.apple.4.3.0.nupkg.sha512"}, "runtime.osx.10.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X7IdhILzr4ROXd8mI1BUCQMSHSQwelUlBjF1JyTKCjXaOGn2fB4EKBxQbCK2VjO3WaWIdlXZL3W6TiIVnrhX4g==", "path": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.osx.10.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.rhel.7-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-nyFNiCk/r+VOiIqreLix8yN+q3Wga9+SE8BCgkf+2BwEKiNx6DyvFjCgkfV743/grxv8jHJ8gUK4XEQw7yzRYg==", "path": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.rhel.7-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.14.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ytoewC6wGorL7KoCAvRfsgoJPJbNq+64k2SqW6JcOAebWsFUvCCYgfzQMrnpvPiEl4OrblUlhF2ji+Q1+SVLrQ==", "path": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.14.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.04-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I8bKw2I8k58Wx7fMKQJn2R8lamboCAiHfHeV/pS65ScKWMMI0+wJkLYlEKvgW1D/XvSl/221clBoR2q9QNNM7A==", "path": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.04-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "runtime.ubuntu.16.10-x64.runtime.native.System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VB5cn/7OzUfzdnC8tqAIMQciVLiq2epm2NrAm1E9OjNRyG4lVhfR61SMcLizejzQP8R8Uf/0l5qOIbUEi+RdEg==", "path": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl/4.3.0", "hashPath": "runtime.ubuntu.16.10-x64.runtime.native.system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-JFk0+HHUPdjYuPhkpGBMLi2JtnEuWkE2pp0yXQp64DmeMe+Fb0hZyVNq/ENJ2vQNso7Zg+C758WmR/xyAl36bA==", "path": "swashbuckle.aspnetcore/6.3.1", "hashPath": "swashbuckle.aspnetcore.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-idAFh4xhyJHYHfdLVOOn+BmscBul1OQbWsnL6YPJE8tO/0y6S79hDCvs6OY5VI093/9+1pYY3j31Zet9yaDZjA==", "path": "swashbuckle.aspnetcore.swagger/6.3.1", "hashPath": "swashbuckle.aspnetcore.swagger.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-+uoBV4h/6NhCPLoTofSmuOnZ+usu4PW1jP6l4OHwPyu2frbYXGNpJsHs5uUXXn929OiVQkT8wo3Lj/o+P99Ejg==", "path": "swashbuckle.aspnetcore.swaggergen/6.3.1", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.3.1.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"type": "package", "serviceable": true, "sha512": "sha512-JLm9hN67jh7RHsX3H30+tb432Li8xm/qV5lRyMMkyHYMfWitIuKAAdrpo2ILcHOIeH7CLMuOO2hp/iLBmE+Bkw==", "path": "swashbuckle.aspnetcore.swaggerui/6.3.1", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.3.1.nupkg.sha512"}, "System.Buffers/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-AwarXzzoDwX6BgrhjoJsk6tUezZEozOT5Y9QKF94Gl4JK91I4PIIBkBco9068Y9/Dra8Dkbie99kXB8+1BaYKw==", "path": "system.buffers/4.4.0", "hashPath": "system.buffers.4.4.0.nupkg.sha512"}, "System.Collections/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "path": "system.collections/4.3.0", "hashPath": "system.collections.4.3.0.nupkg.sha512"}, "System.Collections.Concurrent/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ztl69Xp0Y/UXCL+3v3tEU+lIy+bvjKNUmopn1wep/a291pVPK7dxBd6T7WnlQqRog+d1a/hSsgRsmFnIBKTPLQ==", "path": "system.collections.concurrent/4.3.0", "hashPath": "system.collections.concurrent.4.3.0.nupkg.sha512"}, "System.Collections.Immutable/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "path": "system.collections.immutable/5.0.0", "hashPath": "system.collections.immutable.5.0.0.nupkg.sha512"}, "System.Collections.NonGeneric/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-prtjIEMhGUnQq6RnPEYLpFt8AtLbp9yq2zxOSrY7KJJZrw25Fi97IzBqY7iqssbM61Ek5b8f3MG/sG1N2sN5KA==", "path": "system.collections.nongeneric/4.3.0", "hashPath": "system.collections.nongeneric.4.3.0.nupkg.sha512"}, "System.Collections.Specialized/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-Epx8PoVZR0iuOnJJDzp7pWvdfMMOAvpUo95pC4ScH2mJuXkKA2Y4aR3cG9qt2klHgSons1WFh4kcGW7cSXvrxg==", "path": "system.collections.specialized/4.3.0", "hashPath": "system.collections.specialized.4.3.0.nupkg.sha512"}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "path": "system.componentmodel.annotations/5.0.0", "hashPath": "system.componentmodel.annotations.5.0.0.nupkg.sha512"}, "System.Diagnostics.Contracts/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "path": "system.diagnostics.contracts/4.3.0", "hashPath": "system.diagnostics.contracts.4.3.0.nupkg.sha512"}, "System.Diagnostics.Debug/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "path": "system.diagnostics.debug/4.3.0", "hashPath": "system.diagnostics.debug.4.3.0.nupkg.sha512"}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "serviceable": true, "sha512": "sha512-uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "path": "system.diagnostics.diagnosticsource/5.0.1", "hashPath": "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512"}, "System.Diagnostics.Tools/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-UUvkJfSYJMM6x527dJg2VyWPSRqIVB0Z7dbjHst1zmwTXz5CcXSYJFWRpuigfbO1Lf7yfZiIaEUesfnl/g5EyA==", "path": "system.diagnostics.tools/4.3.0", "hashPath": "system.diagnostics.tools.4.3.0.nupkg.sha512"}, "System.Diagnostics.Tracing/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rswfv0f/Cqkh78rA5S8eN8Neocz234+emGCtTF3lxPY96F+mmmUen6tbn0glN6PMvlKQb9bPAY5e9u7fgPTkKw==", "path": "system.diagnostics.tracing/4.3.0", "hashPath": "system.diagnostics.tracing.4.3.0.nupkg.sha512"}, "System.Dynamic.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-SNVi1E/vfWUAs/WYKhE9+qlS6KqK0YVhnlT0HQtr8pMIA8YX3lwy3uPMownDwdYISBdmAF/2holEIldVp85Wag==", "path": "system.dynamic.runtime/4.3.0", "hashPath": "system.dynamic.runtime.4.3.0.nupkg.sha512"}, "System.Globalization/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "path": "system.globalization/4.3.0", "hashPath": "system.globalization.4.3.0.nupkg.sha512"}, "System.Globalization.Calendars/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GUlBtdOWT4LTV3I+9/PJW+56AnnChTaOqqTLFtdmype/L500M2LIyXgmtd9X2P2VOkmJd5c67H5SaC2QcL1bFA==", "path": "system.globalization.calendars/4.3.0", "hashPath": "system.globalization.calendars.4.3.0.nupkg.sha512"}, "System.Globalization.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-FhKmdR6MPG+pxow6wGtNAWdZh7noIOpdD5TwQ3CprzgIE1bBBoim0vbR1+AWsWjQmU7zXHgQo4TWSP6lCeiWcQ==", "path": "system.globalization.extensions/4.3.0", "hashPath": "system.globalization.extensions.4.3.0.nupkg.sha512"}, "System.IdentityModel.Tokens.Jwt/5.1.4": {"type": "package", "serviceable": true, "sha512": "sha512-hLUU1N99aL9uyxiTraBnCKlpUKsbP/+5ygD7cswspH9/+M7fAAL0hRzt2aA4w7VEQlSSiu8j+xWFk3NRcqhfQQ==", "path": "system.identitymodel.tokens.jwt/5.1.4", "hashPath": "system.identitymodel.tokens.jwt.5.1.4.nupkg.sha512"}, "System.IO/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "path": "system.io/4.3.0", "hashPath": "system.io.4.3.0.nupkg.sha512"}, "System.IO.FileSystem/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-3wEMARTnuio+ulnvi+hkRNROYwa1kylvYahhcLk4HSoVdl+xxTFVeVlYOfLwrDPImGls0mDqbMhrza8qnWPTdA==", "path": "system.io.filesystem/4.3.0", "hashPath": "system.io.filesystem.4.3.0.nupkg.sha512"}, "System.IO.FileSystem.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-6QOb2XFLch7bEc4lIcJH49nJN2HV+OC3fHDgsLVsBVBk3Y4hFAnOBGzJ2lUu7CyDDFo9IBWkSsnbkT6IBwwiMw==", "path": "system.io.filesystem.primitives/4.3.0", "hashPath": "system.io.filesystem.primitives.4.3.0.nupkg.sha512"}, "System.Linq/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "path": "system.linq/4.3.0", "hashPath": "system.linq.4.3.0.nupkg.sha512"}, "System.Linq.Expressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "path": "system.linq.expressions/4.3.0", "hashPath": "system.linq.expressions.4.3.0.nupkg.sha512"}, "System.Net.Http/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-sYg+FtILtRQuYWSIAuNOELwVuVsxVyJGWQyOnlAzhV4xvhyFnON1bAzYYC+jjRW8JREM45R0R5Dgi8MTC5sEwA==", "path": "system.net.http/4.3.0", "hashPath": "system.net.http.4.3.0.nupkg.sha512"}, "System.Net.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-qOu+hDwFwoZPbzPvwut2qATe3ygjeQBDQj91xlsaqGFQUI5i4ZnZb8yyQuLGpDGivEPIt8EJkd1BVzVoP31FXA==", "path": "system.net.primitives/4.3.0", "hashPath": "system.net.primitives.4.3.0.nupkg.sha512"}, "System.Numerics.Vectors/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-UiLzLW+Lw6HLed1Hcg+8jSRttrbuXv7DANVj0DkL9g6EnnzbL75EB7EWsw5uRbhxd/4YdG8li5XizGWepmG3PQ==", "path": "system.numerics.vectors/4.4.0", "hashPath": "system.numerics.vectors.4.4.0.nupkg.sha512"}, "System.ObjectModel/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "path": "system.objectmodel/4.3.0", "hashPath": "system.objectmodel.4.3.0.nupkg.sha512"}, "System.Reflection/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "path": "system.reflection/4.3.0", "hashPath": "system.reflection.4.3.0.nupkg.sha512"}, "System.Reflection.Emit/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-228FG0jLcIwTVJyz8CLFKueVqQK36ANazUManGaJHkO0icjiIypKW7YLWLIWahyIkdh5M7mV2dJepllLyA1SKg==", "path": "system.reflection.emit/4.3.0", "hashPath": "system.reflection.emit.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.ILGeneration/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "path": "system.reflection.emit.ilgeneration/4.3.0", "hashPath": "system.reflection.emit.ilgeneration.4.3.0.nupkg.sha512"}, "System.Reflection.Emit.Lightweight/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-oadVHGSMsTmZsAF864QYN1t1QzZjIcuKU3l2S9cZOwDdDueNTrqq1yRj7koFfIGEnKpt6NjpL3rOzRhs4ryOgA==", "path": "system.reflection.emit.lightweight/4.3.0", "hashPath": "system.reflection.emit.lightweight.4.3.0.nupkg.sha512"}, "System.Reflection.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "path": "system.reflection.extensions/4.3.0", "hashPath": "system.reflection.extensions.4.3.0.nupkg.sha512"}, "System.Reflection.Metadata/1.5.0": {"type": "package", "serviceable": true, "sha512": "sha512-423hF/x1/1/aBT6hjgrp8RH2zdKOd1iTujlHisSesTW/cgv1ixUitfk23ZknVzItMm6jnwp9CBwI2P3r9jpitw==", "path": "system.reflection.metadata/1.5.0", "hashPath": "system.reflection.metadata.1.5.0.nupkg.sha512"}, "System.Reflection.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "path": "system.reflection.primitives/4.3.0", "hashPath": "system.reflection.primitives.4.3.0.nupkg.sha512"}, "System.Reflection.TypeExtensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "path": "system.reflection.typeextensions/4.3.0", "hashPath": "system.reflection.typeextensions.4.3.0.nupkg.sha512"}, "System.Resources.ResourceManager/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "path": "system.resources.resourcemanager/4.3.0", "hashPath": "system.resources.resourcemanager.4.3.0.nupkg.sha512"}, "System.Runtime/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "path": "system.runtime/4.3.0", "hashPath": "system.runtime.4.3.0.nupkg.sha512"}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {"type": "package", "serviceable": true, "sha512": "sha512-HxozeSlipUK7dAroTYwIcGwKDeOVpQnJlpVaOkBz7CM4TsE5b/tKlQBZecTjh6FzcSbxndYaxxpsBMz+wMJeyw==", "path": "system.runtime.compilerservices.unsafe/4.6.0", "hashPath": "system.runtime.compilerservices.unsafe.4.6.0.nupkg.sha512"}, "System.Runtime.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "path": "system.runtime.extensions/4.3.0", "hashPath": "system.runtime.extensions.4.3.0.nupkg.sha512"}, "System.Runtime.Handles/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-OKiSUN7DmTWeYb3l51A7EYaeNMnvxwE249YtZz7yooT4gOZhmTjIn48KgSsw2k2lYdLgTKNJw/ZIfSElwDRVgg==", "path": "system.runtime.handles/4.3.0", "hashPath": "system.runtime.handles.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-uv1ynXqiMK8mp1GM3jDqPCFN66eJ5w5XNomaK2XD+TuCroNTLFGeZ+WCmBMcBDyTFKou3P6cR6J/QsaqDp7fGQ==", "path": "system.runtime.interopservices/4.3.0", "hashPath": "system.runtime.interopservices.4.3.0.nupkg.sha512"}, "System.Runtime.InteropServices.RuntimeInformation/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-cbz4YJMqRDR7oLeMRbdYv7mYzc++17lNhScCX0goO2XpGWdvAt60CGN+FHdePUEHCe/Jy9jUlvNAiNdM+7jsOw==", "path": "system.runtime.interopservices.runtimeinformation/4.3.0", "hashPath": "system.runtime.interopservices.runtimeinformation.4.3.0.nupkg.sha512"}, "System.Runtime.Numerics/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-yMH+MfdzHjy17l2KESnPiF2dwq7T+xLnSJar7slyimAkUh/gTrS9/UQOtv7xarskJ2/XDSNvfLGOBQPjL7PaHQ==", "path": "system.runtime.numerics/4.3.0", "hashPath": "system.runtime.numerics.4.3.0.nupkg.sha512"}, "System.Security.AccessControl/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-2NRFPX/V81ucKQmqNgGBZrKGH/5ejsvivSGMRum0SMgPnJxwhuNkzVS1+7gC3R2X0f57CtwrPrXPPSe6nOp82g==", "path": "system.security.accesscontrol/4.4.0", "hashPath": "system.security.accesscontrol.4.4.0.nupkg.sha512"}, "System.Security.Claims/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-P/+BR/2lnc4PNDHt/TPBAWHVMLMRHsyYZbU1NphW4HIWzCggz8mJbTQQ3MKljFE7LS3WagmVFuBgoLcFzYXlkA==", "path": "system.security.claims/4.3.0", "hashPath": "system.security.claims.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Algorithms/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-W1kd2Y8mYSCgc3ULTAZ0hOP2dSdG5YauTb1089T0/kRcN2MpSAW1izOFROrJgxSlMn3ArsgHXagigyi+ibhevg==", "path": "system.security.cryptography.algorithms/4.3.0", "hashPath": "system.security.cryptography.algorithms.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Cng/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-03idZOqFlsKRL4W+LuCpJ6dBYDUWReug6lZjBa3uJWnk5sPCUXckocevTaUA8iT/MFSrY/2HXkOt753xQ/cf8g==", "path": "system.security.cryptography.cng/4.3.0", "hashPath": "system.security.cryptography.cng.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Csp/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-X4s/FCkEUnRGnwR3aSfVIkldBmtURMhmexALNTwpjklzxWU7yjMk7GHLKOZTNkgnWnE0q7+BCf9N2LVRWxewaA==", "path": "system.security.cryptography.csp/4.3.0", "hashPath": "system.security.cryptography.csp.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-1DEWjZZly9ae9C79vFwqaO5kaOlI5q+3/55ohmq/7dpDyDfc8lYe7YVxJUZ5MF/NtbkRjwFRo14yM4OEo9EmDw==", "path": "system.security.cryptography.encoding/4.3.0", "hashPath": "system.security.cryptography.encoding.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.OpenSsl/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-h4CEgOgv5PKVF/HwaHzJRiVboL2THYCou97zpmhjghx5frc7fIvlkY1jL+lnIQyChrJDMNEXS6r7byGif8Cy4w==", "path": "system.security.cryptography.openssl/4.3.0", "hashPath": "system.security.cryptography.openssl.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Primitives/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-7bDIyVFNL/xKeFHjhobUAQqSpJq9YTOpbEs6mR233Et01STBMXNAc/V+BM6dwYGc95gVh/Zf+iVXWzj3mE8DWg==", "path": "system.security.cryptography.primitives/4.3.0", "hashPath": "system.security.cryptography.primitives.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.X509Certificates/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-t2Tmu6Y2NtJ2um0RtcuhP7ZdNNxXEgUm2JeoA/0NvlMjAhKCnM1NX07TDl3244mVp3QU6LPEhT3HTtH1uF7IYw==", "path": "system.security.cryptography.x509certificates/4.3.0", "hashPath": "system.security.cryptography.x509certificates.4.3.0.nupkg.sha512"}, "System.Security.Cryptography.Xml/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-1Xubvo4i+K+DO6YzVh6vBKmCl5xx/cAoiJEze6VQ+XwVQU25KQC9pPrmniz2EbbJnmoQ5Rm2FFjHsfQAi0Rs+Q==", "path": "system.security.cryptography.xml/4.4.0", "hashPath": "system.security.cryptography.xml.4.4.0.nupkg.sha512"}, "System.Security.Principal/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-I1tkfQlAoMM2URscUtpcRo/hX0jinXx6a/KUtEQoz3owaYwl3qwsO8cbzYVVnjxrzxjHo3nJC+62uolgeGIS9A==", "path": "system.security.principal/4.3.0", "hashPath": "system.security.principal.4.3.0.nupkg.sha512"}, "System.Security.Principal.Windows/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-pP+AOzt1o3jESOuLmf52YQTF7H3Ng9hTnrOESQiqsnl2IbBh1HInsAMHYtoh75iUYV0OIkHmjvveraYB6zM97w==", "path": "system.security.principal.windows/4.4.0", "hashPath": "system.security.principal.windows.4.4.0.nupkg.sha512"}, "System.Text.Encoding/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "path": "system.text.encoding/4.3.0", "hashPath": "system.text.encoding.4.3.0.nupkg.sha512"}, "System.Text.Encoding.Extensions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-YVMK0Bt/A43RmwizJoZ22ei2nmrhobgeiYwFzC4YAN+nue8RF6djXDMog0UCn+brerQoYVyaS+ghy9P/MUVcmw==", "path": "system.text.encoding.extensions/4.3.0", "hashPath": "system.text.encoding.extensions.4.3.0.nupkg.sha512"}, "System.Text.Encodings.Web/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-l/tYeikqMHX2MD2jzrHDfR9ejrpTTF7wvAEbR51AMvzip1wSJgiURbDik4iv/w7ZgytmTD/hlwpplEhF9bmFNw==", "path": "system.text.encodings.web/4.4.0", "hashPath": "system.text.encodings.web.4.4.0.nupkg.sha512"}, "System.Text.RegularExpressions/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-RpT2DA+L660cBt1FssIE9CAGpLFdFPuheB7pLpKpn6ZXNby7jDERe8Ua/Ne2xGiwLVG2JOqziiaVCGDon5sKFA==", "path": "system.text.regularexpressions/4.3.0", "hashPath": "system.text.regularexpressions.4.3.0.nupkg.sha512"}, "System.Threading/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "path": "system.threading/4.3.0", "hashPath": "system.threading.4.3.0.nupkg.sha512"}, "System.Threading.Tasks/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "path": "system.threading.tasks/4.3.0", "hashPath": "system.threading.tasks.4.3.0.nupkg.sha512"}, "System.Threading.Tasks.Extensions/4.4.0": {"type": "package", "serviceable": true, "sha512": "sha512-SPKfFGbpQsK5Srz2Kq3URgvC90yoOyBE8H1quDA2+MAJ2HAzFmV3biOgPv2Ck3mPAvdKngo3QHi2BNwUQDRVvA==", "path": "system.threading.tasks.extensions/4.4.0", "hashPath": "system.threading.tasks.extensions.4.4.0.nupkg.sha512"}, "System.Xml.ReaderWriter/4.3.0": {"type": "package", "serviceable": true, "sha512": "sha512-GrprA+Z0RUXaR4N7/eW71j1rgMnEnEVlgii49GZyAjTH7uliMnrOU3HNFBr6fEDBCJCIdlVNq9hHbaDR621XBA==", "path": "system.xml.readerwriter/4.3.0", "hashPath": "system.xml.readerwriter.4.3.0.nupkg.sha512"}}}