﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>netcoreapp3.1</TargetFramework>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="AspNetCore.Firebase.Authentication" Version="2.0.1" />
    <PackageReference Include="Firebase.Auth" Version="1.0.0" />
    <PackageReference Include="FirebaseAdmin" Version="3.2.0" />
    <PackageReference Include="FireSharp" Version="2.0.4" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="5.0.16">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="3.1.32">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.NETCore.App" Version="2.2.8" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="5.0.10" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL.Design" Version="1.1.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.3.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Swagger" Version="6.3.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.SwaggerUI" Version="6.3.1" />
  </ItemGroup>

</Project>
