"use strict";(self.webpackChunkMeteodusouffle=self.webpackChunkMeteodusouffle||[]).push([[441],{6441:(w,u,s)=>{s.r(u),s.d(u,{LoginModule:()=>T});var a=s(9808),n=s(3075),l=s(8744),o=s(5e3),p=s(2160),m=s(2290),h=s(9855),g=s(7531),v=s(5245),c=s(7322),Z=s(7446);function I(i,r){1&i&&(o.TgZ(0,"div",18),o._uU(1,"Un email valide est requis pour se connecter."),o.qZA())}function A(i,r){1&i&&(o.TgZ(0,"div",18),o._uU(1,"Un mot de passe valide est requis pour se connecter"),o.qZA())}const f=function(i){return{"is-invalid":i}},k=[{path:"",component:(()=>{class i{constructor(t,e,d,L,U){this.router=t,this.cookie=e,this.formBuilder=d,this.toastr=L,this.loginService=U,this.submitted=!1,this.hide=!0}createForm(){this.loginForm=this.formBuilder.group({emailId:["",n.kI.required],password:["",[n.kI.required,n.kI.pattern(/^(?=.*[A-Z][A-Za-z])(?=.*\d)(?=.*[$@$!*%^()])[A-Za-z\d$@$!*%^()]{6,16}$/)]],deciveInfo:"Windows",remember:[!1]})}checkRememberMe(){void 0!==this.cookie.get("remember")&&"Yes"===this.cookie.get("remember")&&this.patchForm()}patchForm(){this.loginForm.patchValue({emailId:this.cookie.get("emailId"),password:this.cookie.get("password"),remember:[!0]})}ngOnInit(){this.createForm(),this.checkRememberMe(),this.emailId=n.kI.email,n.kI.pattern("^[a-z0-9._%+-]+@[a-z0-9.-]+\\.[a-z]{2,4}$"),localStorage.clear()}get f(){return this.loginForm.controls}login(){this.submitted=!0,!this.loginForm.invalid&&this.loginService.AuthorizeUser(this.loginForm.value).subscribe(t=>{t.status?(localStorage.setItem("token",t.data.token),localStorage.setItem("refreshToken",t.data.refreshtoken),this.setCookie(),this.toastr.success(t.message),this.router.navigate(["/home"])):this.toastr.error(t.message)},t=>{this.toastr.error(t.error.message)})}setCookie(){this.loginForm.value.remember?(this.cookie.set("remember","Yes"),this.cookie.set("emailId",this.loginForm.value.emailId),this.cookie.set("password",this.loginForm.value.password)):(this.cookie.set("remember","No"),this.cookie.set("emailId",""),this.cookie.set("password",""))}onKeyPress(t){13===t.keyCode&&this.login()}login1(){this.router.navigate(["/home"])}}return i.\u0275fac=function(t){return new(t||i)(o.Y36(l.F0),o.Y36(p.N),o.Y36(n.qu),o.Y36(m._W),o.Y36(h.r))},i.\u0275cmp=o.Xpm({type:i,selectors:[["app-login"]],decls:36,vars:11,consts:[[1,"container-fluid"],[1,"row"],[1,"col-lg-8","env-background"],[1,"col-lg-4","text-center","login-section"],["src","../../../assets/images/logoimage.png","alt","logo","title","logo","width","270px","height","120px"],[1,"login-form"],[1,"inputsection",3,"formGroup"],[1,"form-group","text-left"],["for","Email Address"],["type","text","pattern","[a-zA-Z0-9.-_]{1,}@[a-zA-Z.-]{2,}[.]{1}[a-zA-Z]{2,}","matInput","","formControlName","emailId",1,"form-control",3,"ngClass"],["class","box",4,"ngIf"],[1,"form-group","text-left","position-relative"],["for","Password"],["matInput","","autocomplete","off","pattern","[a-zA-Z0-9.-_]{0,9}[.,:'!@#$%^^&*'][0-9]{0,9}[a-zA-Z0-9.-_]{0,9}","type","password","matInput","","formControlName","password",1,"form-control",3,"type","ngClass"],["password",""],["matSuffix","",1,"showingIcons",3,"click"],["formControlName","remember",1,"example-margin","checkbox"],["type","submit",1,"submit",3,"click"],[1,"box"]],template:function(t,e){1&t&&(o.TgZ(0,"section")(1,"div",0)(2,"div",1)(3,"div",2)(4,"p"),o._uU(5,"ANALYSE, PREDICT,"),o._UZ(6,"br"),o._uU(7,"ACT FOR THE ENVIRONMENT"),o.qZA()(),o.TgZ(8,"div",3)(9,"div")(10,"figure"),o._UZ(11,"img",4),o.qZA(),o.TgZ(12,"div",5)(13,"h1"),o._uU(14,"M\xe9t\xe9o du Souffle"),o.qZA(),o.TgZ(15,"p"),o._uU(16,"Admin Login"),o.qZA()(),o.TgZ(17,"form",6)(18,"div",7)(19,"label",8),o._uU(20,"Email Address"),o.qZA(),o._UZ(21,"input",9),o.YNc(22,I,2,0,"div",10),o.qZA(),o.TgZ(23,"div",11)(24,"label",12),o._uU(25,"Password"),o.qZA(),o._UZ(26,"input",13,14),o.TgZ(28,"mat-icon",15),o.NdJ("click",function(){return e.hide=!e.hide}),o._uU(29),o.qZA(),o.YNc(30,A,2,0,"div",10),o.qZA(),o.TgZ(31,"mat-checkbox",16),o._uU(32,"Se souvenir de moi?"),o.qZA()(),o.TgZ(33,"div")(34,"button",17),o.NdJ("click",function(){return e.login()}),o._uU(35,"Submit"),o.qZA()()()()()()()),2&t&&(o.xp6(17),o.Q6J("formGroup",e.loginForm),o.xp6(4),o.Q6J("ngClass",o.VKq(7,f,e.submitted&&e.f.emailId.errors||!e.loginForm.controls.emailId.valid&&e.f.emailId.touched&&e.f.emailId.invalid)),o.xp6(1),o.Q6J("ngIf",!e.loginForm.controls.emailId.valid&&e.submitted||!e.loginForm.controls.emailId.valid&&e.f.emailId.touched&&e.f.emailId.invalid),o.xp6(4),o.Q6J("type",e.hide?"password":"text")("ngClass",o.VKq(9,f,e.submitted&&e.f.password.errors||e.f.password.touched&&e.f.password.invalid)),o.xp6(3),o.Oqu(e.hide?"visibility_off":"visibility"),o.xp6(1),o.Q6J("ngIf",!e.loginForm.controls.password.valid&&e.submitted||!e.loginForm.controls.password.valid&&e.f.password.touched&&e.f.password.invalid))},directives:[n._Y,n.JL,n.sg,g.Nt,n.Fj,n.c5,n.JJ,n.u,a.mk,a.O5,v.Hw,c.R9,Z.oG],styles:[".showingIcons[_ngcontent-%COMP%]{position:absolute;right:1rem;cursor:pointer;top:2.8rem}.box[_ngcontent-%COMP%]{color:#e9453b;margin:10px 2px}"]}),i})()}];let C=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=o.oAB({type:i}),i.\u0275inj=o.cJS({imports:[[l.Bz.forChild(k)],l.Bz]}),i})();var b=s(766),F=s(8345);let T=(()=>{class i{}return i.\u0275fac=function(t){return new(t||i)},i.\u0275mod=o.oAB({type:i}),i.\u0275inj=o.cJS({providers:[m._W,F.V],imports:[[a.ez,C,n.UX,n.u5,b.h,c.lN,g.c,m.Rh]]}),i})()}}]);