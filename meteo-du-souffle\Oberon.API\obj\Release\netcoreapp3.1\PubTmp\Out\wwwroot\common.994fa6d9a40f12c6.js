"use strict";(self.webpackChunkMeteodusouffle=self.webpackChunkMeteodusouffle||[]).push([[592],{9891:(d,p,n)=>{n.d(p,{T:()=>s});var a=n(520),r=n(5e3),o=n(8345);let s=(()=>{class i{constructor(e,t){this.http=e,this.config=t,this.httpOptions={headers:new a.WM({"Content-Type":"application/json"})}}updatefile(e){const t=new FormData;return t.append("PdfFileNameId",e.id),t.append("FileName",e.FileName),t.append("File",e.File),this.http.post(this.config.fileupload,t)}update(e){return this.http.post(this.config.updateUpload,JSON.stringify(e),this.httpOptions)}getDetailUpload(){return this.http.get(this.config.getDetailUpload)}deleteAccess(e,t){return this.http.delete(this.config.deleteUpload+"?UniqueFileName="+t+"&Id="+e)}updateImgfile(e){const t=new FormData;return t.append("Id",e.Id),t.append("FileName",e.FileName),t.append("File",e.File),this.http.post(this.config.updatesplashScreenUpload,t)}getsplashList(){return this.http.get(this.config.getsplashScreenList)}}return i.\u0275fac=function(e){return new(e||i)(r.LFG(a.eN),r.LFG(o.V))},i.\u0275prov=r.Yz7({token:i,factory:i.\u0275fac,providedIn:"root"}),i})()},7993:(d,p,n)=>{n.d(p,{N:()=>r});var a=n(5e3);let r=(()=>{class o{constructor(i){this._el=i}onInputChange(i){const l=this._el.nativeElement.value;this._el.nativeElement.value=l.replace(/[^0-9]*/g,""),l!==this._el.nativeElement.value&&i.stopPropagation()}}return o.\u0275fac=function(i){return new(i||o)(a.Y36(a.SBq))},o.\u0275dir=a.lG2({type:o,selectors:[["input","numbersOnly",""]],hostBindings:function(i,l){1&i&&a.NdJ("input",function(t){return l.onInputChange(t)})}}),o})()}}]);