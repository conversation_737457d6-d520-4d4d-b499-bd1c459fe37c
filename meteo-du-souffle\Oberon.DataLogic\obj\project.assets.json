{"version": 3, "targets": {".NETCoreApp,Version=v3.1": {"Humanizer.Core/2.8.26": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/Humanizer.dll": {}}}, "Microsoft.CSharp/4.7.0": {"type": "package", "compile": {"ref/netcoreapp2.0/_._": {}}, "runtime": {"lib/netcoreapp2.0/_._": {}}}, "Microsoft.EntityFrameworkCore/5.0.16": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Abstractions": "5.0.16", "Microsoft.EntityFrameworkCore.Analyzers": "5.0.16", "Microsoft.Extensions.Caching.Memory": "5.0.0", "Microsoft.Extensions.DependencyInjection": "5.0.2", "Microsoft.Extensions.Logging": "5.0.0", "System.Collections.Immutable": "5.0.0", "System.ComponentModel.Annotations": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.1"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll": {}}}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.16": {"type": "package", "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll": {}}}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.16": {"type": "package", "compile": {"lib/netstandard2.0/_._": {}}, "runtime": {"lib/netstandard2.0/_._": {}}}, "Microsoft.EntityFrameworkCore.Design/5.0.16": {"type": "package", "dependencies": {"Humanizer.Core": "2.8.26", "Microsoft.CSharp": "4.7.0", "Microsoft.EntityFrameworkCore.Relational": "5.0.16"}, "compile": {"lib/netstandard2.1/_._": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Design.dll": {}}, "build": {"build/netcoreapp3.0/Microsoft.EntityFrameworkCore.Design.props": {}}}, "Microsoft.EntityFrameworkCore.Relational/5.0.16": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "5.0.16", "Microsoft.Extensions.Configuration.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll": {}}}, "Microsoft.EntityFrameworkCore.Relational.Design/1.1.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Relational": "1.1.0", "NETStandard.Library": "1.6.1"}, "compile": {"lib/netstandard1.3/Microsoft.EntityFrameworkCore.Relational.Design.dll": {}}, "runtime": {"lib/netstandard1.3/Microsoft.EntityFrameworkCore.Relational.Design.dll": {}}}, "Microsoft.EntityFrameworkCore.Tools/5.0.16": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore.Design": "5.0.16"}, "compile": {"lib/netstandard2.1/_._": {}}, "runtime": {"lib/netstandard2.1/_._": {}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "build": {"build/Microsoft.Extensions.ApiDescription.Server.props": {}, "build/Microsoft.Extensions.ApiDescription.Server.targets": {}}, "buildMultiTargeting": {"buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props": {}, "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets": {}}}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll": {}}}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Caching.Abstractions": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll": {}}}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll": {}}}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll": {}}}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll": {}}}, "Microsoft.Extensions.Logging/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection": "5.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Logging.Abstractions": "5.0.0", "Microsoft.Extensions.Options": "5.0.0", "System.Diagnostics.DiagnosticSource": "5.0.0"}, "compile": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {}}, "runtime": {"lib/netstandard2.1/Microsoft.Extensions.Logging.dll": {}}}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll": {}}}, "Microsoft.Extensions.Options/5.0.0": {"type": "package", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "5.0.0", "Microsoft.Extensions.Primitives": "5.0.0"}, "compile": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.Extensions.Options.dll": {}}}, "Microsoft.Extensions.Primitives/5.0.0": {"type": "package", "compile": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {}}, "runtime": {"lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll": {}}}, "Microsoft.NETCore.App/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostPolicy": "2.2.8", "Microsoft.NETCore.Platforms": "2.2.4", "Microsoft.NETCore.Targets": "2.0.0", "NETStandard.Library": "2.0.3"}, "compile": {"ref/netcoreapp2.2/Microsoft.CSharp.dll": {}, "ref/netcoreapp2.2/Microsoft.VisualBasic.dll": {}, "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll": {}, "ref/netcoreapp2.2/System.AppContext.dll": {}, "ref/netcoreapp2.2/System.Buffers.dll": {}, "ref/netcoreapp2.2/System.Collections.Concurrent.dll": {}, "ref/netcoreapp2.2/System.Collections.Immutable.dll": {}, "ref/netcoreapp2.2/System.Collections.NonGeneric.dll": {}, "ref/netcoreapp2.2/System.Collections.Specialized.dll": {}, "ref/netcoreapp2.2/System.Collections.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll": {}, "ref/netcoreapp2.2/System.ComponentModel.dll": {}, "ref/netcoreapp2.2/System.Configuration.dll": {}, "ref/netcoreapp2.2/System.Console.dll": {}, "ref/netcoreapp2.2/System.Core.dll": {}, "ref/netcoreapp2.2/System.Data.Common.dll": {}, "ref/netcoreapp2.2/System.Data.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Debug.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Process.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Tools.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll": {}, "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll": {}, "ref/netcoreapp2.2/System.Drawing.Primitives.dll": {}, "ref/netcoreapp2.2/System.Drawing.dll": {}, "ref/netcoreapp2.2/System.Dynamic.Runtime.dll": {}, "ref/netcoreapp2.2/System.Globalization.Calendars.dll": {}, "ref/netcoreapp2.2/System.Globalization.Extensions.dll": {}, "ref/netcoreapp2.2/System.Globalization.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll": {}, "ref/netcoreapp2.2/System.IO.Compression.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll": {}, "ref/netcoreapp2.2/System.IO.FileSystem.dll": {}, "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll": {}, "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll": {}, "ref/netcoreapp2.2/System.IO.Pipes.dll": {}, "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll": {}, "ref/netcoreapp2.2/System.IO.dll": {}, "ref/netcoreapp2.2/System.Linq.Expressions.dll": {}, "ref/netcoreapp2.2/System.Linq.Parallel.dll": {}, "ref/netcoreapp2.2/System.Linq.Queryable.dll": {}, "ref/netcoreapp2.2/System.Linq.dll": {}, "ref/netcoreapp2.2/System.Memory.dll": {}, "ref/netcoreapp2.2/System.Net.Http.dll": {}, "ref/netcoreapp2.2/System.Net.HttpListener.dll": {}, "ref/netcoreapp2.2/System.Net.Mail.dll": {}, "ref/netcoreapp2.2/System.Net.NameResolution.dll": {}, "ref/netcoreapp2.2/System.Net.NetworkInformation.dll": {}, "ref/netcoreapp2.2/System.Net.Ping.dll": {}, "ref/netcoreapp2.2/System.Net.Primitives.dll": {}, "ref/netcoreapp2.2/System.Net.Requests.dll": {}, "ref/netcoreapp2.2/System.Net.Security.dll": {}, "ref/netcoreapp2.2/System.Net.ServicePoint.dll": {}, "ref/netcoreapp2.2/System.Net.Sockets.dll": {}, "ref/netcoreapp2.2/System.Net.WebClient.dll": {}, "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll": {}, "ref/netcoreapp2.2/System.Net.WebProxy.dll": {}, "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll": {}, "ref/netcoreapp2.2/System.Net.WebSockets.dll": {}, "ref/netcoreapp2.2/System.Net.dll": {}, "ref/netcoreapp2.2/System.Numerics.Vectors.dll": {}, "ref/netcoreapp2.2/System.Numerics.dll": {}, "ref/netcoreapp2.2/System.ObjectModel.dll": {}, "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll": {}, "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll": {}, "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll": {}, "ref/netcoreapp2.2/System.Reflection.Emit.dll": {}, "ref/netcoreapp2.2/System.Reflection.Extensions.dll": {}, "ref/netcoreapp2.2/System.Reflection.Metadata.dll": {}, "ref/netcoreapp2.2/System.Reflection.Primitives.dll": {}, "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll": {}, "ref/netcoreapp2.2/System.Reflection.dll": {}, "ref/netcoreapp2.2/System.Resources.Reader.dll": {}, "ref/netcoreapp2.2/System.Resources.ResourceManager.dll": {}, "ref/netcoreapp2.2/System.Resources.Writer.dll": {}, "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll": {}, "ref/netcoreapp2.2/System.Runtime.Extensions.dll": {}, "ref/netcoreapp2.2/System.Runtime.Handles.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll": {}, "ref/netcoreapp2.2/System.Runtime.InteropServices.dll": {}, "ref/netcoreapp2.2/System.Runtime.Loader.dll": {}, "ref/netcoreapp2.2/System.Runtime.Numerics.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll": {}, "ref/netcoreapp2.2/System.Runtime.Serialization.dll": {}, "ref/netcoreapp2.2/System.Runtime.dll": {}, "ref/netcoreapp2.2/System.Security.Claims.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll": {}, "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll": {}, "ref/netcoreapp2.2/System.Security.Principal.dll": {}, "ref/netcoreapp2.2/System.Security.SecureString.dll": {}, "ref/netcoreapp2.2/System.Security.dll": {}, "ref/netcoreapp2.2/System.ServiceModel.Web.dll": {}, "ref/netcoreapp2.2/System.ServiceProcess.dll": {}, "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll": {}, "ref/netcoreapp2.2/System.Text.Encoding.dll": {}, "ref/netcoreapp2.2/System.Text.RegularExpressions.dll": {}, "ref/netcoreapp2.2/System.Threading.Overlapped.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll": {}, "ref/netcoreapp2.2/System.Threading.Tasks.dll": {}, "ref/netcoreapp2.2/System.Threading.Thread.dll": {}, "ref/netcoreapp2.2/System.Threading.ThreadPool.dll": {}, "ref/netcoreapp2.2/System.Threading.Timer.dll": {}, "ref/netcoreapp2.2/System.Threading.dll": {}, "ref/netcoreapp2.2/System.Transactions.Local.dll": {}, "ref/netcoreapp2.2/System.Transactions.dll": {}, "ref/netcoreapp2.2/System.ValueTuple.dll": {}, "ref/netcoreapp2.2/System.Web.HttpUtility.dll": {}, "ref/netcoreapp2.2/System.Web.dll": {}, "ref/netcoreapp2.2/System.Windows.dll": {}, "ref/netcoreapp2.2/System.Xml.Linq.dll": {}, "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll": {}, "ref/netcoreapp2.2/System.Xml.Serialization.dll": {}, "ref/netcoreapp2.2/System.Xml.XDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XPath.dll": {}, "ref/netcoreapp2.2/System.Xml.XmlDocument.dll": {}, "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll": {}, "ref/netcoreapp2.2/System.Xml.dll": {}, "ref/netcoreapp2.2/System.dll": {}, "ref/netcoreapp2.2/WindowsBase.dll": {}, "ref/netcoreapp2.2/mscorlib.dll": {}, "ref/netcoreapp2.2/netstandard.dll": {}}, "build": {"build/netcoreapp2.2/Microsoft.NETCore.App.props": {}, "build/netcoreapp2.2/Microsoft.NETCore.App.targets": {}}}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {"type": "package"}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetHostResolver": "2.2.8"}}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"type": "package", "dependencies": {"Microsoft.NETCore.DotNetAppHost": "2.2.8"}}, "Microsoft.NETCore.Platforms/2.2.4": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.NETCore.Targets/2.0.0": {"type": "package", "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}}, "Microsoft.OpenApi/1.2.3": {"type": "package", "compile": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}, "runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {}}}, "NETStandard.Library/2.0.3": {"type": "package", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}, "compile": {"lib/netstandard1.0/_._": {}}, "runtime": {"lib/netstandard1.0/_._": {}}, "build": {"build/netstandard2.0/NETStandard.Library.targets": {}}}, "Npgsql/5.0.10": {"type": "package", "dependencies": {"System.Runtime.CompilerServices.Unsafe": "4.6.0"}, "compile": {"lib/netcoreapp3.1/Npgsql.dll": {}}, "runtime": {"lib/netcoreapp3.1/Npgsql.dll": {}}}, "Npgsql.EntityFrameworkCore.PostgreSQL/5.0.10": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "5.0.10", "Microsoft.EntityFrameworkCore.Abstractions": "5.0.10", "Microsoft.EntityFrameworkCore.Relational": "5.0.10", "Npgsql": "5.0.10"}, "compile": {"lib/netstandard2.1/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {}}, "runtime": {"lib/netstandard2.1/Npgsql.EntityFrameworkCore.PostgreSQL.dll": {}}}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design/1.1.0": {"type": "package", "dependencies": {"Microsoft.EntityFrameworkCore": "1.1.0", "Microsoft.EntityFrameworkCore.Relational": "1.1.0", "Microsoft.EntityFrameworkCore.Relational.Design": "1.1.0", "Microsoft.Extensions.DependencyInjection": "1.1.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "1.1.0", "Npgsql": "3.1.9", "Npgsql.EntityFrameworkCore.PostgreSQL": "1.1.0"}, "compile": {"lib/netstandard1.3/Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll": {}}, "runtime": {"lib/netstandard1.3/Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll": {}}}, "Swashbuckle.AspNetCore/6.3.1": {"type": "package", "dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.3.1", "Swashbuckle.AspNetCore.SwaggerGen": "6.3.1", "Swashbuckle.AspNetCore.SwaggerUI": "6.3.1"}, "build": {"build/Swashbuckle.AspNetCore.props": {}}}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"type": "package", "dependencies": {"Microsoft.OpenApi": "1.2.3"}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {}}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"type": "package", "dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.3.1"}, "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"type": "package", "compile": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}, "runtime": {"lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {}}, "frameworkReferences": ["Microsoft.AspNetCore.App"]}, "System.Collections.Immutable/5.0.0": {"type": "package", "compile": {"lib/netstandard2.0/System.Collections.Immutable.dll": {}}, "runtime": {"lib/netstandard2.0/System.Collections.Immutable.dll": {}}}, "System.ComponentModel.Annotations/5.0.0": {"type": "package", "compile": {"ref/netstandard2.1/System.ComponentModel.Annotations.dll": {}}, "runtime": {"lib/netstandard2.1/System.ComponentModel.Annotations.dll": {}}}, "System.Diagnostics.DiagnosticSource/5.0.1": {"type": "package", "compile": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}, "runtime": {"lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll": {}}}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {"type": "package", "compile": {"ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll": {}}, "runtime": {"lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll": {}}}}}, "libraries": {"Humanizer.Core/2.8.26": {"sha512": "OiKusGL20vby4uDEswj2IgkdchC1yQ6rwbIkZDVBPIR6al2b7n3pC91elBul9q33KaBgRKhbZH3+2Ur4fnWx2A==", "type": "package", "path": "humanizer.core/2.8.26", "files": [".nupkg.metadata", ".signature.p7s", "humanizer.core.2.8.26.nupkg.sha512", "humanizer.core.nuspec", "lib/netstandard1.0/Humanizer.dll", "lib/netstandard1.0/Humanizer.xml", "lib/netstandard2.0/Humanizer.dll", "lib/netstandard2.0/Humanizer.xml", "logo.png"]}, "Microsoft.CSharp/4.7.0": {"sha512": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA==", "type": "package", "path": "microsoft.csharp/4.7.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/netcore50/Microsoft.CSharp.dll", "lib/netcoreapp2.0/_._", "lib/netstandard1.3/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.dll", "lib/netstandard2.0/Microsoft.CSharp.xml", "lib/portable-net45+win8+wp8+wpa81/_._", "lib/uap10.0.16299/_._", "lib/win8/_._", "lib/wp80/_._", "lib/wpa81/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "microsoft.csharp.4.7.0.nupkg.sha512", "microsoft.csharp.nuspec", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/netcore50/Microsoft.CSharp.dll", "ref/netcore50/Microsoft.CSharp.xml", "ref/netcore50/de/Microsoft.CSharp.xml", "ref/netcore50/es/Microsoft.CSharp.xml", "ref/netcore50/fr/Microsoft.CSharp.xml", "ref/netcore50/it/Microsoft.CSharp.xml", "ref/netcore50/ja/Microsoft.CSharp.xml", "ref/netcore50/ko/Microsoft.CSharp.xml", "ref/netcore50/ru/Microsoft.CSharp.xml", "ref/netcore50/zh-hans/Microsoft.CSharp.xml", "ref/netcore50/zh-hant/Microsoft.CSharp.xml", "ref/netcoreapp2.0/_._", "ref/netstandard1.0/Microsoft.CSharp.dll", "ref/netstandard1.0/Microsoft.CSharp.xml", "ref/netstandard1.0/de/Microsoft.CSharp.xml", "ref/netstandard1.0/es/Microsoft.CSharp.xml", "ref/netstandard1.0/fr/Microsoft.CSharp.xml", "ref/netstandard1.0/it/Microsoft.CSharp.xml", "ref/netstandard1.0/ja/Microsoft.CSharp.xml", "ref/netstandard1.0/ko/Microsoft.CSharp.xml", "ref/netstandard1.0/ru/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hans/Microsoft.CSharp.xml", "ref/netstandard1.0/zh-hant/Microsoft.CSharp.xml", "ref/netstandard2.0/Microsoft.CSharp.dll", "ref/netstandard2.0/Microsoft.CSharp.xml", "ref/portable-net45+win8+wp8+wpa81/_._", "ref/uap10.0.16299/_._", "ref/win8/_._", "ref/wp80/_._", "ref/wpa81/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.EntityFrameworkCore/5.0.16": {"sha512": "niMGaRvTPPGAhuhKxFdmdfeW5Vo6rvhNXy2vnRotC7ToR/1ST6iDqZCIvW4L/Pt2+ISiFbvcqRQIE9MrNSMUZQ==", "type": "package", "path": "microsoft.entityframeworkcore/5.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.xml", "microsoft.entityframeworkcore.5.0.16.nupkg.sha512", "microsoft.entityframeworkcore.nuspec"]}, "Microsoft.EntityFrameworkCore.Abstractions/5.0.16": {"sha512": "nGESdyRnKGQ0PBUBrtVwGz3U13/C7RKcOctps2WozCGo5fHnQmcVtZ23mYc3Ri1LSMY+l4vLblBPJUzRaPaAWA==", "type": "package", "path": "microsoft.entityframeworkcore.abstractions/5.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Abstractions.xml", "microsoft.entityframeworkcore.abstractions.5.0.16.nupkg.sha512", "microsoft.entityframeworkcore.abstractions.nuspec"]}, "Microsoft.EntityFrameworkCore.Analyzers/5.0.16": {"sha512": "BiSrqLpJ6dv6xOH1xuHG/85edPfQo5DbuUZJqXNCOm3DD9BjnxaeVYnl2EUdadEUbCTTNVhXT0iKnaOpD5bv6A==", "type": "package", "path": "microsoft.entityframeworkcore.analyzers/5.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "analyzers/dotnet/cs/Microsoft.EntityFrameworkCore.Analyzers.dll", "lib/netstandard2.0/_._", "microsoft.entityframeworkcore.analyzers.5.0.16.nupkg.sha512", "microsoft.entityframeworkcore.analyzers.nuspec"]}, "Microsoft.EntityFrameworkCore.Design/5.0.16": {"sha512": "LoOs2a4OhPPPv4AdjRMDEn4FJXhLZ5L8uiriomNYTdS7UqP7+7b32s+xDuntqfJhIFMNURaZtT1nDeVLaseb+g==", "type": "package", "path": "microsoft.entityframeworkcore.design/5.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "build/netcoreapp3.0/Microsoft.EntityFrameworkCore.Design.props", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Design.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Design.xml", "microsoft.entityframeworkcore.design.5.0.16.nupkg.sha512", "microsoft.entityframeworkcore.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational/5.0.16": {"sha512": "B1ux0Pf1tTo7AXRldxInBWlFVm0/SVdV/7wSBxUmwy+uRO4oQCIJk69ot1DbL8YCQKjSMMB6IZkRTK95Haetow==", "type": "package", "path": "microsoft.entityframeworkcore.relational/5.0.16", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.dll", "lib/netstandard2.1/Microsoft.EntityFrameworkCore.Relational.xml", "microsoft.entityframeworkcore.relational.5.0.16.nupkg.sha512", "microsoft.entityframeworkcore.relational.nuspec"]}, "Microsoft.EntityFrameworkCore.Relational.Design/1.1.0": {"sha512": "fF0sVEUkoGeJutGIzwnYsJJ9o2hxEGJRpSaCxPq63rhSwn0hBmCwf9ET4QqYqO9Pc6+ODXenAQa095CWzuM4Kg==", "type": "package", "path": "microsoft.entityframeworkcore.relational.design/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Microsoft.EntityFrameworkCore.Relational.Design.dll", "lib/net451/Microsoft.EntityFrameworkCore.Relational.Design.xml", "lib/netstandard1.3/Microsoft.EntityFrameworkCore.Relational.Design.dll", "lib/netstandard1.3/Microsoft.EntityFrameworkCore.Relational.Design.xml", "microsoft.entityframeworkcore.relational.design.1.1.0.nupkg.sha512", "microsoft.entityframeworkcore.relational.design.nuspec"]}, "Microsoft.EntityFrameworkCore.Tools/5.0.16": {"sha512": "56FD9EtENIN/0SnQ7jPCURL0jgawraHhyJb2Qtf9y8PK8nOE+YfyabLsC4MGWxVgxe4c3pFon+3SWl0I1qqIFg==", "type": "package", "path": "microsoft.entityframeworkcore.tools/5.0.16", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "lib/netstandard2.1/_._", "microsoft.entityframeworkcore.tools.5.0.16.nupkg.sha512", "microsoft.entityframeworkcore.tools.nuspec", "tools/EntityFrameworkCore.PS2.psd1", "tools/EntityFrameworkCore.PS2.psm1", "tools/EntityFrameworkCore.psd1", "tools/EntityFrameworkCore.psm1", "tools/about_EntityFrameworkCore.help.txt", "tools/init.ps1", "tools/net461/any/ef.exe", "tools/net461/win-x86/ef.exe", "tools/netcoreapp2.0/any/ef.dll", "tools/netcoreapp2.0/any/ef.runtimeconfig.json"]}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"sha512": "LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "type": "package", "path": "microsoft.extensions.apidescription.server/3.0.0", "hasTools": true, "files": [".nupkg.metadata", ".signature.p7s", "build/Microsoft.Extensions.ApiDescription.Server.props", "build/Microsoft.Extensions.ApiDescription.Server.targets", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.props", "buildMultiTargeting/Microsoft.Extensions.ApiDescription.Server.targets", "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512", "microsoft.extensions.apidescription.server.nuspec", "tools/Newtonsoft.Json.dll", "tools/dotnet-getdocument.deps.json", "tools/dotnet-getdocument.dll", "tools/dotnet-getdocument.runtimeconfig.json", "tools/net461-x86/GetDocument.Insider.exe", "tools/net461-x86/GetDocument.Insider.exe.config", "tools/net461/GetDocument.Insider.exe", "tools/net461/GetDocument.Insider.exe.config", "tools/netcoreapp2.1/GetDocument.Insider.deps.json", "tools/netcoreapp2.1/GetDocument.Insider.dll", "tools/netcoreapp2.1/GetDocument.Insider.runtimeconfig.json"]}, "Microsoft.Extensions.Caching.Abstractions/5.0.0": {"sha512": "bu8As90/SBAouMZ6fJ+qRNo1X+KgHGrVueFhhYi+E5WqEhcnp2HoWRFnMzXQ6g4RdZbvPowFerSbKNH4Dtg5yg==", "type": "package", "path": "microsoft.extensions.caching.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Abstractions.dll", "lib/net461/Microsoft.Extensions.Caching.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Abstractions.xml", "microsoft.extensions.caching.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.caching.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Caching.Memory/5.0.0": {"sha512": "/1qPCleFOkJe0O+xmFqCNLFYQZTJz965sVw8CUB/BQgsApBwzAUsL2BUkDvQW+geRUVTXUS9zLa0pBjC2VJ1gA==", "type": "package", "path": "microsoft.extensions.caching.memory/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Caching.Memory.dll", "lib/net461/Microsoft.Extensions.Caching.Memory.xml", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.dll", "lib/netstandard2.0/Microsoft.Extensions.Caching.Memory.xml", "microsoft.extensions.caching.memory.5.0.0.nupkg.sha512", "microsoft.extensions.caching.memory.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Configuration.Abstractions/5.0.0": {"sha512": "ETjSBHMp3OAZ4HxGQYpwyGsD8Sw5FegQXphi0rpoGMT74S4+I2mm7XJEswwn59XAaKOzC15oDSOWEE8SzDCd6Q==", "type": "package", "path": "microsoft.extensions.configuration.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/net461/Microsoft.Extensions.Configuration.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Configuration.Abstractions.xml", "microsoft.extensions.configuration.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.configuration.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection/5.0.2": {"sha512": "xzFW00AZEvOXM1OX+0+AYH5op/Hf3u//e6wszBd/rK72sypD+jx5CtsHxM4BVuFBEs8SajfO4QzSJtrQaHDr4A==", "type": "package", "path": "microsoft.extensions.dependencyinjection/5.0.2", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.xml", "lib/net5.0/Microsoft.Extensions.DependencyInjection.dll", "lib/net5.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.xml", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.dll", "lib/netstandard2.1/Microsoft.Extensions.DependencyInjection.xml", "microsoft.extensions.dependencyinjection.5.0.2.nupkg.sha512", "microsoft.extensions.dependencyinjection.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.DependencyInjection.Abstractions/5.0.0": {"sha512": "ORj7Zh81gC69TyvmcUm9tSzytcy8AVousi+IVRAI8nLieQjOFryRusSFh7+aLk16FN9pQNqJAiMd7BTKINK0kA==", "type": "package", "path": "microsoft.extensions.dependencyinjection.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/net461/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.DependencyInjection.Abstractions.xml", "microsoft.extensions.dependencyinjection.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.dependencyinjection.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging/5.0.0": {"sha512": "MgOwK6tPzB6YNH21wssJcw/2MKwee8b2gI7SllYfn6rvTpIrVvVS5HAjSU2vqSku1fwqRvWP0MdIi14qjd93Aw==", "type": "package", "path": "microsoft.extensions.logging/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.dll", "lib/net461/Microsoft.Extensions.Logging.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.xml", "lib/netstandard2.1/Microsoft.Extensions.Logging.dll", "lib/netstandard2.1/Microsoft.Extensions.Logging.xml", "microsoft.extensions.logging.5.0.0.nupkg.sha512", "microsoft.extensions.logging.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Logging.Abstractions/5.0.0": {"sha512": "NxP6ahFcBnnSfwNBi2KH2Oz8Xl5Sm2krjId/jRR3I7teFphwiUoUeZPwTNA21EX+5PtjqmyAvKaOeBXcJjcH/w==", "type": "package", "path": "microsoft.extensions.logging.abstractions/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Logging.Abstractions.dll", "lib/net461/Microsoft.Extensions.Logging.Abstractions.xml", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.dll", "lib/netstandard2.0/Microsoft.Extensions.Logging.Abstractions.xml", "microsoft.extensions.logging.abstractions.5.0.0.nupkg.sha512", "microsoft.extensions.logging.abstractions.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Options/5.0.0": {"sha512": "CBvR92TCJ5uBIdd9/HzDSrxYak+0W/3+yxrNg8Qm6Bmrkh5L+nu6m3WeazQehcZ5q1/6dDA7J5YdQjim0165zg==", "type": "package", "path": "microsoft.extensions.options/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Options.dll", "lib/net461/Microsoft.Extensions.Options.xml", "lib/net5.0/Microsoft.Extensions.Options.dll", "lib/net5.0/Microsoft.Extensions.Options.xml", "lib/netstandard2.0/Microsoft.Extensions.Options.dll", "lib/netstandard2.0/Microsoft.Extensions.Options.xml", "microsoft.extensions.options.5.0.0.nupkg.sha512", "microsoft.extensions.options.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.Extensions.Primitives/5.0.0": {"sha512": "cI/VWn9G1fghXrNDagX9nYaaB/nokkZn0HYAawGaELQrl8InSezfe9OnfPZLcJq3esXxygh3hkq2c3qoV3SDyQ==", "type": "package", "path": "microsoft.extensions.primitives/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/Microsoft.Extensions.Primitives.dll", "lib/net461/Microsoft.Extensions.Primitives.xml", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.dll", "lib/netcoreapp3.0/Microsoft.Extensions.Primitives.xml", "lib/netstandard2.0/Microsoft.Extensions.Primitives.dll", "lib/netstandard2.0/Microsoft.Extensions.Primitives.xml", "microsoft.extensions.primitives.5.0.0.nupkg.sha512", "microsoft.extensions.primitives.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.App/2.2.8": {"sha512": "GOxlvyc8hFrnhDjYlm25JJ7PwoyeoOpZzcg6ZgF8n8l6VxezNupRkkTeA2ek1WsspN0CdAoA8e7iDVNU84/F+Q==", "type": "package", "path": "microsoft.netcore.app/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "Microsoft.NETCore.App.versions.txt", "THIRD-PARTY-NOTICES.TXT", "build/netcoreapp2.2/Microsoft.NETCore.App.PlatformManifest.txt", "build/netcoreapp2.2/Microsoft.NETCore.App.props", "build/netcoreapp2.2/Microsoft.NETCore.App.targets", "microsoft.netcore.app.2.2.8.nupkg.sha512", "microsoft.netcore.app.nuspec", "ref/netcoreapp2.2/Microsoft.CSharp.dll", "ref/netcoreapp2.2/Microsoft.CSharp.xml", "ref/netcoreapp2.2/Microsoft.VisualBasic.dll", "ref/netcoreapp2.2/Microsoft.VisualBasic.xml", "ref/netcoreapp2.2/Microsoft.Win32.Primitives.dll", "ref/netcoreapp2.2/Microsoft.Win32.Primitives.xml", "ref/netcoreapp2.2/System.AppContext.dll", "ref/netcoreapp2.2/System.Buffers.dll", "ref/netcoreapp2.2/System.Buffers.xml", "ref/netcoreapp2.2/System.Collections.Concurrent.dll", "ref/netcoreapp2.2/System.Collections.Concurrent.xml", "ref/netcoreapp2.2/System.Collections.Immutable.dll", "ref/netcoreapp2.2/System.Collections.Immutable.xml", "ref/netcoreapp2.2/System.Collections.NonGeneric.dll", "ref/netcoreapp2.2/System.Collections.NonGeneric.xml", "ref/netcoreapp2.2/System.Collections.Specialized.dll", "ref/netcoreapp2.2/System.Collections.Specialized.xml", "ref/netcoreapp2.2/System.Collections.dll", "ref/netcoreapp2.2/System.Collections.xml", "ref/netcoreapp2.2/System.ComponentModel.Annotations.dll", "ref/netcoreapp2.2/System.ComponentModel.Annotations.xml", "ref/netcoreapp2.2/System.ComponentModel.DataAnnotations.dll", "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.dll", "ref/netcoreapp2.2/System.ComponentModel.EventBasedAsync.xml", "ref/netcoreapp2.2/System.ComponentModel.Primitives.dll", "ref/netcoreapp2.2/System.ComponentModel.Primitives.xml", "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.dll", "ref/netcoreapp2.2/System.ComponentModel.TypeConverter.xml", "ref/netcoreapp2.2/System.ComponentModel.dll", "ref/netcoreapp2.2/System.ComponentModel.xml", "ref/netcoreapp2.2/System.Configuration.dll", "ref/netcoreapp2.2/System.Console.dll", "ref/netcoreapp2.2/System.Console.xml", "ref/netcoreapp2.2/System.Core.dll", "ref/netcoreapp2.2/System.Data.Common.dll", "ref/netcoreapp2.2/System.Data.Common.xml", "ref/netcoreapp2.2/System.Data.dll", "ref/netcoreapp2.2/System.Diagnostics.Contracts.dll", "ref/netcoreapp2.2/System.Diagnostics.Contracts.xml", "ref/netcoreapp2.2/System.Diagnostics.Debug.dll", "ref/netcoreapp2.2/System.Diagnostics.Debug.xml", "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.dll", "ref/netcoreapp2.2/System.Diagnostics.DiagnosticSource.xml", "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.dll", "ref/netcoreapp2.2/System.Diagnostics.FileVersionInfo.xml", "ref/netcoreapp2.2/System.Diagnostics.Process.dll", "ref/netcoreapp2.2/System.Diagnostics.Process.xml", "ref/netcoreapp2.2/System.Diagnostics.StackTrace.dll", "ref/netcoreapp2.2/System.Diagnostics.StackTrace.xml", "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.dll", "ref/netcoreapp2.2/System.Diagnostics.TextWriterTraceListener.xml", "ref/netcoreapp2.2/System.Diagnostics.Tools.dll", "ref/netcoreapp2.2/System.Diagnostics.Tools.xml", "ref/netcoreapp2.2/System.Diagnostics.TraceSource.dll", "ref/netcoreapp2.2/System.Diagnostics.TraceSource.xml", "ref/netcoreapp2.2/System.Diagnostics.Tracing.dll", "ref/netcoreapp2.2/System.Diagnostics.Tracing.xml", "ref/netcoreapp2.2/System.Drawing.Primitives.dll", "ref/netcoreapp2.2/System.Drawing.Primitives.xml", "ref/netcoreapp2.2/System.Drawing.dll", "ref/netcoreapp2.2/System.Dynamic.Runtime.dll", "ref/netcoreapp2.2/System.Globalization.Calendars.dll", "ref/netcoreapp2.2/System.Globalization.Extensions.dll", "ref/netcoreapp2.2/System.Globalization.dll", "ref/netcoreapp2.2/System.IO.Compression.Brotli.dll", "ref/netcoreapp2.2/System.IO.Compression.FileSystem.dll", "ref/netcoreapp2.2/System.IO.Compression.ZipFile.dll", "ref/netcoreapp2.2/System.IO.Compression.ZipFile.xml", "ref/netcoreapp2.2/System.IO.Compression.dll", "ref/netcoreapp2.2/System.IO.Compression.xml", "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.dll", "ref/netcoreapp2.2/System.IO.FileSystem.DriveInfo.xml", "ref/netcoreapp2.2/System.IO.FileSystem.Primitives.dll", "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.dll", "ref/netcoreapp2.2/System.IO.FileSystem.Watcher.xml", "ref/netcoreapp2.2/System.IO.FileSystem.dll", "ref/netcoreapp2.2/System.IO.FileSystem.xml", "ref/netcoreapp2.2/System.IO.IsolatedStorage.dll", "ref/netcoreapp2.2/System.IO.IsolatedStorage.xml", "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.dll", "ref/netcoreapp2.2/System.IO.MemoryMappedFiles.xml", "ref/netcoreapp2.2/System.IO.Pipes.dll", "ref/netcoreapp2.2/System.IO.Pipes.xml", "ref/netcoreapp2.2/System.IO.UnmanagedMemoryStream.dll", "ref/netcoreapp2.2/System.IO.dll", "ref/netcoreapp2.2/System.Linq.Expressions.dll", "ref/netcoreapp2.2/System.Linq.Expressions.xml", "ref/netcoreapp2.2/System.Linq.Parallel.dll", "ref/netcoreapp2.2/System.Linq.Parallel.xml", "ref/netcoreapp2.2/System.Linq.Queryable.dll", "ref/netcoreapp2.2/System.Linq.Queryable.xml", "ref/netcoreapp2.2/System.Linq.dll", "ref/netcoreapp2.2/System.Linq.xml", "ref/netcoreapp2.2/System.Memory.dll", "ref/netcoreapp2.2/System.Memory.xml", "ref/netcoreapp2.2/System.Net.Http.dll", "ref/netcoreapp2.2/System.Net.Http.xml", "ref/netcoreapp2.2/System.Net.HttpListener.dll", "ref/netcoreapp2.2/System.Net.HttpListener.xml", "ref/netcoreapp2.2/System.Net.Mail.dll", "ref/netcoreapp2.2/System.Net.Mail.xml", "ref/netcoreapp2.2/System.Net.NameResolution.dll", "ref/netcoreapp2.2/System.Net.NameResolution.xml", "ref/netcoreapp2.2/System.Net.NetworkInformation.dll", "ref/netcoreapp2.2/System.Net.NetworkInformation.xml", "ref/netcoreapp2.2/System.Net.Ping.dll", "ref/netcoreapp2.2/System.Net.Ping.xml", "ref/netcoreapp2.2/System.Net.Primitives.dll", "ref/netcoreapp2.2/System.Net.Primitives.xml", "ref/netcoreapp2.2/System.Net.Requests.dll", "ref/netcoreapp2.2/System.Net.Requests.xml", "ref/netcoreapp2.2/System.Net.Security.dll", "ref/netcoreapp2.2/System.Net.Security.xml", "ref/netcoreapp2.2/System.Net.ServicePoint.dll", "ref/netcoreapp2.2/System.Net.ServicePoint.xml", "ref/netcoreapp2.2/System.Net.Sockets.dll", "ref/netcoreapp2.2/System.Net.Sockets.xml", "ref/netcoreapp2.2/System.Net.WebClient.dll", "ref/netcoreapp2.2/System.Net.WebClient.xml", "ref/netcoreapp2.2/System.Net.WebHeaderCollection.dll", "ref/netcoreapp2.2/System.Net.WebHeaderCollection.xml", "ref/netcoreapp2.2/System.Net.WebProxy.dll", "ref/netcoreapp2.2/System.Net.WebProxy.xml", "ref/netcoreapp2.2/System.Net.WebSockets.Client.dll", "ref/netcoreapp2.2/System.Net.WebSockets.Client.xml", "ref/netcoreapp2.2/System.Net.WebSockets.dll", "ref/netcoreapp2.2/System.Net.WebSockets.xml", "ref/netcoreapp2.2/System.Net.dll", "ref/netcoreapp2.2/System.Numerics.Vectors.dll", "ref/netcoreapp2.2/System.Numerics.Vectors.xml", "ref/netcoreapp2.2/System.Numerics.dll", "ref/netcoreapp2.2/System.ObjectModel.dll", "ref/netcoreapp2.2/System.ObjectModel.xml", "ref/netcoreapp2.2/System.Reflection.DispatchProxy.dll", "ref/netcoreapp2.2/System.Reflection.DispatchProxy.xml", "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.dll", "ref/netcoreapp2.2/System.Reflection.Emit.ILGeneration.xml", "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.dll", "ref/netcoreapp2.2/System.Reflection.Emit.Lightweight.xml", "ref/netcoreapp2.2/System.Reflection.Emit.dll", "ref/netcoreapp2.2/System.Reflection.Emit.xml", "ref/netcoreapp2.2/System.Reflection.Extensions.dll", "ref/netcoreapp2.2/System.Reflection.Metadata.dll", "ref/netcoreapp2.2/System.Reflection.Metadata.xml", "ref/netcoreapp2.2/System.Reflection.Primitives.dll", "ref/netcoreapp2.2/System.Reflection.Primitives.xml", "ref/netcoreapp2.2/System.Reflection.TypeExtensions.dll", "ref/netcoreapp2.2/System.Reflection.TypeExtensions.xml", "ref/netcoreapp2.2/System.Reflection.dll", "ref/netcoreapp2.2/System.Resources.Reader.dll", "ref/netcoreapp2.2/System.Resources.ResourceManager.dll", "ref/netcoreapp2.2/System.Resources.ResourceManager.xml", "ref/netcoreapp2.2/System.Resources.Writer.dll", "ref/netcoreapp2.2/System.Resources.Writer.xml", "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.dll", "ref/netcoreapp2.2/System.Runtime.CompilerServices.VisualC.xml", "ref/netcoreapp2.2/System.Runtime.Extensions.dll", "ref/netcoreapp2.2/System.Runtime.Extensions.xml", "ref/netcoreapp2.2/System.Runtime.Handles.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.RuntimeInformation.xml", "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.WindowsRuntime.xml", "ref/netcoreapp2.2/System.Runtime.InteropServices.dll", "ref/netcoreapp2.2/System.Runtime.InteropServices.xml", "ref/netcoreapp2.2/System.Runtime.Loader.dll", "ref/netcoreapp2.2/System.Runtime.Loader.xml", "ref/netcoreapp2.2/System.Runtime.Numerics.dll", "ref/netcoreapp2.2/System.Runtime.Numerics.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Formatters.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Json.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Json.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Primitives.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.dll", "ref/netcoreapp2.2/System.Runtime.Serialization.Xml.xml", "ref/netcoreapp2.2/System.Runtime.Serialization.dll", "ref/netcoreapp2.2/System.Runtime.dll", "ref/netcoreapp2.2/System.Runtime.xml", "ref/netcoreapp2.2/System.Security.Claims.dll", "ref/netcoreapp2.2/System.Security.Claims.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Algorithms.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Csp.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Csp.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Encoding.xml", "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.dll", "ref/netcoreapp2.2/System.Security.Cryptography.Primitives.xml", "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.dll", "ref/netcoreapp2.2/System.Security.Cryptography.X509Certificates.xml", "ref/netcoreapp2.2/System.Security.Principal.dll", "ref/netcoreapp2.2/System.Security.Principal.xml", "ref/netcoreapp2.2/System.Security.SecureString.dll", "ref/netcoreapp2.2/System.Security.dll", "ref/netcoreapp2.2/System.ServiceModel.Web.dll", "ref/netcoreapp2.2/System.ServiceProcess.dll", "ref/netcoreapp2.2/System.Text.Encoding.Extensions.dll", "ref/netcoreapp2.2/System.Text.Encoding.Extensions.xml", "ref/netcoreapp2.2/System.Text.Encoding.dll", "ref/netcoreapp2.2/System.Text.RegularExpressions.dll", "ref/netcoreapp2.2/System.Text.RegularExpressions.xml", "ref/netcoreapp2.2/System.Threading.Overlapped.dll", "ref/netcoreapp2.2/System.Threading.Overlapped.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Dataflow.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Extensions.xml", "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.dll", "ref/netcoreapp2.2/System.Threading.Tasks.Parallel.xml", "ref/netcoreapp2.2/System.Threading.Tasks.dll", "ref/netcoreapp2.2/System.Threading.Tasks.xml", "ref/netcoreapp2.2/System.Threading.Thread.dll", "ref/netcoreapp2.2/System.Threading.Thread.xml", "ref/netcoreapp2.2/System.Threading.ThreadPool.dll", "ref/netcoreapp2.2/System.Threading.ThreadPool.xml", "ref/netcoreapp2.2/System.Threading.Timer.dll", "ref/netcoreapp2.2/System.Threading.Timer.xml", "ref/netcoreapp2.2/System.Threading.dll", "ref/netcoreapp2.2/System.Threading.xml", "ref/netcoreapp2.2/System.Transactions.Local.dll", "ref/netcoreapp2.2/System.Transactions.Local.xml", "ref/netcoreapp2.2/System.Transactions.dll", "ref/netcoreapp2.2/System.ValueTuple.dll", "ref/netcoreapp2.2/System.Web.HttpUtility.dll", "ref/netcoreapp2.2/System.Web.HttpUtility.xml", "ref/netcoreapp2.2/System.Web.dll", "ref/netcoreapp2.2/System.Windows.dll", "ref/netcoreapp2.2/System.Xml.Linq.dll", "ref/netcoreapp2.2/System.Xml.ReaderWriter.dll", "ref/netcoreapp2.2/System.Xml.ReaderWriter.xml", "ref/netcoreapp2.2/System.Xml.Serialization.dll", "ref/netcoreapp2.2/System.Xml.XDocument.dll", "ref/netcoreapp2.2/System.Xml.XDocument.xml", "ref/netcoreapp2.2/System.Xml.XPath.XDocument.dll", "ref/netcoreapp2.2/System.Xml.XPath.XDocument.xml", "ref/netcoreapp2.2/System.Xml.XPath.dll", "ref/netcoreapp2.2/System.Xml.XPath.xml", "ref/netcoreapp2.2/System.Xml.XmlDocument.dll", "ref/netcoreapp2.2/System.Xml.XmlSerializer.dll", "ref/netcoreapp2.2/System.Xml.XmlSerializer.xml", "ref/netcoreapp2.2/System.Xml.dll", "ref/netcoreapp2.2/System.dll", "ref/netcoreapp2.2/WindowsBase.dll", "ref/netcoreapp2.2/mscorlib.dll", "ref/netcoreapp2.2/netstandard.dll", "runtime.json"]}, "Microsoft.NETCore.DotNetAppHost/2.2.8": {"sha512": "Lh1F6z41levvtfC3KuuiQe9ppWKRP1oIB42vP1QNQE4uumo95h+LpjPDeysX1DlTjCzG0BVGSUEpCW5fHkni7w==", "type": "package", "path": "microsoft.netcore.dotnetapphost/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnetapphost.2.2.8.nupkg.sha512", "microsoft.netcore.dotnetapphost.nuspec", "runtime.json"]}, "Microsoft.NETCore.DotNetHostPolicy/2.2.8": {"sha512": "rOHr0Dk87vaiq9d1hMpXETB4IKq1jIiPQlVKNUjRGilK/cjOcadhsk+1MsrJ/GnM3eovhy8zW2PGkN8pYEolnw==", "type": "package", "path": "microsoft.netcore.dotnethostpolicy/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnethostpolicy.2.2.8.nupkg.sha512", "microsoft.netcore.dotnethostpolicy.nuspec", "runtime.json"]}, "Microsoft.NETCore.DotNetHostResolver/2.2.8": {"sha512": "culLr+x2GvUkXVGi4ULZ7jmWJEhuAMyS7iTWBlkWnqbKtYJ36ZlgHbw/6qTm82790gJemEFeo9RehDwfRXfJzA==", "type": "package", "path": "microsoft.netcore.dotnethostresolver/2.2.8", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "microsoft.netcore.dotnethostresolver.2.2.8.nupkg.sha512", "microsoft.netcore.dotnethostresolver.nuspec", "runtime.json"]}, "Microsoft.NETCore.Platforms/2.2.4": {"sha512": "ZeCe9PRhMpKzVWrNgTvWpLjJigppErzN663lJOqAzcx0xjXpcAMpIImFI46IE1gze18VWw6bbfo7JDkcaRWuOg==", "type": "package", "path": "microsoft.netcore.platforms/2.2.4", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.platforms.2.2.4.nupkg.sha512", "microsoft.netcore.platforms.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.NETCore.Targets/2.0.0": {"sha512": "odP/tJj1z6GylFpNo7pMtbd/xQgTC3Ex2If63dRTL38bBNMwsBnJ+RceUIyHdRBC0oik/3NehYT+oECwBhIM3Q==", "type": "package", "path": "microsoft.netcore.targets/2.0.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netstandard1.0/_._", "microsoft.netcore.targets.2.0.0.nupkg.sha512", "microsoft.netcore.targets.nuspec", "runtime.json", "useSharedDesignerContext.txt", "version.txt"]}, "Microsoft.OpenApi/1.2.3": {"sha512": "Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "type": "package", "path": "microsoft.openapi/1.2.3", "files": [".nupkg.metadata", ".signature.p7s", "lib/net46/Microsoft.OpenApi.dll", "lib/net46/Microsoft.OpenApi.pdb", "lib/net46/Microsoft.OpenApi.xml", "lib/netstandard2.0/Microsoft.OpenApi.dll", "lib/netstandard2.0/Microsoft.OpenApi.pdb", "lib/netstandard2.0/Microsoft.OpenApi.xml", "microsoft.openapi.1.2.3.nupkg.sha512", "microsoft.openapi.nuspec"]}, "NETStandard.Library/2.0.3": {"sha512": "st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "type": "package", "path": "netstandard.library/2.0.3", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "build/netstandard2.0/NETStandard.Library.targets", "build/netstandard2.0/ref/Microsoft.Win32.Primitives.dll", "build/netstandard2.0/ref/System.AppContext.dll", "build/netstandard2.0/ref/System.Collections.Concurrent.dll", "build/netstandard2.0/ref/System.Collections.NonGeneric.dll", "build/netstandard2.0/ref/System.Collections.Specialized.dll", "build/netstandard2.0/ref/System.Collections.dll", "build/netstandard2.0/ref/System.ComponentModel.Composition.dll", "build/netstandard2.0/ref/System.ComponentModel.EventBasedAsync.dll", "build/netstandard2.0/ref/System.ComponentModel.Primitives.dll", "build/netstandard2.0/ref/System.ComponentModel.TypeConverter.dll", "build/netstandard2.0/ref/System.ComponentModel.dll", "build/netstandard2.0/ref/System.Console.dll", "build/netstandard2.0/ref/System.Core.dll", "build/netstandard2.0/ref/System.Data.Common.dll", "build/netstandard2.0/ref/System.Data.dll", "build/netstandard2.0/ref/System.Diagnostics.Contracts.dll", "build/netstandard2.0/ref/System.Diagnostics.Debug.dll", "build/netstandard2.0/ref/System.Diagnostics.FileVersionInfo.dll", "build/netstandard2.0/ref/System.Diagnostics.Process.dll", "build/netstandard2.0/ref/System.Diagnostics.StackTrace.dll", "build/netstandard2.0/ref/System.Diagnostics.TextWriterTraceListener.dll", "build/netstandard2.0/ref/System.Diagnostics.Tools.dll", "build/netstandard2.0/ref/System.Diagnostics.TraceSource.dll", "build/netstandard2.0/ref/System.Diagnostics.Tracing.dll", "build/netstandard2.0/ref/System.Drawing.Primitives.dll", "build/netstandard2.0/ref/System.Drawing.dll", "build/netstandard2.0/ref/System.Dynamic.Runtime.dll", "build/netstandard2.0/ref/System.Globalization.Calendars.dll", "build/netstandard2.0/ref/System.Globalization.Extensions.dll", "build/netstandard2.0/ref/System.Globalization.dll", "build/netstandard2.0/ref/System.IO.Compression.FileSystem.dll", "build/netstandard2.0/ref/System.IO.Compression.ZipFile.dll", "build/netstandard2.0/ref/System.IO.Compression.dll", "build/netstandard2.0/ref/System.IO.FileSystem.DriveInfo.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Primitives.dll", "build/netstandard2.0/ref/System.IO.FileSystem.Watcher.dll", "build/netstandard2.0/ref/System.IO.FileSystem.dll", "build/netstandard2.0/ref/System.IO.IsolatedStorage.dll", "build/netstandard2.0/ref/System.IO.MemoryMappedFiles.dll", "build/netstandard2.0/ref/System.IO.Pipes.dll", "build/netstandard2.0/ref/System.IO.UnmanagedMemoryStream.dll", "build/netstandard2.0/ref/System.IO.dll", "build/netstandard2.0/ref/System.Linq.Expressions.dll", "build/netstandard2.0/ref/System.Linq.Parallel.dll", "build/netstandard2.0/ref/System.Linq.Queryable.dll", "build/netstandard2.0/ref/System.Linq.dll", "build/netstandard2.0/ref/System.Net.Http.dll", "build/netstandard2.0/ref/System.Net.NameResolution.dll", "build/netstandard2.0/ref/System.Net.NetworkInformation.dll", "build/netstandard2.0/ref/System.Net.Ping.dll", "build/netstandard2.0/ref/System.Net.Primitives.dll", "build/netstandard2.0/ref/System.Net.Requests.dll", "build/netstandard2.0/ref/System.Net.Security.dll", "build/netstandard2.0/ref/System.Net.Sockets.dll", "build/netstandard2.0/ref/System.Net.WebHeaderCollection.dll", "build/netstandard2.0/ref/System.Net.WebSockets.Client.dll", "build/netstandard2.0/ref/System.Net.WebSockets.dll", "build/netstandard2.0/ref/System.Net.dll", "build/netstandard2.0/ref/System.Numerics.dll", "build/netstandard2.0/ref/System.ObjectModel.dll", "build/netstandard2.0/ref/System.Reflection.Extensions.dll", "build/netstandard2.0/ref/System.Reflection.Primitives.dll", "build/netstandard2.0/ref/System.Reflection.dll", "build/netstandard2.0/ref/System.Resources.Reader.dll", "build/netstandard2.0/ref/System.Resources.ResourceManager.dll", "build/netstandard2.0/ref/System.Resources.Writer.dll", "build/netstandard2.0/ref/System.Runtime.CompilerServices.VisualC.dll", "build/netstandard2.0/ref/System.Runtime.Extensions.dll", "build/netstandard2.0/ref/System.Runtime.Handles.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.RuntimeInformation.dll", "build/netstandard2.0/ref/System.Runtime.InteropServices.dll", "build/netstandard2.0/ref/System.Runtime.Numerics.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Formatters.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Json.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Primitives.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.Xml.dll", "build/netstandard2.0/ref/System.Runtime.Serialization.dll", "build/netstandard2.0/ref/System.Runtime.dll", "build/netstandard2.0/ref/System.Security.Claims.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Algorithms.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Csp.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Encoding.dll", "build/netstandard2.0/ref/System.Security.Cryptography.Primitives.dll", "build/netstandard2.0/ref/System.Security.Cryptography.X509Certificates.dll", "build/netstandard2.0/ref/System.Security.Principal.dll", "build/netstandard2.0/ref/System.Security.SecureString.dll", "build/netstandard2.0/ref/System.ServiceModel.Web.dll", "build/netstandard2.0/ref/System.Text.Encoding.Extensions.dll", "build/netstandard2.0/ref/System.Text.Encoding.dll", "build/netstandard2.0/ref/System.Text.RegularExpressions.dll", "build/netstandard2.0/ref/System.Threading.Overlapped.dll", "build/netstandard2.0/ref/System.Threading.Tasks.Parallel.dll", "build/netstandard2.0/ref/System.Threading.Tasks.dll", "build/netstandard2.0/ref/System.Threading.Thread.dll", "build/netstandard2.0/ref/System.Threading.ThreadPool.dll", "build/netstandard2.0/ref/System.Threading.Timer.dll", "build/netstandard2.0/ref/System.Threading.dll", "build/netstandard2.0/ref/System.Transactions.dll", "build/netstandard2.0/ref/System.ValueTuple.dll", "build/netstandard2.0/ref/System.Web.dll", "build/netstandard2.0/ref/System.Windows.dll", "build/netstandard2.0/ref/System.Xml.Linq.dll", "build/netstandard2.0/ref/System.Xml.ReaderWriter.dll", "build/netstandard2.0/ref/System.Xml.Serialization.dll", "build/netstandard2.0/ref/System.Xml.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.XDocument.dll", "build/netstandard2.0/ref/System.Xml.XPath.dll", "build/netstandard2.0/ref/System.Xml.XmlDocument.dll", "build/netstandard2.0/ref/System.Xml.XmlSerializer.dll", "build/netstandard2.0/ref/System.Xml.dll", "build/netstandard2.0/ref/System.dll", "build/netstandard2.0/ref/mscorlib.dll", "build/netstandard2.0/ref/netstandard.dll", "build/netstandard2.0/ref/netstandard.xml", "lib/netstandard1.0/_._", "netstandard.library.2.0.3.nupkg.sha512", "netstandard.library.nuspec"]}, "Npgsql/5.0.10": {"sha512": "3TB9le3lfu5Hc+LSHqMCVLcA+qUPg1enyM4+u0pMUBmNNGwc0sVPrnfnys2TVZIdkF8Aww/AZlnJHDsnEGqD0g==", "type": "package", "path": "npgsql/5.0.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Npgsql.dll", "lib/net5.0/Npgsql.xml", "lib/netcoreapp3.1/Npgsql.dll", "lib/netcoreapp3.1/Npgsql.xml", "lib/netstandard2.0/Npgsql.dll", "lib/netstandard2.0/Npgsql.xml", "lib/netstandard2.1/Npgsql.dll", "lib/netstandard2.1/Npgsql.xml", "npgsql.5.0.10.nupkg.sha512", "npgsql.nuspec", "postgresql.png"]}, "Npgsql.EntityFrameworkCore.PostgreSQL/5.0.10": {"sha512": "3K+YNZFdwMcTxY8aW9m6BHyzPce/WFdDKPESP4uCXk6xNS6Ujq6zhi7+KXkzb7iNB0OYretEuF3ezqkk4ZnJMg==", "type": "package", "path": "npgsql.entityframeworkcore.postgresql/5.0.10", "files": [".nupkg.metadata", ".signature.p7s", "lib/netstandard2.1/Npgsql.EntityFrameworkCore.PostgreSQL.dll", "npgsql.entityframeworkcore.postgresql.5.0.10.nupkg.sha512", "npgsql.entityframeworkcore.postgresql.nuspec", "postgresql.png"]}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design/1.1.0": {"sha512": "+taA6t8sRI0JTbyCZH0GobEtJYvU4aAGBf5/2/CutWHiYpO2nLfJ8SQSyWYMKakrwn1/NDM9VV85mGEAzBZ7LQ==", "type": "package", "path": "npgsql.entityframeworkcore.postgresql.design/1.1.0", "files": [".nupkg.metadata", ".signature.p7s", "lib/net451/Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll", "lib/netstandard1.3/Npgsql.EntityFrameworkCore.PostgreSQL.Design.dll", "npgsql.entityframeworkcore.postgresql.design.1.1.0.nupkg.sha512", "npgsql.entityframeworkcore.postgresql.design.nuspec"]}, "Swashbuckle.AspNetCore/6.3.1": {"sha512": "JFk0+HHUPdjYuPhkpGBMLi2JtnEuWkE2pp0yXQp64DmeMe+Fb0hZyVNq/ENJ2vQNso7Zg+C758WmR/xyAl36bA==", "type": "package", "path": "swashbuckle.aspnetcore/6.3.1", "files": [".nupkg.metadata", ".signature.p7s", "build/Swashbuckle.AspNetCore.props", "swashbuckle.aspnetcore.6.3.1.nupkg.sha512", "swashbuckle.aspnetcore.nuspec"]}, "Swashbuckle.AspNetCore.Swagger/6.3.1": {"sha512": "idAFh4xhyJHYHfdLVOOn+BmscBul1OQbWsnL6YPJE8tO/0y6S79hDCvs6OY5VI093/9+1pYY3j31Zet9yaDZjA==", "type": "package", "path": "swashbuckle.aspnetcore.swagger/6.3.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net5.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/net6.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.Swagger.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.Swagger.xml", "swashbuckle.aspnetcore.swagger.6.3.1.nupkg.sha512", "swashbuckle.aspnetcore.swagger.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerGen/6.3.1": {"sha512": "+uoBV4h/6NhCPLoTofSmuOnZ+usu4PW1jP6l4OHwPyu2frbYXGNpJsHs5uUXXn929OiVQkT8wo3Lj/o+P99Ejg==", "type": "package", "path": "swashbuckle.aspnetcore.swaggergen/6.3.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerGen.xml", "swashbuckle.aspnetcore.swaggergen.6.3.1.nupkg.sha512", "swashbuckle.aspnetcore.swaggergen.nuspec"]}, "Swashbuckle.AspNetCore.SwaggerUI/6.3.1": {"sha512": "JLm9hN67jh7RHsX3H30+tb432Li8xm/qV5lRyMMkyHYMfWitIuKAAdrpo2ILcHOIeH7CLMuOO2hp/iLBmE+Bkw==", "type": "package", "path": "swashbuckle.aspnetcore.swaggerui/6.3.1", "files": [".nupkg.metadata", ".signature.p7s", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net5.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netcoreapp3.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.dll", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.pdb", "lib/netstandard2.0/Swashbuckle.AspNetCore.SwaggerUI.xml", "swashbuckle.aspnetcore.swaggerui.6.3.1.nupkg.sha512", "swashbuckle.aspnetcore.swaggerui.nuspec"]}, "System.Collections.Immutable/5.0.0": {"sha512": "FXkLXiK0sVVewcso0imKQoOxjoPAj42R8HtjjbSjVPAzwDfzoyoznWxgA3c38LDbN9SJux1xXoXYAhz98j7r2g==", "type": "package", "path": "system.collections.immutable/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net461/System.Collections.Immutable.dll", "lib/net461/System.Collections.Immutable.xml", "lib/netstandard1.0/System.Collections.Immutable.dll", "lib/netstandard1.0/System.Collections.Immutable.xml", "lib/netstandard1.3/System.Collections.Immutable.dll", "lib/netstandard1.3/System.Collections.Immutable.xml", "lib/netstandard2.0/System.Collections.Immutable.dll", "lib/netstandard2.0/System.Collections.Immutable.xml", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.dll", "lib/portable-net45+win8+wp8+wpa81/System.Collections.Immutable.xml", "system.collections.immutable.5.0.0.nupkg.sha512", "system.collections.immutable.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.ComponentModel.Annotations/5.0.0": {"sha512": "dMkqfy2el8A8/I76n2Hi1oBFEbG1SfxD2l5nhwXV3XjlnOmwxJlQbYpJH4W51odnU9sARCSAgv7S3CyAFMkpYg==", "type": "package", "path": "system.componentmodel.annotations/5.0.0", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/MonoAndroid10/_._", "lib/MonoTouch10/_._", "lib/net45/_._", "lib/net461/System.ComponentModel.Annotations.dll", "lib/netcore50/System.ComponentModel.Annotations.dll", "lib/netstandard1.4/System.ComponentModel.Annotations.dll", "lib/netstandard2.0/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.dll", "lib/netstandard2.1/System.ComponentModel.Annotations.xml", "lib/portable-net45+win8/_._", "lib/win8/_._", "lib/xamarinios10/_._", "lib/xamarinmac20/_._", "lib/xamarintvos10/_._", "lib/xamarinwatchos10/_._", "ref/MonoAndroid10/_._", "ref/MonoTouch10/_._", "ref/net45/_._", "ref/net461/System.ComponentModel.Annotations.dll", "ref/net461/System.ComponentModel.Annotations.xml", "ref/netcore50/System.ComponentModel.Annotations.dll", "ref/netcore50/System.ComponentModel.Annotations.xml", "ref/netcore50/de/System.ComponentModel.Annotations.xml", "ref/netcore50/es/System.ComponentModel.Annotations.xml", "ref/netcore50/fr/System.ComponentModel.Annotations.xml", "ref/netcore50/it/System.ComponentModel.Annotations.xml", "ref/netcore50/ja/System.ComponentModel.Annotations.xml", "ref/netcore50/ko/System.ComponentModel.Annotations.xml", "ref/netcore50/ru/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hans/System.ComponentModel.Annotations.xml", "ref/netcore50/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/System.ComponentModel.Annotations.dll", "ref/netstandard1.1/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.1/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/System.ComponentModel.Annotations.dll", "ref/netstandard1.3/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.3/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/System.ComponentModel.Annotations.dll", "ref/netstandard1.4/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/de/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/es/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/fr/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/it/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ja/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ko/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/ru/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hans/System.ComponentModel.Annotations.xml", "ref/netstandard1.4/zh-hant/System.ComponentModel.Annotations.xml", "ref/netstandard2.0/System.ComponentModel.Annotations.dll", "ref/netstandard2.0/System.ComponentModel.Annotations.xml", "ref/netstandard2.1/System.ComponentModel.Annotations.dll", "ref/netstandard2.1/System.ComponentModel.Annotations.xml", "ref/portable-net45+win8/_._", "ref/win8/_._", "ref/xamarinios10/_._", "ref/xamarinmac20/_._", "ref/xamarintvos10/_._", "ref/xamarinwatchos10/_._", "system.componentmodel.annotations.5.0.0.nupkg.sha512", "system.componentmodel.annotations.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Diagnostics.DiagnosticSource/5.0.1": {"sha512": "uXQEYqav2V3zP6OwkOKtLv+qIi6z3m1hsGyKwXX7ZA7htT4shoVccGxnJ9kVRFPNAsi1ArZTq2oh7WOto6GbkQ==", "type": "package", "path": "system.diagnostics.diagnosticsource/5.0.1", "files": [".nupkg.metadata", ".signature.p7s", "Icon.png", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/net45/System.Diagnostics.DiagnosticSource.dll", "lib/net45/System.Diagnostics.DiagnosticSource.xml", "lib/net46/System.Diagnostics.DiagnosticSource.dll", "lib/net46/System.Diagnostics.DiagnosticSource.xml", "lib/net5.0/System.Diagnostics.DiagnosticSource.dll", "lib/net5.0/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.1/System.Diagnostics.DiagnosticSource.xml", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.dll", "lib/netstandard1.3/System.Diagnostics.DiagnosticSource.xml", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.dll", "lib/portable-net45+win8+wpa81/System.Diagnostics.DiagnosticSource.xml", "system.diagnostics.diagnosticsource.5.0.1.nupkg.sha512", "system.diagnostics.diagnosticsource.nuspec", "useSharedDesignerContext.txt", "version.txt"]}, "System.Runtime.CompilerServices.Unsafe/4.6.0": {"sha512": "HxozeSlipUK7dAroTYwIcGwKDeOVpQnJlpVaOkBz7CM4TsE5b/tKlQBZecTjh6FzcSbxndYaxxpsBMz+wMJeyw==", "type": "package", "path": "system.runtime.compilerservices.unsafe/4.6.0", "files": [".nupkg.metadata", ".signature.p7s", "LICENSE.TXT", "THIRD-PARTY-NOTICES.TXT", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netcoreapp2.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "lib/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard1.0/System.Runtime.CompilerServices.Unsafe.xml", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.dll", "ref/netstandard2.0/System.Runtime.CompilerServices.Unsafe.xml", "system.runtime.compilerservices.unsafe.4.6.0.nupkg.sha512", "system.runtime.compilerservices.unsafe.nuspec", "useSharedDesignerContext.txt", "version.txt"]}}, "projectFileDependencyGroups": {".NETCoreApp,Version=v3.1": ["Microsoft.EntityFrameworkCore.Design >= 5.0.16", "Microsoft.EntityFrameworkCore.Tools >= 5.0.16", "Microsoft.NETCore.App >= 2.2.8", "Npgsql.EntityFrameworkCore.PostgreSQL >= 5.0.10", "Npgsql.EntityFrameworkCore.PostgreSQL.Design >= 1.1.0", "Swashbuckle.AspNetCore >= 6.3.1", "Swashbuckle.AspNetCore.Swagger >= 6.3.1", "Swashbuckle.AspNetCore.SwaggerUI >= 6.3.1"]}, "packageFolders": {"C:\\Users\\<USER>\\.nuget\\packages\\": {}}, "project": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\Oberon.DataLogic.csproj", "projectName": "Oberon.DataLogic", "projectPath": "C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\Oberon.DataLogic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.16, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.16, )"}, "Microsoft.NETCore.App": {"target": "Package", "version": "[2.2.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[5.0.10, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design": {"target": "Package", "version": "[1.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.Swagger": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[6.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.213\\RuntimeIdentifierGraph.json"}}}}