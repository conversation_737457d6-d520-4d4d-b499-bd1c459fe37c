<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>MSDeploy</WebPublishMethod>
    <IsLinux>true</IsLinux>
    <ResourceId>/subscriptions/6aac0474-9179-493a-bba4-dfc4752a4fc9/resourceGroups/oberondev/providers/Microsoft.Web/sites/oberondev</ResourceId>
    <ResourceGroup>oberondev</ResourceGroup>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <SiteUrlToLaunchAfterPublish>http://oberondev.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <SCMUrl>https://oberondev.scm.azurewebsites.net/</SCMUrl>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <ExcludeApp_Data>False</ExcludeApp_Data>
    <ProjectGuid>bd5d924f-843a-4ff1-b42f-ced41de6550a</ProjectGuid>
    <MSDeployServiceURL>waws-prod-dm1-209.publish.azurewebsites.windows.net:443</MSDeployServiceURL>
    <DeployIisAppPath>oberondev</DeployIisAppPath>
    <RemoteSitePhysicalPath />
    <SkipExtraFilesOnServer>True</SkipExtraFilesOnServer>
    <MSDeployPublishMethod>WMSVC</MSDeployPublishMethod>
    <EnableMSDeployBackup>True</EnableMSDeployBackup>
    <UserName>$oberondev</UserName>
    <_SavePWD>True</_SavePWD>
    <_DestinationType>AzureWebSite</_DestinationType>
  </PropertyGroup>
</Project>