﻿using System;
using System.Collections.Generic;
using System.Text;

namespace Oberon.BusinessLogic.Model
{
    public class FirebaseResponse
    {
        public string Message { get; set; }
        public object UserDetail { get; set; }
        public string Token { get; set; }
        public string RefreshToken { get; set; }
        public string Uid { get; set; }
        public bool Status { get; set; }
    }

    public class FirebaseError
    {
        public Error error { get; set; }
    }

    public class Error
    {
        public int code { get; set; }
        public string message { get; set; }
        public List<Error> errors { get; set; }
    }
}
