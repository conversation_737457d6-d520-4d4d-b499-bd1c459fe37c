﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oberon.BusinessLogic.BusinessHelper;
using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Oberon.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class FeedBackController : ControllerBase
    {
        private readonly UserDBContext _context;
        UserBusinessHelper _userBusinessHelper;
        public FeedBackController(UserDBContext context)
        {
            _context = context;
            _userBusinessHelper = new UserBusinessHelper();
        }

        [HttpPost]
        [Route("update")]
        public async Task<IActionResult> Update(FeedBack model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var datainfo = _context.UserFeedBack
                .Where(feedback => feedback.UserId == model.UserId && feedback.Date == model.Date)
                .FirstOrDefault();

                if (datainfo == null)
                {
                    UserFeedBack userFeedBack = new UserFeedBack()
                    {
                        UserId = model.UserId,
                        Date = model.Date,
                        Level = model.Level,
                        Symptoms = model.Symptoms,
                        Pollen = model.Pollen,
                        CreatedDate = DateTime.Now,
                        UpdatedDate = DateTime.Now
                    };
                    _context.Add(userFeedBack);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);

                }
                else
                {
                    datainfo.Date = model.Date;
                    datainfo.Level = model.Level;
                    datainfo.Symptoms = model.Symptoms;
                    datainfo.UpdatedDate = DateTime.Now;
                    datainfo.Pollen = model.Pollen;
                    _context.Update(datainfo);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }

        #region FeedBack List
        [HttpPost]
        [Route("list")]
        public async Task<IActionResult> UsersList(FeedBackList feedBackList)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<UserFeedBack> userlistResponse = new List<UserFeedBack>();
            try
            {

                userlistResponse = await _context.UserFeedBack.ToListAsync();

                var response = (from s in userlistResponse
                                where (s.UserId == feedBackList.UserId && s.Date.Year == feedBackList.TargetYear && s.Date.Month == feedBackList.TargetMonth)
                                orderby s.Date ascending
                                select s).ToList();

                if (response.Count == 0 || response == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    responseData.Data = response;
                    responseData.TotalCount = response.Count;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region FeedBack By Id
        //[Authorize]
        //[HttpGet]
        //[Route("feedbackid")]
        //public IActionResult GetUser(int id)
        //{
        //    IActionResult result;
        //    ResponseData responseData = new ResponseData();

        //    try
        //    {
        //        var feedBack = _context.Find<UserFeedBack>(id);
        //        if (User == null)
        //        {
        //            responseData.Status = false;
        //            responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
        //            responseData.Data = User;
        //            result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
        //        }
        //        else
        //        {
        //            responseData.Status = true;
        //            responseData.Message = ResponseMessageHelper.LIST.GetDescription();
        //            var profilepath = (from f in _context.UserFeedBack
        //                               where f.UserId == feedBack.Id
        //                               select f).FirstOrDefault();
        //            var output = new
        //            {
        //                userId = feedBack.Id,
        //                name = feedBack.Name,
        //                email = feedBack.Email,
        //                firstName = feedBack.FirstName,
        //                yearofBirth = feedBack.YearofBirth,
        //                gender = feedBack.Gender,
        //                isSmoking = feedBack.IsSmoking,
        //                isNewsfromApp = feedBack.IsNewsFromApp,
        //                isTermsandconditions = feedBack.IstermAndConditions,
        //                createdby = feedBack.createdBy,
        //                updatedby = feedBack.UpdatedBy,
        //                createdDate = feedBack.Createddate,
        //                updatedDate = feedBack.UpdatedDate,
        //                zipCode = feedBack.ZipCode,
        //                resProblemId = (feedBack.ResProblemId == "" || feedBack.ResProblemId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : feedBack.ResProblemId.Split(',').Select(Int32.Parse).ToList(),
        //                pollenId = (feedBack.PollenId == "" || feedBack.PollenId == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : feedBack.PollenId.Split(',').Select(Int32.Parse).ToList(),
        //                NotificationFor = (feedBack.NotificationFor == "" || feedBack.NotificationFor == null) ? ("0").Split(',').Select(Int32.Parse).ToList() : feedBack.NotificationFor.Split(',').Select(Int32.Parse).ToList(),
        //                refreshtoken = ""
        //            };
        //            responseData.Data = output;
        //            result = StatusCode((int)HttpStatusCode.OK, responseData);
        //        }

        //    }
        //    catch (Exception)
        //    {
        //        responseData.Status = false;
        //        responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
        //        result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
        //    }
        //    return result;
        //}
        #endregion
    }
}
