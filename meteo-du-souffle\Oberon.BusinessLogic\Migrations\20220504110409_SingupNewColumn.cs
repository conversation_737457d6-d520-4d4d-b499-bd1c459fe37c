﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class SingupNewColumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<bool>(
                name: "Is<PERSON>ews<PERSON>romApp",
                table: "Signup",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IstermAndConditions",
                table: "Signup",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<long>(
                name: "ZipCode",
                table: "Signup",
                type: "bigint",
                nullable: false,
                defaultValue: 0L);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsNews<PERSON>romApp",
                table: "Signup");

            migrationBuilder.DropColumn(
                name: "IstermAndConditions",
                table: "Signup");

            migrationBuilder.DropColumn(
                name: "<PERSON>ipCode",
                table: "Signup");
        }
    }
}
