﻿using Azure.Storage.Blobs;
using Microsoft.AspNetCore.Hosting;
using Microsoft.WindowsAzure.Storage;
using Microsoft.WindowsAzure.Storage.Blob;
using System;
using System.IO;
using System.Threading.Tasks;


namespace Oberon.API.Helper
{
    public class AzureBlobHelper
    {
        private readonly string blobConnectionString;
        public AzureBlobHelper(string connectionString)
        {
            blobConnectionString = connectionString;
        }

        public async Task<string> UploadFileAsBlob(Stream FileStream, string FileName, string ContainerName, string fileMimeType)
        {
            string uri = string.Empty;
            try
            {
                CloudStorageAccount storageacc = CloudStorageAccount.Parse(blobConnectionString);
                CloudBlobClient blobClient = storageacc.CreateCloudBlobClient();
                CloudBlobContainer container = blobClient.GetContainerReference(ContainerName);

                var returnCont = await container.CreateIfNotExistsAsync();

                if (returnCont)
                {
                    await container.SetPermissionsAsync(new BlobContainerPermissions { PublicAccess = BlobContainerPublicAccessType.Blob });
                }

                CloudBlockBlob blockBlobFile = container.GetBlockBlobReference(FileName);
                blockBlobFile.Properties.ContentType = fileMimeType;

                if (!await blockBlobFile.ExistsAsync())
                {
                    await blockBlobFile.UploadFromStreamAsync(FileStream);
                    FileStream.Dispose();
                    uri = blockBlobFile?.Uri.ToString();
                }
                else
                {
                    new Exception("File already exist!");
                }
            }
            catch (Exception)
            {
                uri = string.Empty;
            }
            return uri;
        }

        public async Task<bool> DeleteFileAsync(string fileName, string ContainerName)
        {
            bool status = false;
            if (!string.IsNullOrEmpty(fileName))
            {
                try
                {
                    CloudStorageAccount storageacc = CloudStorageAccount.Parse(blobConnectionString);
                    CloudBlobClient blobClient = storageacc.CreateCloudBlobClient();
                    CloudBlobContainer container = blobClient.GetContainerReference(ContainerName);
                    CloudBlockBlob blockBlob = container.GetBlockBlobReference(fileName);

                    await blockBlob.DeleteAsync();
                    status = true;
                }
                catch (Exception)
                {
                    status = false;
                }
            }

            return status;
        }
    }
}
