﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class PollenandpollutionNewcolumn : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "FileName",
                table: "PollutionMessage",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsFile",
                table: "PollutionMessage",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<string>(
                name: "FileName",
                table: "PollenMessage",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<bool>(
                name: "IsFile",
                table: "PollenMessage",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "<PERSON>N<PERSON>",
                table: "PollutionMessage");

            migrationBuilder.DropColumn(
                name: "IsFile",
                table: "PollutionMessage");

            migrationBuilder.DropColumn(
                name: "FileName",
                table: "PollenMessage");

            migrationBuilder.DropColumn(
                name: "IsFile",
                table: "PollenMessage");
        }
    }
}
