{"format": 1, "restore": {"C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\Oberon.DataLogic.csproj": {}}, "projects": {"C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\Oberon.DataLogic.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\Oberon.DataLogic.csproj", "projectName": "Oberon.DataLogic", "projectPath": "C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\Oberon.DataLogic.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "C:\\Kiruthika\\Projects\\Oberon\\backend\\meteo-du-souffle\\Oberon.DataLogic\\obj\\", "projectStyle": "PackageReference", "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["netcoreapp3.1"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}}, "frameworks": {"netcoreapp3.1": {"targetAlias": "netcoreapp3.1", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.16, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[5.0.16, )"}, "Microsoft.NETCore.App": {"target": "Package", "version": "[2.2.8, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL": {"target": "Package", "version": "[5.0.10, )"}, "Npgsql.EntityFrameworkCore.PostgreSQL.Design": {"target": "Package", "version": "[1.1.0, )"}, "Swashbuckle.AspNetCore": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.Swagger": {"target": "Package", "version": "[6.3.1, )"}, "Swashbuckle.AspNetCore.SwaggerUI": {"target": "Package", "version": "[6.3.1, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\5.0.213\\RuntimeIdentifierGraph.json"}}}}}