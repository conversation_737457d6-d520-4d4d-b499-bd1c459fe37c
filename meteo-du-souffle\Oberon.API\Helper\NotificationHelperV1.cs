using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Oberon.BusinessLogic.DBContext;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace Oberon.API.Helper
{
    public class NotificationHelperV1
    {
        private const int MAX_FCM_RECIPIENTS = 500; // FCM recommends sending to batches of up to 500 tokens

        public async Task<bool> SendNotificationV1(List<User> users, string title, string body)
        {
            try
            {
                // Initialize Firebase App only once
                if (FirebaseApp.DefaultInstance == null)
                {
                    FirebaseApp.Create(new AppOptions()
                    {
                        Credential = GoogleCredential.FromFile("meteo-du-souffle-firebase-adminsdk-e5xql-2c21b70c21.json")
                    });
                }

                // Filter out users without a device ID
                var validDeviceTokens = users
                                        .Where(u => !string.IsNullOrEmpty(u.DeciveId))
                                        .Select(u => u.DeciveId)
                                        .ToList();

                if (!validDeviceTokens.Any())
                {
                    Console.WriteLine("No valid device tokens found to send notifications.");
                    return false;
                }

                List<Task<BatchResponse>> sendTasks = new List<Task<BatchResponse>>();

                // --- CUSTOM CHUNKING FOR .NET CORE 3.1 ---
                for (int i = 0; i < validDeviceTokens.Count; i += MAX_FCM_RECIPIENTS)
                {
                    List<string> currentBatch = validDeviceTokens
                                                    .Skip(i)
                                                    .Take(MAX_FCM_RECIPIENTS)
                                                    .ToList();

                    var multicastMessage = new MulticastMessage()
                    {
                        Tokens = currentBatch,
                        Notification = new FirebaseAdmin.Messaging.Notification()
                        {
                            Title = title,
                            Body = body
                        },
                        Data = new Dictionary<string, string>()
                        {
                            { "commonData", "someValue" } // Adjust if data needs to be dynamic per user
                        }
                    };
                    sendTasks.Add(FirebaseMessaging.DefaultInstance.SendMulticastAsync(multicastMessage));
                }
                // ------------------------------------------

                // Wait for all batch send operations to complete
                var responses = await Task.WhenAll(sendTasks);

                // Process the results of the batch sends
                foreach (var response in responses)
                {
                    if (response.FailureCount > 0)
                    {
                        foreach (var sendResponse in response.Responses)
                        {
                            if (!sendResponse.IsSuccess)
                            {
                                // Log or handle failed messages for individual tokens
                                Console.WriteLine($"Failed to send notification: {sendResponse.Exception?.Message}");

                                // IMPORTANT: Handle invalid tokens
                                if (sendResponse.Exception is FirebaseMessagingException messagingException)
                                {
                                    // Common error codes indicating token invalidation
                                    if (messagingException.MessagingErrorCode.HasValue &&
                                        (messagingException.MessagingErrorCode == MessagingErrorCode.Unregistered ||
                                         messagingException.MessagingErrorCode == MessagingErrorCode.InvalidArgument)) // InvalidArgument can sometimes mean malformed token
                                    {
                                        // This is where you'd remove the invalid token from your database
                                        Console.WriteLine($"Token {sendResponse.MessageId} (or associated token if you can map it back) is invalid. Removing from DB.");
                                        // You'd need to add logic here to identify *which* user/device this token belonged to
                                        // and then update your database to remove/clear that device ID.
                                        // This is why it's beneficial to store the token along with the user ID when you receive it from the app.
                                    }
                                }
                            }
                        }
                    }
                }

                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Firebase Notification Error: {ex.Message}");
                // Log the full exception details in a real application
                return false;
            }
        }
    }
}