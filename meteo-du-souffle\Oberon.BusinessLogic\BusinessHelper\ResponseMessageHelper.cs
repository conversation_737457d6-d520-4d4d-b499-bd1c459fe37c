﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Text;

namespace Oberon.BusinessLogic.BusinessHelper
{
    public enum ResponseMessageHelper
    {
        //[Description("No data found.")]
        [Description("Aucune donnée disponible.")]
        DATANOTFOUND,

        //[Description("Something went wrong, Please try again later.")]
        [Description("<PERSON><PERSON><PERSON> chose s'est mal passé. Merci d'essayer plus tard.")]
        MISTAKE,

        //[Description("Login successfully.")]
        [Description("Connexion réussie.")]
        LOGIN,

        //[Description("Registration successfully, Email verification link sent to your emailID.")]
        [Description("Inscription réussie, un lien de vérification vous a été envoyé par e-mail.")]
        REGISTRATION,

        //[Description("Please enter validate emailId.")]
        [Description("Veuillez entrer une adresse e-mail valide.")]
        VALIDATEEMAIL,

        //[Description("Please enter validate emailId or password.")]
        [Description("Veuillez entrer une adresse e-mail ou un mot de passe valide.")]
        VALIDATEEMAILANDPASSWORD,

        //[Description("Invaild email.")]
        [Description("Vérifiez votre adresse e-mail.")]
        INVAILDEMAIL,

        //[Description("EmailId already exists.")]
        [Description("Cette adresse e-mail existe déjà.")]
        EMAILEXISTS,

        //[Description("Please enter validate password.")]
        [Description("Veuillez entrer une adresse e-mail valide.")]
        VALIDATEPASSWORD,

        //[Description("You didn't have access to login, Only admin can login.")]
        [Description("Cette connexion est réservée à l'administrateur. ")]
        ADMINVALIDATE,

        //[Description("Get list successfully.")]
        [Description("La liste a été fournie avec succès.")]
        LIST,

        //[Description("Update successfully.")]
        [Description("La mise à jour a bien été effectuée.")]
        UPDATE,

        //[Description("Delete successfully.")]
        [Description("Supprimé avec succès.")]
        DELETE,

        //[Description("Added successfully.")]
        [Description("L'ajout a bien été effectué.")]
        ADDED,

        //[Description("Advices not exists, Please check advicesId.")]
        [Description("Pas de conseil disponible.")]
        ADVICENOTEXISTS,

        //[Description("Get advice details successfully.")]
        [Description("Le détail des conseils a été fourni avec succès.")]
        ADVICEDETAILS,

        //[Description("User not found.")]
        [Description("Utilisateur inconnu.")]
        USERDETAILSNOTFOUND,

        //[Description("Please verfiy your email id.")]
        [Description("Veuillez vérifier votre identifiant de messagerie.")]
        VERIFIYEMAIL,

        //[Description("Reset password link sent to your registered emailId.")]
        [Description("Le lien de réinitialisation de votre mot de passe a été envoyé à votre adresse e-mail.")]
        RESETPASSWORD,

        //[Description("Notification sent successfully.")]
        [Description("La notification a bien été envoyée.")]
        NOTIFICATIONSUCCESS,

        //[Description("Notification not sent, Something went wrong.")]
        [Description("Notification non envoyée, une erreur s'est produite.")]
        NOTIFICATIONERROR,

        //[Description("Profile picture upload successfully.")]
        [Description("Photo de profil ajoutée avec succès.")]
        PROFILEUPLOADSUCCESS,

        //[Description("Get token successfully.")]
        [Description("Récupération du token (clé) réussie.")]
        NEWTOKEN,

        //[Description("GPS zipcode updated successfully.")]
        [Description("Code postal GPS mis à jour avec succès.")]
        GPSZIPCODE,

        //[Description("Pollen list not exists.")]
        [Description("La liste de pollen n'existe pas.")]
        POLLENNOTEXISTS,

        //[Description("Get pollen list successfully.")]
        [Description("Récupération de la liste des pollens réussie.")]
        POLLENLIST,

        //[Description("Pollution list not exists.")]
        [Description("La liste de polluants n'existe pas.")]
        POLLUTIONNOTEXISTS,

        //[Description("Get Pollution list successfully.")]
        [Description("Récupération de la liste des polluants réussie.")]
        POLLUTIONLIST,

        //[Description("Logout successfully.")]
        [Description("Déconnexion réussie.")]
        LOGOUT,

        //[Description("File uploaded successfully.")]
        [Description("Téléchargement du fichier réussie.")]
        FILEUPLOAD,

        //[Description("File not uploaded successfully.")]
        [Description("Echec du téléchargement du fichier.")]
        FILENOTUPLOAD,

        //[Description("File saved successfully.")]
        [Description("Fichier enregistré avec succès.")]
        FILESAVED,

        //[Description("Upload only pdf file format.")]
        [Description("Veuillez ne télécharger que des fichiers PDF.")]
        FILEFORMATE
    }
}
