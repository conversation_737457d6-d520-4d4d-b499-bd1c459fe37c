﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class spellingchanges : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_sponsor",
                table: "sponsor");

            migrationBuilder.RenameTable(
                name: "sponsor",
                newName: "Sponsors");

            migrationBuilder.AddPrimaryKey(
                name: "PK_Sponsors",
                table: "Sponsors",
                column: "Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropPrimaryKey(
                name: "PK_Sponsors",
                table: "Sponsors");

            migrationBuilder.RenameTable(
                name: "Sponsors",
                newName: "sponsor");

            migrationBuilder.AddPrimaryKey(
                name: "PK_sponsor",
                table: "sponsor",
                column: "Id");
        }
    }
}
