﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class addednewcoluminpdffileupload : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "TempUniqueFileName",
                table: "PdfFileUpload",
                type: "text",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UniqueFileName",
                table: "PdfFileUpload",
                type: "text",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "TempUniqueFileName",
                table: "PdfFileUpload");

            migrationBuilder.DropColumn(
                name: "UniqueFileName",
                table: "PdfFileUpload");
        }
    }
}
