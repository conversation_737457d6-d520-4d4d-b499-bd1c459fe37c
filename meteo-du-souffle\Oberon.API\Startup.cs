using CorePush.Apple;
using CorePush.Google;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.StaticFiles;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using Microsoft.IdentityModel.Tokens;
using Microsoft.OpenApi.Models;
using Oberon.API.Handlers;
using Oberon.API.Helper;
using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Model;
using System;
using System.Linq;
using System.Text;
using static Oberon.API.Helper.NotificationHelper;

namespace Oberon.API
{
    public class Startup
    {
        public AzureBlobConnectionHandler azureBlobConnection { get; set; }
        public Startup(IConfiguration configuration)
        {
            Configuration = configuration;
        }

        public IConfiguration Configuration { get; }
        readonly string MyAllowSpecificOrigins = "_myAllowSpecificOrigins";
        public void ConfigureServices(IServiceCollection services)
        {
            services.AddControllers();

            services.AddMvc().SetCompatibilityVersion(CompatibilityVersion.Version_2_1);
            
            services.AddDbContext<UserDBContext>(options => options.UseNpgsql(Configuration.GetConnectionString("DataBaseConnection")));
            
            azureBlobConnection = Configuration.GetSection("AzureConnection").Get<AzureBlobConnectionHandler>();

            var allowedOrigins = Configuration.GetSection("CorsSettings:AllowedOrigins").Get<string[]>();
            services.AddCors(options =>
            {
                options.AddPolicy(MyAllowSpecificOrigins,
                builder =>
                {
                    builder.WithOrigins(allowedOrigins.ToArray()).AllowAnyHeader().AllowAnyMethod();
                });
            });
            
            services.AddAuthentication(JwtBearerDefaults.AuthenticationScheme).AddJwtBearer(options =>
            {
                options.Authority = "https://securetoken.google.com/meteo-du-souffle";
                options.TokenValidationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidIssuer = "https://securetoken.google.com/meteo-du-souffle",
                ValidateAudience = true,
                ValidAudience = "meteo-du-souffle",
                ValidateLifetime = true
            };
        });
           

            services.AddSwaggerGen(options =>
            {
                options.SwaggerDoc("v6", new Microsoft.OpenApi.Models.OpenApiInfo
                {
                    Title = "Oberon API"
                });
                var securityScheme = new OpenApiSecurityScheme
                {
                    Name = "JWT Authentication",
                    Description = "Enter JWT Bearer token **_only_**",
                    In = ParameterLocation.Header,
                    Type = SecuritySchemeType.Http,
                    Scheme = "bearer", // must be lower case
                    BearerFormat = "JWT",
                    Reference = new OpenApiReference
                    {
                        Id = JwtBearerDefaults.AuthenticationScheme,
                        Type = ReferenceType.SecurityScheme
                    }
                };
                options.AddSecurityDefinition(securityScheme.Reference.Id, securityScheme);
                options.AddSecurityRequirement(new OpenApiSecurityRequirement{
                        {securityScheme, new string[] { }}
                    });
            });

            services.AddSingleton<IAzureBlobConnectionHandler>(azureBlobConnection);
        }

        // This method gets called by the runtime. Use this method to configure the HTTP request pipeline.
        public void Configure(IApplicationBuilder app, IWebHostEnvironment env)
        {
            if (env.IsDevelopment())
            {
                app.UseDeveloperExceptionPage();
            }

            var extensionProvider = new FileExtensionContentTypeProvider();
            extensionProvider.Mappings.Add(".webmanifest", "application/manifest+json");

            app.UseDefaultFiles();

            app.UseStaticFiles(new StaticFileOptions
            {
                ContentTypeProvider = extensionProvider
            });

            app.UseHttpsRedirection();
            app.UseRouting();
            app.UseAuthentication();
            app.UseSwagger();
            app.UseSwaggerUI(c => c.SwaggerEndpoint("/swagger/v6/swagger.json", "Oberon Services"));
            app.UseStatusCodePages();
            app.UseAuthorization();

            app.UseEndpoints(endpoints =>
            {
                endpoints.MapControllers();
            });
        }
    }
}
