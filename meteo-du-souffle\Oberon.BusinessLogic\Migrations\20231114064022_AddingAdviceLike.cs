﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class AddingAdviceLike : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "userId",
                table: "AdviceLikes",
                newName: "UserId");

            migrationBuilder.RenameColumn(
                name: "userId",
                table: "AdviceComments",
                newName: "UserId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AdviceLikes",
                newName: "userId");

            migrationBuilder.RenameColumn(
                name: "UserId",
                table: "AdviceComments",
                newName: "userId");
        }
    }
}
