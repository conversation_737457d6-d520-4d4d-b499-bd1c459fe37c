﻿using FirebaseAdmin;
using FirebaseAdmin.Messaging;
using Google.Apis.Auth.OAuth2;
using Oberon.BusinessLogic.DBContext;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace Oberon.API.Helper
{
    public class NotificationHelper
    {
        public async Task<bool> SendNotification(List<User> token, string title, string body)
        {
            try
            {
                if (FirebaseApp.DefaultInstance == null)
                {
                    FirebaseApp.Create(new AppOptions()
                    {
                        Credential = GoogleCredential.FromFile("meteo-du-souffle-firebase-adminsdk-e5xql-2c21b70c21.json")
                    });
                }

                foreach (User ItemInfo in token)
                {
                    try
                    {
                        string zip = Convert.ToString(ItemInfo.GPSZipCode);

                        var message = new Message()
                        {
                            Token = ItemInfo.DeciveId,
                            Notification = new FirebaseAdmin.Messaging.Notification()
                            {
                                Title = title,
                                Body = body
                            },
                            Data = new Dictionary<string, string>()
                            {
                                { "myData", zip }
                            }
                        };

                        // Try sending the notification for each device separately
                        string response = await FirebaseMessaging.DefaultInstance.SendAsync(message);
                    }
                    catch (Exception ex)
                    {
                        // Log error but continue sending to other devices
                        Console.WriteLine($"Failed to send notification to Device ID: {ItemInfo.DeciveId}, Error: {ex.Message}");
                        continue;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Firebase Initialization Error: {ex.Message}");
                return false;
            }
        }
    }
}
