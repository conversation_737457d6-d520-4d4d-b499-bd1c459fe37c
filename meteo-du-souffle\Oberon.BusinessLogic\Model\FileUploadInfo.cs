﻿using Microsoft.AspNetCore.Http;
using System;
using System.Collections.Generic;
using System.Text;

namespace Oberon.BusinessLogic.Model
{
    public class FileUploadInfo
    {
        public int UserId { get; set; }
        public string EmailId { get; set; }
        public string FileName { get; set; }
        public IFormFile File { get; set; }
        public DateTime Createddate { get; set; }
        public DateTime UpdatedDate { get; set; }
    }

    public class PdfFileUploadInfo
    {
        public int PdfFileNameId { get; set; }
        public string FileName { get; set; }
        public IFormFile File { get; set; }
    }
    public class PdfFileSaveInfo
    {
        public bool IsAllergies { get; set; }
        public bool IsTermAndCon { get; set; }
        public bool IsInfor { get; set; }
        public bool IsPatho { get; set; }
        public bool IsConseilsInformation { get; set; }
        public bool IsPolitiqueDeConfidentialite { get; set; }
    }
    public class PdfFileInfo
    {
        public int Id { get; set; }
        public string PdfFileName { get; set; }
        public string FileName { get; set; }
        public string UniqueFileName { get; set; }
        public string FilePath { get; set; }
    }

    public class PollenPollutionFileUploadInfo
    {
        public string Name { get; set; }
        public string FileName { get; set; }
        public IFormFile File { get; set; }
    }
    public class SplashImageRequest
    {
        public int Id { get; set; }
        public string FileName { get; set; }
        public IFormFile File { get; set; }
    }
    public class SplashImageInfo
    {
        public int Id { get; set; }
        public string FilePath { get; set; }

    }
}
