<?xml version="1.0" encoding="utf-8"?>
<!--
This file is used by the publish/package process of your Web project. You can customize the behavior of this process
by editing this MSBuild file. In order to learn more about this please visit https://go.microsoft.com/fwlink/?LinkID=208121. 
-->
<Project ToolsVersion="4.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <WebPublishMethod>ZipDeploy</WebPublishMethod>
    <IsLinux>true</IsLinux>
    <ResourceId>/subscriptions/6aac0474-9179-493a-bba4-dfc4752a4fc9/resourceGroups/oberondev/providers/Microsoft.Web/sites/OberonProd</ResourceId>
    <ResourceGroup>oberondev</ResourceGroup>
    <LaunchSiteAfterPublish>True</LaunchSiteAfterPublish>
    <SiteUrlToLaunchAfterPublish>https://oberonprod.azurewebsites.net</SiteUrlToLaunchAfterPublish>
    <PublishProvider>AzureWebSite</PublishProvider>
    <LastUsedBuildConfiguration>Release</LastUsedBuildConfiguration>
    <LastUsedPlatform>Any CPU</LastUsedPlatform>
    <ProjectGuid>bd5d924f-843a-4ff1-b42f-ced41de6550a</ProjectGuid>
    <PublishUrl>https://oberonprod.scm.azurewebsites.net/</PublishUrl>
    <UserName>$OberonProd</UserName>
    <_SavePWD>True</_SavePWD>
  </PropertyGroup>
</Project>