using System;
using System.Security.Cryptography;
using System.Text;

namespace Oberon.API.Helper
{
    public class EncryptionDecryptionHelper
    {
        private RijndaelManaged myRijndael = new RijndaelManaged();

        public EncryptionDecryptionHelper()
        {
            myRijndael.Padding = PaddingMode.PKCS7;
            myRijndael.Mode = CipherMode.ECB;
            myRijndael.IV = Encoding.ASCII.GetBytes("9/()V).A,lY&=t2b");
            myRijndael.Key = Encoding.ASCII.GetBytes("esa{@^h`h&_`50/ja9!dcmh3!#uw<&=?");
        }

        public string Encrypt(string strPlainText)
        {
            byte[] strText = new System.Text.UTF8Encoding().GetBytes(strPlainText);
            ICryptoTransform transform = myRijndael.CreateEncryptor();
            byte[] cipherText = transform.TransformFinalBlock(strText, 0, strText.Length);

            return Convert.ToBase64String(cipherText);
        }
        public string Decrypt(string encryptedText)
        {
            byte[] encryptedBytes = Convert.FromBase64String(encryptedText);
            var decryptor = myRijndael.CreateDecryptor(myRijndael.Key, myRijndael.IV);
            byte[] originalBytes = decryptor.TransformFinalBlock(encryptedBytes, 0, encryptedBytes.Length);

            return Encoding.UTF8.GetString(originalBytes);
        }
    }
}
