﻿using Azure;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oberon.API.Handlers;
using Oberon.API.Helper;
using Oberon.BusinessLogic.BusinessHelper;
using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Migrations;
using Oberon.BusinessLogic.Model;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Threading.Tasks;
using static Google.Apis.Requests.BatchRequest;

namespace Oberon.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AdvicesController : ControllerBase
    {
        private readonly UserDBContext _context;
        UserBusinessHelper _userBusinessHelper;
        NotificationHelperV1 _notificationHelper;

        public AdvicesController(UserDBContext context)
        {
            _context = context;
            _userBusinessHelper = new UserBusinessHelper();
            _notificationHelper = new NotificationHelperV1();
        }

        #region Create Sponsor API
        ////[Authorize]
        [HttpPost]
        [Route("sponsor/update")]
        public async Task<IActionResult> SponsorUpdate(SponsorModel model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<User> UserResponse = new List<User>();
            try
            {
                if (model.Id == 0)
                {
                    Sponsor sponsor = new Sponsor()
                    {
                        Content = model.Content,
                        Link = model.Link,
                        CreatedDate = DateTime.Now
                    };

                    _context.Add(sponsor);
                    _context.SaveChanges();

                    UserResponse = await _context.Signup
                                                .Where(u => !string.IsNullOrEmpty(u.DeciveId) && u.isAndriod == true)
                                                .OrderByDescending(u => u.Id)
                                                .ToListAsync();


                    if (UserResponse.Count() == 0 || UserResponse == null)
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                        result = StatusCode((int)HttpStatusCode.OK, responseData);
                    }
                    else
                    {
                        #region Push Notification
                        var notificationresult = _notificationHelper.SendNotificationV1(UserResponse, model.Content, model.Link).Result;
                        #endregion
                    }

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    var response = await _context.FindAsync<Sponsor>(model.Id); //Changes response to sponsorResponse due to conflict.

                    if (response == null)
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                        result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                    }
                    else
                    {
                        response.Content = model.Content;
                        response.Link = model.Link;
                        response.UpdatedDate = DateTime.Now;

                        _context.Update(response);
                        _context.SaveChanges();

                        UserResponse = await _context.Signup
                                                    .Where(u => !string.IsNullOrEmpty(u.DeciveId)
                                                                && u.isAndriod == true
                                                                && u.Createddate.Year == 2025)
                                                                //&& (u.Createddate.Month >= 3 && u.Createddate.Month <= 5))
                                                    .OrderByDescending(u => u.Id)
                                                    .Take(50) // Added to take the last 50
                                                    .ToListAsync();

                        if (UserResponse.Count() == 0 || UserResponse == null)
                        {
                            responseData.Status = false;
                            responseData.Message = ResponseMessageHelper.USERDETAILSNOTFOUND.GetDescription();
                            result = StatusCode((int)HttpStatusCode.OK, responseData);
                        }
                        else
                        {
                            #region Push Notification
                            var notificationresult = await _notificationHelper.SendNotificationV1(UserResponse, model.Content, model.Link);
                            #endregion
                        }


                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                        result = StatusCode((int)HttpStatusCode.OK, responseData);
                    }
                }
            }
            catch
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Create Advices API
        ////[Authorize]
        [HttpPost]
        [Route("update")]
        public async Task<IActionResult> Update(AdvicesModel model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                if (model.Id == 0)
                {
                    model.HistoryDate = DateTime.Now;
                    Advices advices = new Advices()
                    {
                        Zipcode = model.Zipcode.ToString(),
                        Advice = model.Advice,
                        AdviceType = (int)model.AdviceType,
                        AdviceHistoryDate = model.HistoryDate,
                        CreatedDate = DateTime.Now,
                        CreatedBy = model.CreatedBy
                    };
                    _context.Add(advices);
                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    var response = await _context.FindAsync<Advices>(model.Id);

                    if (response == null)
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                        result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                    }
                    else
                    {
                        model.HistoryDate = DateTime.Now;
                        response.Zipcode = model.Zipcode.ToString();
                        response.Advice = model.Advice;
                        response.AdviceType = (int)model.AdviceType;
                        response.AdviceHistoryDate = model.HistoryDate;
                        response.UpdatedDate = DateTime.Now;
                        response.UpdatedBy = model.CreatedBy;

                        _context.Update(response);
                        _context.SaveChanges();

                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                        result = StatusCode((int)HttpStatusCode.OK, responseData);
                    }
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Delete Advices API
        ////[Authorize]
        [HttpDelete]
        [Route("delete/{id}")]
        public async Task<IActionResult> Delete(int id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                var response = await _context.Advices.Where(x => x.Id == id).FirstOrDefaultAsync();
                var advicelikeRes = await _context.AdviceLikes.Where(x => x.AdviceId == id).FirstOrDefaultAsync();
                var adviceCommentRes = await _context.AdviceComments.Where(x => x.AdviceId == id).FirstOrDefaultAsync();
                if (response != null)
                {
                    // delete advice
                    _context.Advices.Remove(response);

                    // Delete likes
                    if (advicelikeRes != null)
                    {
                        _context.AdviceLikes.Remove(advicelikeRes);
                    }

                    // Delete Comments
                    if (adviceCommentRes != null)
                    {
                        _context.AdviceComments.Remove(adviceCommentRes);
                    }
                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.DELETE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Advices List API
        //[Authorize]
        [HttpPost]
        [Route("advicelist")]
        public async Task<IActionResult> AdviceList(AdviceList model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<Advices> advicelistResponse = new List<Advices>();
            try
            {
                advicelistResponse = await _context.Advices.ToListAsync();

                var response = (from s in advicelistResponse
                                where (model.zipcode != "" ? s.Zipcode.ToLower().Trim().Contains(model.zipcode.ToLower().Trim()) : s.Zipcode != "") &&
                                (model.AdviceType != 0 ? s.AdviceType == model.AdviceType : s.AdviceType != 0) &&
                                (model.Like == 0 || s.LikesCount == model.Like) && //(model.Like != 0 ? s.LikesCount == model.Like : s.LikesCount != 0) &&
                                (model.Advice != "" ? s.Advice.ToLower().Trim().Contains(model.Advice.ToLower().Trim()) : s.Advice != "")
                                select s).ToList();

                if (model.ModifiedDate != null)
                {
                    var date = Convert.ToDateTime(model.ModifiedDate);
                    response = response.Where(a => a.CreatedDate.Date == date.Date).ToList();
                }

                if (response.Count == 0 || response == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    List<AdvicesResponse> dataList = new List<AdvicesResponse>();
                    model.pageNumber = model.pageNumber == 0 ? 1 : model.pageNumber;
                    model.pageSize = model.pageSize == 0 ? 5 : model.pageSize;
                    var data = _userBusinessHelper.AdviceResponseSortHelper(model.sortBy, response);

                    var userlist = data.Skip((model.pageNumber - 1) * model.pageSize).Take(model.pageSize).ToList();
                    foreach (var item in userlist)
                    {
                        AdvicesResponse advices = new AdvicesResponse();
                        advices.Id = item.Id;
                        advices.Zipcode = item.Zipcode;
                        advices.AdviceType = item.AdviceType;
                        advices.AdviceTypeName = Enum.GetName(typeof(NotificationTypeHelper), item.AdviceType);
                        advices.Advice = item.Advice;
                        advices.LikesCount = item.LikesCount;
                        advices.CreatedDate = item.CreatedDate;
                        //advices.UpdatedDate = item.UpdatedDate;
                        //advices.CreatedBy = item.CreatedBy;
                        //advices.UpdatedBy = item.UpdatedBy;
                        advices.AdviceHistoryDate = item.AdviceHistoryDate;
                        advices.IsSendMessage = item.IsSendMessage;
                        dataList.Add(advices);
                    }
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    responseData.Data = dataList;
                    responseData.TotalCount = response.Count;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
            }
            catch (Exception ex)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Get Advice By Id
        //[Authorize]
        [HttpGet]
        [Route("advicebyid")]
        public IActionResult GetAdvice(int id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var data = _context.Find<Advices>(id);
                if (data == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.ADVICENOTEXISTS.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADVICEDETAILS.GetDescription();
                    var output = new
                    {
                        id = data.Id,
                        zipCode = data.Zipcode,
                        adviceName = data.Advice,
                        AdviceType = data.AdviceType,
                        AdviceTypeName = Enum.GetName(typeof(NotificationTypeHelper), data.AdviceType),
                        historyDate = data.AdviceHistoryDate,
                        createdBy = data.CreatedBy,
                        createdDate = data.CreatedDate,
                        updatedBy = data.UpdatedBy,
                        updatedDate = data.UpdatedDate,
                        issendmessage = data.IsSendMessage,
                    };
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Get AdviceList By ZipCode
        //[Authorize]
        [HttpGet]
        [Route("advicebyzipCode")]
        public IActionResult GetAdviceList(int zipcode, int userId)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            bool userLikedPollution = false;
            bool userLikedPollen = false;
            bool userLikedTips = false;
            try
            {
                Hashtable resultSet = new Hashtable();

                var pollenList = _context.Advices.Where(x => (x.Zipcode.StartsWith(zipcode.ToString().Substring(0, 2))) && x.AdviceType == 1 && x.IsSendMessage == true)
                                               .OrderByDescending(i => i.AdviceHistoryDate).FirstOrDefault();
                //.Take(3)  //// Takes the first 3 items
                //.ToList();
                if (pollenList != null)
                {
                    userLikedPollen = _context.AdviceLikes.Any(x => x.UserId == userId && x.AdviceId == pollenList.Id);
                }

                var pollutionList = _context.Advices.Where(x => (x.Zipcode.StartsWith(zipcode.ToString().Substring(0, 2))) && x.AdviceType == 2 && x.IsSendMessage == true)
                                               .OrderByDescending(i => i.AdviceHistoryDate).FirstOrDefault();
                //.Take(3)  //// Takes the first 3 items
                //.ToList();
                if (pollutionList != null)
                {
                    userLikedPollution = _context.AdviceLikes.Any(x => x.UserId == userId && x.AdviceId == pollutionList.Id);
                }

                var tips = _context.Advices.Where(x => (x.Zipcode.StartsWith(zipcode.ToString().Substring(0, 2))) && x.AdviceType == 3 && x.IsSendMessage == true)
                                           .OrderByDescending(i => i.AdviceHistoryDate).FirstOrDefault();

                //.Take(3)  //// Takes the first 3 items
                //.ToList();
                if (tips != null)
                {
                    userLikedTips = _context.AdviceLikes.Any(x => x.UserId == userId && x.AdviceId == tips.Id);
                }


                resultSet.Add("pollenList", new { Advice = pollenList, UserLiked = userLikedPollen });
                resultSet.Add("pollutionList", new { Advice = pollutionList, UserLiked = userLikedPollution });
                resultSet.Add("tipsList", new { Advice = tips, UserLiked = userLikedTips });


                if (resultSet == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Data = resultSet;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Like Update
        ////[Authorize]
        [HttpPost]
        [Route("likeUpdate")]
        public async Task<IActionResult> LikeUpdate(AdviceLike model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            var advice = _context.Advices.Find(model.AdviceId);
            var liked = _context.AdviceLikes.Where(adviceLike => adviceLike.AdviceId == model.AdviceId && adviceLike.UserId == model.UserId).FirstOrDefault();

            try
            {
                if (liked != null && advice.LikesCount > 0 && model.IsLiked == false)
                {
                    advice.LikesCount--;
                    _context.SaveChanges();

                    _context.AdviceLikes.Remove(liked);
                    _context.SaveChanges();
                }
                if (advice != null && liked == null && model.IsLiked == true)
                {
                    AdviceLikes adviceLikes = new AdviceLikes()
                    {
                        AdviceId = model.AdviceId,
                        UserId = model.UserId,
                    };
                    _context.Add(adviceLikes);
                    _context.SaveChanges();

                    advice.LikesCount++;
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    var response = _context.AdviceLikes
                    .Where(adviceLike => adviceLike.AdviceId == model.AdviceId && adviceLike.UserId == model.UserId)
                    .FirstOrDefault();

                    if (response == null)
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                        result = StatusCode((int)HttpStatusCode.BadRequest, response);
                    }
                    else
                    {
                        _context.AdviceLikes.Remove(response);
                        _context.SaveChanges();

                        advice.LikesCount--;
                        _context.SaveChanges();

                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                        result = StatusCode((int)HttpStatusCode.OK, response);
                    }
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Comment Update
        ////[Authorize]
        [HttpPost]
        [Route("commentUpdate")]
        public async Task<IActionResult> CommentUpdate(AdviceComment model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                var advice = _context.Advices.Find(model.AdviceId);
                if (advice != null)
                {
                    AdviceComments advices = new AdviceComments()
                    {
                        AdviceId = model.AdviceId,
                        UserId = model.UserId,
                        Comments = model.Comments,
                        CreatedDate = DateTime.Now
                    };
                    _context.Add(advices);
                    _context.SaveChanges();

                    advice.CommentsCount++;
                    _context.SaveChanges();

                    var totalCommentCount = _context.AdviceComments.Count(ac => ac.AdviceId == model.AdviceId);

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    responseData.Data = totalCommentCount;
                }
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch (Exception ex)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region CommentLikeUpdate
        [HttpPost]
        [Route("CommentlikeUpdate")]
        public async Task<IActionResult> CommentLikeUpdate(CommentLike model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            var comments = _context.AdviceComments.Find(model.CommentId);
            var liked = _context.CommentsLikes.Where(commentLike => commentLike.AdviceId == model.AdviceId && commentLike.UserId == model.UserId && commentLike.CommentId == model.CommentId).FirstOrDefault();

            try
            {
                if (liked != null && comments.LikesCount > 0 && model.IsLiked == false)
                {
                    comments.LikesCount--;
                    _context.SaveChanges();

                    _context.CommentsLikes.Remove(liked);
                    _context.SaveChanges();
                }
                if (comments != null && liked == null && model.IsLiked == true)
                {
                    CommentsLikes adviceLikes = new CommentsLikes()
                    {
                        AdviceId = model.AdviceId,
                        UserId = model.UserId,
                        CommentId = model.CommentId,
                    };
                    _context.Add(adviceLikes);
                    _context.SaveChanges();

                    comments.LikesCount++;
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    var response = _context.CommentsLikes
                    .Where(commentLike => commentLike.AdviceId == model.AdviceId && commentLike.UserId == model.UserId && commentLike.CommentId == model.CommentId)
                    .FirstOrDefault();

                    if (response == null)
                    {
                        responseData.Status = false;
                        responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                        result = StatusCode((int)HttpStatusCode.BadRequest, response);
                    }
                    else
                    {
                        _context.CommentsLikes.Remove(response);
                        _context.SaveChanges();

                        comments.LikesCount--;
                        _context.SaveChanges();

                        responseData.Status = true;
                        responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                        result = StatusCode((int)HttpStatusCode.OK, response);
                    }
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion
        #region Advices List API
        //[Authorize]
        [HttpPost]
        [Route("comment/{adviceId}")]
        public async Task<IActionResult> CommentList(int adviceId)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<Advices> advicelistResponse = new List<Advices>();
            try
            {
                //var response = await _context.AdviceComments.Where(x => x.AdviceId == adviceId).OrderByDescending(x => x.CreatedDate).ToListAsync();

                var data = from a in _context.AdviceComments
                           join b in _context.Signup on a.UserId equals b.Id
                           where (a.AdviceId == adviceId)
                           orderby a.CreatedDate descending
                           select new
                           {
                               Id = a.Id,
                               //AdviceId = a.AdviceId,
                               UserId = a.UserId,
                               Username = b.FirstName + " " + b.Name,
                               Comments = a.Comments,
                               LikeCount = a.LikesCount,
                               CreatedDate = a.CreatedDate
                           };

                if (data == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    responseData.Data = data;
                    responseData.TotalCount = data.Count();

                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception ex)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion



        #region
        [HttpGet]
        [Route("sponsorlist")]
        public async Task<IActionResult> SponsorList()
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<Sponsor> sponsors = new List<Sponsor>();
            try
            {
                sponsors = await _context.Sponsors.ToListAsync();

                responseData.Status = true;
                responseData.Message = "";
                responseData.Data = sponsors;
                responseData.TotalCount = sponsors.Count;
                result = StatusCode((int)HttpStatusCode.OK, responseData);
            }
            catch
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region
        [HttpGet]
        [Route("sponsorbyid")]
        public async Task<IActionResult> GetSponsor(int id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            List<Sponsor> sponsors = new List<Sponsor>();
            try
            {
                var data = _context.Find<Sponsor>(id);
                if (data == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = "";
                    var output = new
                    {
                        id = data.Id,
                        Content = data.Content,
                        Link = data.Link
                    };
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
            }
            catch
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Delete Advices API
        [HttpDelete]
        [Route("sponsor/delete/{id}")]
        public async Task<IActionResult> SponsorDelete(int id)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                var sponsors = await _context.Sponsors.Where(x => x.Id == id).FirstOrDefaultAsync();
                if (sponsors != null)
                {
                    // delete advice
                    _context.Sponsors.Remove(sponsors);

                    _context.SaveChanges();
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.DELETE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion
    }
}
