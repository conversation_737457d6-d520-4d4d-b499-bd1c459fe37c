﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Oberon.API.Handlers;
using Oberon.API.Helper;
using Oberon.BusinessLogic.BusinessHelper;
using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Model;
using System;
using System.Linq;
using System.Net;
using System.Threading.Tasks;

namespace Oberon.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class PollenPollutionMessageController : ControllerBase
    {
        private readonly UserDBContext _context;
        private readonly IAzureBlobConnectionHandler _azureBlobConnection;
        static string[] _extentions = new string[] { ".pdf" };

        public PollenPollutionMessageController(UserDBContext context, IAzureBlobConnectionHandler azureBlobConnection)
        {
            _context = context;
            _azureBlobConnection = azureBlobConnection;
        }

        #region Pollen Message
        //[Authorize]
        [HttpGet]
        [Route("updatepollen")]
        public IActionResult Update(string message)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "pdf-file";
                var data = _context.PollenMessage.OrderBy(x => x.Id).LastOrDefault();
                if(data == null)
                {
                    PollenMessage pollenmsg = new PollenMessage()
                    {
                        Message = (message == null || message == "") ? "" : message,
                        CreatedDate = DateTime.Now
                    };
                    _context.Add(pollenmsg);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    if (data.FileName != "" || data.FileName != null)
                    {
                        //Delete file from Azure function call                    
                        var response = blobStorage.DeleteFileAsync(data.FileName, containerName);

                    }
                    data.Message = (message == null || message == "") ? "" : message;
                    data.UpdatedDate = DateTime.Now;
                    data.IsFile = false;
                    data.FileName = "";
                    data.UniqueFileName = "";
                    _context.Update(data);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }             

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Pollution Message
        //[Authorize]
        [HttpGet]
        [Route("updatepollution")]
        public IActionResult UpdatePollution(string message)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "pdf-file";
                var data = _context.PollutionMessage.OrderBy(x => x.Id).LastOrDefault();
                if (data == null)
                {
                    PollutionMessage pollutionmsg = new PollutionMessage()
                    {
                        Message = (message == null || message == "") ? "" : message,
                        CreatedDate = DateTime.Now
                    };
                    _context.Add(pollutionmsg);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.ADDED.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    if (data.FileName != "" || data.FileName != null)
                    {
                        //Delete file from Azure function call                    
                        var response = blobStorage.DeleteFileAsync(data.FileName, containerName);

                    }
                    data.Message = (message == null || message == "") ? "" : message;
                    data.UpdatedDate = DateTime.Now;
                    data.IsFile = false;
                    data.FileName = "";
                    data.UniqueFileName = "";
                    _context.Update(data);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.UPDATE.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Pollen and Pollution List
        //[Authorize]
        [HttpGet]
        [Route("list")]
        public IActionResult GetList()
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                var pollenresponse = _context.PollenMessage.ToList();
                var pollutionresponse = _context.PollutionMessage.ToList();

                if ((pollenresponse.Count == 0 && pollutionresponse.Count == 0) || (pollenresponse == null && pollutionresponse == null))
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.DATANOTFOUND.GetDescription();
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    var output = new
                    {
                        pollenList = pollenresponse,
                        pollutionList = pollutionresponse
                    };
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.LIST.GetDescription();
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Get PollenList
        //[Authorize]
        [HttpGet]
        [Route("pollenlist")]
        public IActionResult GetPollenList()
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var data = _context.PollenMessage.OrderBy(x => x.Id).LastOrDefault();
                if (data == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.POLLENNOTEXISTS.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.POLLENLIST.GetDescription();
                    var output = new
                    {
                        adviceName = "Pollen",
                        id = data.Id,
                        message = data.Message,
                        fileName = data.FileName,
                        uniqueFileName = data.UniqueFileName,
                        isFile = data.IsFile,
                        createdDate = data.CreatedDate,
                        updatedDate = data.UpdatedDate
                    };
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Get PollutionList
        //[Authorize]
        [HttpGet]
        [Route("pollutionlist")]
        public IActionResult GetPollutionList()
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();

            try
            {
                var data = _context.PollutionMessage.OrderBy(x => x.Id).LastOrDefault();
                if (data == null)
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.POLLUTIONNOTEXISTS.GetDescription();
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }
                else
                {
                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.POLLUTIONLIST.GetDescription();
                    var output = new
                    {
                        adviceName = "Pollution",
                        id = data.Id,
                        message = data.Message,
                        fileName = data.FileName,
                        uniqueFileName = data.UniqueFileName,
                        isFile = data.IsFile,
                        createdDate = data.CreatedDate,
                        updatedDate = data.UpdatedDate
                    };
                    responseData.Data = output;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }

            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion

        #region Pollen Pollution File Upload
        //[Authorize]
        [HttpPost]
        [Route("pollenpollutionfileupload")]
        public async Task<IActionResult> PdfFileUpload([FromForm] PollenPollutionFileUploadInfo model)
        {
            IActionResult result;
            ResponseData responseData = new ResponseData();
            try
            {
                AzureBlobHelper blobStorage = new AzureBlobHelper(_azureBlobConnection.ConnectionString);
                string containerName = "pdf-file";
                var filename = model.File.FileName;
                string fileType = filename.Substring(filename.Length - 4);
                //Database existing check code here
                if(model.Name.ToLower() == "Pollen".ToLower() && _extentions.Contains(fileType))
                {
                    var filenameinfo = await _context.PollenMessage.OrderByDescending(u => u.Id).FirstOrDefaultAsync();
                    if (filenameinfo.UniqueFileName != "" && filenameinfo.UniqueFileName != null)
                    {
                        //Delete file from Azure function call                    
                        var response = await blobStorage.DeleteFileAsync(filenameinfo.UniqueFileName, containerName);
                    }
                    var profilePicGuidName = string.Empty;
                    var filePath = string.Empty;
                    profilePicGuidName = Guid.NewGuid().ToString() + model.FileName;
                    var noteStream = model.File.OpenReadStream();
                    filePath = await blobStorage.UploadFileAsBlob(noteStream, profilePicGuidName, containerName,model.File.ContentType);

                    filenameinfo.FileName = model.FileName;
                    filenameinfo.UniqueFileName = profilePicGuidName;
                    filenameinfo.Message = filePath;
                    filenameinfo.IsFile = true;
                    _context.Update(filenameinfo);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.FILEUPLOAD.GetDescription();
                    responseData.Data = null;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else if(model.Name.ToLower() == "Pollution".ToLower() && _extentions.Contains(fileType))
                {
                    var filenameinfo = await _context.PollutionMessage.OrderByDescending(u => u.Id).FirstOrDefaultAsync();
                    if (filenameinfo.UniqueFileName != "" && filenameinfo.UniqueFileName != null)
                    {
                        //Delete file from Azure function call                    
                        var response = await blobStorage.DeleteFileAsync(filenameinfo.FileName, containerName);
                    }
                    var profilePicGuidName = string.Empty;
                    var filePath = string.Empty;
                    profilePicGuidName = Guid.NewGuid().ToString() + model.FileName;
                    var noteStream = model.File.OpenReadStream();
                    filePath = await blobStorage.UploadFileAsBlob(noteStream, profilePicGuidName, containerName,model.File.ContentType);

                    filenameinfo.FileName = model.FileName;
                    filenameinfo.UniqueFileName = profilePicGuidName;
                    filenameinfo.Message = filePath;
                    filenameinfo.IsFile = true;
                    _context.Update(filenameinfo);
                    _context.SaveChanges();

                    responseData.Status = true;
                    responseData.Message = ResponseMessageHelper.FILEUPLOAD.GetDescription();
                    responseData.Data = null;
                    result = StatusCode((int)HttpStatusCode.OK, responseData);
                }
                else
                {
                    responseData.Status = false;
                    responseData.Message = ResponseMessageHelper.FILENOTUPLOAD.GetDescription();
                    responseData.Data = null;
                    result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
                }                
            }
            catch (Exception)
            {
                responseData.Status = false;
                responseData.Message = ResponseMessageHelper.MISTAKE.GetDescription();
                result = StatusCode((int)HttpStatusCode.BadRequest, responseData);
            }
            return result;
        }
        #endregion
    }
}
