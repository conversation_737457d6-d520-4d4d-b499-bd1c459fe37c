﻿using Microsoft.EntityFrameworkCore.Migrations;

namespace Oberon.BusinessLogic.Migrations
{
    public partial class AlterFeedbackTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "IsAsthAttack",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "<PERSON><PERSON><PERSON>",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsCryInge<PERSON>",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsDifficultyBreathing",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsItch",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsRunnyNose",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsSneezes",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "<PERSON>Sorethroat",
                table: "UserFeedBack");

            migrationBuilder.DropColumn(
                name: "IsStuffyNose",
                table: "UserFeedBack");

            migrationBuilder.RenameColumn(
                name: "HealthStatus",
                table: "UserFeedBack",
                newName: "Symptoms");

            migrationBuilder.AddColumn<int>(
                name: "Level",
                table: "UserFeedBack",
                type: "integer",
                nullable: false,
                defaultValue: 0);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "Level",
                table: "UserFeedBack");

            migrationBuilder.RenameColumn(
                name: "Symptoms",
                table: "UserFeedBack",
                newName: "HealthStatus");

            migrationBuilder.AddColumn<bool>(
                name: "IsAsthAttack",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsCaugh",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsCryIngeyes",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsDifficultyBreathing",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsItch",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsRunnyNose",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSneezes",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsSorethroat",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);

            migrationBuilder.AddColumn<bool>(
                name: "IsStuffyNose",
                table: "UserFeedBack",
                type: "boolean",
                nullable: false,
                defaultValue: false);
        }
    }
}
