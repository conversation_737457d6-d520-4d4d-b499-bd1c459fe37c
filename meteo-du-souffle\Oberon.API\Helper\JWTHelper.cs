﻿using Microsoft.IdentityModel.Tokens;
using System;
using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Text;

namespace Oberon.API.Helper
{
    public class JWTHelper
    {
        public string GenerateJWTToken(string emailId, string secretKey)
        {
            try
            {
                var keyByteArray = Encoding.UTF8.GetBytes(secretKey);
                var claims = new Claim[]
               {
                new Claim(JwtRegisteredClaimNames.Email, emailId),
                new Claim(JwtRegisteredClaimNames.Nbf, new DateTimeOffset(DateTime.Now).ToUnixTimeSeconds().ToString()),
                new Claim(JwtRegisteredClaimNames.Exp, new DateTimeOffset(DateTime.Now).ToUnixTimeSeconds().ToString()),
               };

                var token = new JwtSecurityToken(
                    new JwtHeader(new SigningCredentials(
                        new SymmetricSecurityKey(keyByteArray),
                                                 SecurityAlgorithms.HmacSha256Signature)),
                    new JwtPayload(claims));

                return new JwtSecurityTokenHandler().WriteToken(token);
            }
            catch (Exception ex)
            {
                throw ex;
            }
        }
    }
}
