﻿using Oberon.BusinessLogic.DBContext;
using Oberon.BusinessLogic.Model;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace Oberon.BusinessLogic.BusinessHelper
{
    public class UserBusinessHelper
    {
        public List<User> ResponseSortHelper(string sortBy, List<User> response)
        {
            switch (sortBy)
            {
                case "id_asc":
                    response = response.OrderBy(x => x.Id).ToList();
                    break;
                case "id_desc":
                    response = response.OrderByDescending(x => x.Id).ToList();
                    break;
                case "firstname_asc":
                    response = response.OrderBy(x => x.Name).ToList();
                    break;
                case "firstname_desc":
                    response = response.OrderByDescending(x => x.Name).ToList();
                    break;
                case "lastname_asc":
                    response = response.OrderBy(x => x.FirstName).ToList();
                    break;
                case "lastname_desc":
                    response = response.OrderByDescending(x => x.FirstName).ToList();
                    break;
                case "email_asc":
                    response = response.OrderBy(x => x.Email).ToList();
                    break;
                case "email_desc":
                    response = response.OrderByDescending(x => x.Email).ToList();
                    break;
                case "yofb_asc":
                    response = response.OrderBy(x => x.YearofBirth).ToList();
                    break;
                case "yofb_desc":
                    response = response.OrderByDescending(x => x.YearofBirth).ToList();
                    break;
                case "gender_asc":
                    response = response.OrderBy(x => x.Gender).ToList();
                    break;
                case "gender_desc":
                    response = response.OrderByDescending(x => x.Gender).ToList();
                    break;
                case "smoker_asc":
                    response = response.OrderBy(x => x.IsSmoking).ToList();
                    break;
                case "smoker_desc":
                    response = response.OrderByDescending(x => x.IsSmoking).ToList();
                    break;
                case "zip_asc":
                    response = response.OrderBy(x => x.ZipCode).ToList();
                    break;
                case "zip_desc":
                    response = response.OrderByDescending(x => x.ZipCode).ToList();
                    break;
                case "resp_asc":
                    response = response.OrderBy(x => x.ResProblemId).ToList();
                    break;
                case "resp_desc":
                    response = response.OrderByDescending(x => x.ResProblemId).ToList();
                    break;
                case "pollen_asc":
                    response = response.OrderBy(x => x.PollenId).ToList();
                    break;
                case "pollen_desc":
                    response = response.OrderByDescending(x => x.PollenId).ToList();
                    break;
                default:
                    response = response.OrderByDescending(x => x.Id).ToList();
                    break;
            }
            return response.ToList();
        }
        public List<Advices> AdviceResponseSortHelper(string sortBy, List<Advices> response)
        {
            switch (sortBy)
            {
                case "zip_asc":
                    response = response.OrderBy(x => x.Zipcode).ToList();
                    break;
                case "zip_desc":
                    response = response.OrderByDescending(x => x.Zipcode).ToList();
                    break;
                case "advice_asc":
                    response = response.OrderBy(x => x.Advice).ToList();
                    break;
                case "advice_desc":
                    response = response.OrderByDescending(x => x.Advice).ToList();
                    break;
                case "advicetype_asc":
                    response = response.OrderBy(x => x.AdviceType).ToList();
                    break;
                case "advicetype_desc":
                    response = response.OrderByDescending(x => x.AdviceType).ToList();
                    break;
                case "history_asc":
                    response = response.OrderBy(x => x.AdviceHistoryDate).ToList();
                    break;
                case "history_desc":
                    response = response.OrderByDescending(x => x.AdviceHistoryDate).ToList();
                    break;
                case "like_asc":
                    response = response.OrderBy(x => x.LikesCount).ToList();
                    break;
                case "like_desc":
                    response = response.OrderByDescending(x => x.LikesCount).ToList();
                    break;
                case "date_asc":
                    response = response.OrderBy(x => x.CreatedDate).ToList();
                    break;
                case "date_desc":
                    response = response.OrderByDescending(x => x.CreatedDate).ToList();
                    break;
                default:
                    response = response.OrderByDescending(x => x.Id).ToList();
                    break;
            }
            return response.ToList();
        }

        public List<PharmacyListResponse> PhamracyResponseSortHelper(string sortBy, List<PharmacyListResponse> response)
        {
            switch (sortBy)
            {
                case "zip_asc":
                    response = response.OrderBy(x => x.ZipCode).ToList();
                    break;
                case "zip_desc":
                    response = response.OrderByDescending(x => x.ZipCode).ToList();
                    break;
                case "name_asc":
                    response = response.OrderBy(x => x.Name).ToList();
                    break;
                case "name_desc":
                    response = response.OrderByDescending(x => x.Name).ToList();
                    break;
                case "location_asc":
                    response = response.OrderBy(x => x.Location).ToList();
                    break;
                case "location_desc":
                    response = response.OrderByDescending(x => x.Location).ToList();
                    break;
                case "date_asc":
                    response = response.OrderBy(x => x.dateofVisit).ToList();
                    break;
                case "date_desc":
                    response = response.OrderByDescending(x => x.dateofVisit).ToList();
                    break;
                case "usercount_asc":
                    response = response.OrderBy(x => x.UserCount).ToList();
                    break;
                case "usercount_desc":
                    response = response.OrderByDescending(x => x.UserCount).ToList();
                    break;
                default:
                    response = response.OrderByDescending(x => x.Name).ToList();
                    break;
            }
            return response.ToList();
        }

    }
}
